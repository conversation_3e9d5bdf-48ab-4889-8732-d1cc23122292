{"name": "base", "version": "1.0.0", "description": "Craft KB Base Install", "private": true, "main": "index.js", "scripts": {"fractal": "fractal start --sync", "build": "vite build --config vite/vite.config.mts && vite build --config vite/vite.config.styleguide.mts", "build:watch": "vite build --config vite/vite.config.mts --watch", "dev": "npx tsx vite/startDevServer.mts", "dev:watch": "npx tsx vite/startDevServer.mts --watch", "preview": "vite preview --config vite/vite.config.mts"}, "author": "Karmabu<PERSON>", "license": "MIT", "browserslist": [">0.2%", "last 2 versions", "Firefox ESR", "not dead"], "dependencies": {"@frctl/twig": "^1.2.13", "@splidejs/splide": "^4.1.4", "@splidejs/splide-extension-intersection": "^0.2.0", "autoprefixer": "^9.8.5", "core-js": "^3.6.5"}, "devDependencies": {"@frctl/fractal": "^1.5.15", "@karmabunny/craft-twig": "^2.0.0", "@types/jquery": "^3.5.32", "dotenv": "^16.4.5", "node-notifier": "^9.0.1", "sass": "^1.71.0", "tsx": "^4.7.1", "vite": "^5.2.10", "vite-plugin-live-reload": "^3.0.3", "vite-plugin-static-copy": "^1.0.2"}}