// jQuery + Magnific both included via {% js %}
declare global {
    interface JQuery {
        magnificPopup(options?: any): JQuery;
    }
}

/**
 * Custom element for a Magnific popup. Can be nested inside a <splide-slider/> custom element
 *
 * @data data-selector         [Required] CSS selector for the container element (e.g. ".js--magnific-gallery")
 * @data data-magnific-options [Optional] JSON string of Magnific options to override defaults
 *
 * @example
 * <magnific-popup data-selector=".js--magnific-gallery"></magnific-popup>
 */
export class MagnificPopup extends HTMLElement {
    connectedCallback() {
        const imagesSelector = this.dataset.selector;

        if (!imagesSelector) {
            console.error("[Magnific-Popup Element] No images selector provided", this);
            return;
        };

        const images = this.querySelectorAll(imagesSelector);
        if (!images.length) {
            console.error("[Magnific-Popup Element] No images found", this);
            return;
        };

        if (typeof jQuery === 'undefined' || !jQuery().magnificPopup) {
            console.error("[Magnific-Popup Element] jQuery or MagnificPopup not found", this);
            return;
        };

        // --- MAGNIFIC OPTIONS ---
        let magnificOptions = {
            type: 'image',
            gallery: { enabled: true },
            image: {
                tError: '<a href="%url%">The image #%curr%</a> could not be loaded.',
                titleSrc: 'title',
            },
        };

        if (this.dataset.magnificOptions) {
            try {
                magnificOptions = JSON.parse(this.dataset.magnificOptions);
            } catch (e) {
                console.warn('[Magnific-Popup Element] Invalid JSON in data-magnific-options, falling back to default options', this);
            }
        }

        jQuery(images).magnificPopup(magnificOptions);
    }
}
