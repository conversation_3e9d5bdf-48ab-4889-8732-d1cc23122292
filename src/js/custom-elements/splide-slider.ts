// Splide included via {% js %}
declare global {
    const Splide: (typeof import("@splidejs/splide"))["Splide"];
}

/**
 * Custom element for a Splide slider.
 *
 * @data data-selector          [Optional] CSS selector for the slider element (e.g. ".js--splide-slider"). By default, the slider will be the element itself.
 * @data data-splide-options    [Optional] JSON string of Splide options to override defaults
 * @data data-index-class       [Optional] CSS selector for element to update with current slide index (defaults to ".js--current-index")
 *
 * @example
 * <splide-slider data-selector=".js--splide-slider"></splide-slider>
 */
export class SplideSlider extends HTMLElement {
    connectedCallback() {
        const sliderSelector = this.dataset.selector;
        const slider = sliderSelector
            ? this.querySelector(sliderSelector) as HTMLElement
            : this;

        if (sliderSelector && !slider) {
            console.error(`[Splide-Slider Element] No slider found via provided selector: ${sliderSelector}`, this);
            return;
        }

        // Required for track initialisation
        slider.classList.add("splide");

        // --- SPLIDE OPTIONS ---
        let splideOptions = {
            type: "loop",
            pagination: false,
            arrows: true,
        };

        if (this.dataset.splideOptions) {
            try {
                splideOptions = JSON.parse(this.dataset.splideOptions);
            } catch (e) {
                console.warn("[Splide-Slider Element] Invalid JSON in data-splide-options, falling back to default options", this);
            }
        }

        const splide = new Splide(slider, splideOptions);
        splide.mount();

        // --- INDEX UPDATE ---
        // Used in repos like hamp, swell, disc-ws.
        const indexClass = this.dataset.indexClass || ".js--current-index";

        const updateIndex = (index: number) => {
            const currentIndex = this.querySelector(indexClass);
            if (currentIndex) {
                currentIndex.textContent = (index + 1).toString();
            }
        };

        splide.on("move", updateIndex);
    }
}
