import { SplideSlider } from './custom-elements/splide-slider';
import { MagnificPopup } from './custom-elements/magnific-popup';

(function() {
    let htmlDoc = document.documentElement;
    htmlDoc.classList.remove('no-js');
    htmlDoc.classList.add('js');
})();

/* jquery */
$(document).ready(function() {

    /* --------------------------------------
       set overaching variables
    -------------------------------------- */
    var	$window = $(window),
		$body = $('body');

    /* --------------------------------------
    MagnificPopup links

    NOTE: This can now be largely handled by the custom magnific-popup element.
    You should prefer using that over these classes.
    ---------------------------------------*/
    if(jQuery().magnificPopup) {
        $('a.js--popup-page').magnificPopup({
            type: 'ajax',
        });

        $('a.js--popup-inline').magnificPopup({
            type: 'inline',
            mainClass: 'mfp-fade inline-popup'
        });

        $('a.js--popup-image').magnificPopup({
            type: 'image',
        });

        $('.js-mfp-close').click(function() {
            $.magnificPopup.close();
        });

        $('.js--popup-gallery').magnificPopup({
            type: 'image',
            gallery: {
                enabled: true
            },
        });
    }


    /* --------------------------------------
       Archive slide toggles
    ---------------------------------------*/
    if($('.js--archive-toggle').length > 0) {

        // initiate the slide toggle
        $('.js--archive-toggle').click(function() {
            console.log( $(this) );
            var $this = $( this );
            if ($this.attr('aria-pressed') == 'false') {
                $this.attr('aria-pressed', true).parent().find('[aria-expanded]').first().attr('aria-expanded', true);
            } else {
                $this.attr('aria-pressed', false).parent().find('[aria-expanded]').first().attr('aria-expanded', false);
            }
        });
    };


    /* --------------------------------------
        Select placeholder effect
    ---------------------------------------*/
    $("option.dropdown-top:selected").closest(".field-element").addClass("field-element--dropdown--placeholder");

    $("option.dropdown-top").closest("select").change(function(){
        var placeholderSelected = $("option:selected", this).hasClass("dropdown-top");
        if(!placeholderSelected) {
            $(this).closest(".field-element").removeClass("field-element--dropdown--placeholder");
        } else if(placeholderSelected){
            $(this).closest(".field-element").addClass("field-element--dropdown--placeholder");
        }
    });

    /**
     * Function for smooth scrolling to a given elements
     *
     * @param Object elem A valid jquery selector
     * @param int timeScroll The time in ms to spend scrolling
     */
     function scrollToElement(elem, timeScroll, offsetAdjust) {
        var offSet;

        if (typeof elem === 'undefined' || !elem) {
            offSet = 0;
        } else {
            offSet = elem.offset().top;
        }

        if (typeof offsetAdjust != 'undefined' && offsetAdjust != 0) {
            offSet = parseInt(offSet) + parseInt(offsetAdjust);
        } else {
            // offset the fixed header height
            offSet = parseInt(offSet) - $('#header').height();
        }

        if (typeof timeScroll === 'undefined' || !timeScroll) timeScroll = 1000;

        $('body,html').animate({
            scrollTop: offSet
        }, timeScroll, function () {
            elem.addClass('active-target')
            setTimeout(function () {
                elem.removeClass('active-target')
            }, 2100);
        });

        return false;
    }

    $('body').on('click', '.js--scroll-to', function(e) {
        e.preventDefault();
        var target = $(this).attr('data-target');
        var $elem = $(target);
        var offsetAdj = $(this).attr('data-offset-adj');
        scrollToElement($elem, 500, offsetAdj);
    });

});

/* Vanilla js */
!function(ready) {
    if (document.readyState === 'complete') setTimeout(ready);
    else document.addEventListener('DOMContentLoaded', ready);
}(function() {

    /* --------------------------------------
    Toggle Expandos
    ---------------------------------------*/
    function toggleExpando(e) {
        let $expander = e.target;
        if ($expander.classList.contains('expando-trigger') == false) {
            $expander = $expander.closest('.expando-trigger');
        }
        let $expando = $expander.nextElementSibling;
        if ($expander.getAttribute('aria-pressed') == 'false') {
            $expander.setAttribute('aria-pressed', true);
            $expando.setAttribute('aria-expanded', true);
        } else {
            $expander.setAttribute('aria-pressed', false);
            $expando.setAttribute('aria-expanded', false);
        }
    }

    /* --------------------------------------
    Initialise Expandos
    ---------------------------------------*/
    function initialiseExpando(elem) {
        let
            $this = elem,
            $expandoTrigger = null;

        if(!(['H2', 'H3', 'H4'].includes($this.previousElementSibling.tagName))) {
            let button = document.createElement("button");
            button.innerHTML = "More information";
            $this.before(button);
        }

        $expandoTrigger = $this.previousElementSibling;

        if(!!$expandoTrigger) {
            if(!$expandoTrigger.classList.contains('expando-trigger')) {
                let icon = document.createElement("span");
                icon.classList.add('expando-open__icon');
                $expandoTrigger.append(icon);

                $expandoTrigger.classList.add('expando-trigger');
                $expandoTrigger.setAttribute('aria-role', 'button');
                $expandoTrigger.setAttribute('aria-pressed', false);
            }

            $this.setAttribute('aria-expanded', false);

            $expandoTrigger.addEventListener("click", toggleExpando, false);
        }
    }

    document.querySelectorAll('.expando').forEach(expando => {
        initialiseExpando(expando);
    });


    /* -------------------------------
		Responsive tables
	---------------------------------- */
    document.querySelectorAll('.table--responsive').forEach($responsiveTable => {
        if(window.innerWidth < 560) {
            setResponsiveTables($responsiveTable);
        }
    });

    function setResponsiveTables(table) {
        let $table = table;
        let headings = [];
        let $headingRow = $table.querySelector("thead tr");
        $bodyRows = [...$table.querySelectorAll("tbody tr")];

        // If table is not properly formatted, assume first row is the thead
        if(!$headingRow) {
            $headingRow = $bodyRows.shift();
            $headingRow.classList.add("table--responsive__first-row");
        }

        $headingRow.querySelectorAll("th").forEach($th => {
            console.log('$th.textContent', $th.textContent);
            headings.push($th.textContent);
        });

        $bodyRows.forEach($row => {
            $row.querySelectorAll("td").forEach(function ($td, i) {
                $row.setAttribute("data-title", headings[i]);

                $wrap = document.createElement('div');

                var elem = $td.appendChild($wrap).setAttribute('class', 'table--responsive__content');

                while ($td.firstChild !== $wrap) {
                    $wrap.appendChild($td.firstChild);
                }
            });
        });
    };


    /* -------------------------------
        Magic anchor hacks
    ---------------------------------- */
    const COPY_FIELDS = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];

    document.querySelectorAll('.js--magic-anchors').forEach($container => {
        const $anchor = $container.querySelector('a');
        if (!$anchor) return;

        $container.style.cursor = 'pointer';
        $container.tabIndex = 0;
        $container.setAttribute('role', 'link');

        if (!$container.hasAttribute('aria-label')) {
            const label = $anchor.getAttribute('aria-label') || $anchor.title || $anchor.innerText || $anchor.textContent;
            $container.setAttribute('aria-label', label);
        }

        $container.addEventListener('click', event => {
            const options = { bubbles: false };

            for (const key of COPY_FIELDS) {
                options[key] = event[key];
            }

            $anchor.dispatchEvent(new MouseEvent('click', options));
        });
    });
});

/* --------------------------------------
Resize observer for vw units that account for vertical scrollbars
See: https://www.smashingmagazine.com/2023/12/new-css-viewport-units-not-solve-classic-scrollbar-problem/
---------------------------------------*/
const resizeObserver = new ResizeObserver(() => {
    const viewportWidth = document.documentElement.clientWidth / 100;
    document.documentElement.style.setProperty('--vw', `${viewportWidth}px`);
});

resizeObserver.observe(document.documentElement);

/* -------------------------------
    Custom elements
---------------------------------- */
customElements.define('splide-slider', SplideSlider);
customElements.define('magnific-popup', MagnificPopup);
