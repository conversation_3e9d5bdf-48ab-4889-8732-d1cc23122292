/.env
/.idea
/vendor
.DS_Store

.directory
*.key
!/config/*.key

# Ignore npm install
/node_modules

# Ignore uploaded files
/web/files

# Ignore compiled css files
/web/css/base.css
/web/css/components.css
/web/css/posts.css
/web/css/magnific-popup.css
/web/css/normalize.css
/web/css/splide-core.min.css
/web/css/splide.css
/web/css/*.map
/fractal/_patterns/styleguide.css
/fractal/_patterns/*.map

# Ignore 3rd party assets
/web/dist/*
!/web/dist/.keep

# Ignore temporary Vite-related files
/vite/server.config.json
**/.vite/
