columnSuffix: null
contentColumnType: string
handle: teamMembers
instructions: null
name: 'Team Members'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  localizeRelations: false
  maintainHierarchy: false
  maxRelations: null
  minRelations: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
  selectionLabel: null
  showSiteMenu: false
  sources:
    - 'section:fcb6fd6a-e9fd-4794-a829-8a2e7cf8993c' # Team Members
  targetSiteId: null
  validateRelatedElements: false
  viewMode: null
translationKeyFormat: null
translationMethod: site
type: craft\fields\Entries
