columnSuffix: null
handle: parentPage
instructions: null
name: 'Parent Page'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
      -
        - conditionRules
        -
          -
            __assoc__:
              -
                - class
                - craft\elements\conditions\HasUrlConditionRule
              -
                - uid
                - 72711cf9-1333-45e2-8d2d-176493dbd073
              -
                - value
                - true
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: true
  sources:
    - 'section:8e022768-bf63-43c1-ad96-2f193cd7f337' # Pages
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
