columnSuffix: null
handle: linkField
instructions: null
name: Link
searchable: false
settings:
  advancedFields:
    - target
  fullGraphqlData: false
  maxLength: 255
  showLabelField: true
  typeSettings:
    __assoc__:
      -
        - asset
        -
          __assoc__:
            -
              - sources
              - '*'
            -
              - allowedKinds
              - '*'
            -
              - showUnpermittedVolumes
              - ''
            -
              - showUnpermittedFiles
              - ''
      -
        - entry
        -
          __assoc__:
            -
              - sources
              - '*'
      -
        - url
        -
          __assoc__:
            -
              - allowRootRelativeUrls
              - '1'
            -
              - allowAnchors
              - '1'
  types:
    - entry
    - url
    - asset
    - email
    - tel
translationKeyFormat: null
translationMethod: none
type: craft\fields\Link
