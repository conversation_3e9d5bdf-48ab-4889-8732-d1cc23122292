columnSuffix: null
handle: postCategory
instructions: null
name: 'Post Category'
searchable: true
settings:
  allowSelfRelations: false
  branchLimit: null
  maintainHierarchy: false
  maxRelations: null
  minRelations: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
      -
        - conditionRules
        -
          -
            __assoc__:
              -
                - class
                - craft\elements\conditions\entries\TypeConditionRule
              -
                - uid
                - 2a6efed3-58ac-4cc5-8838-cdae9622a3a2
              -
                - operator
                - in
              -
                - values
                -
                  - 6e6943d0-412b-41f5-9cec-dcfddb2a6c8a # Post Category
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: true
  sources:
    - 'section:c345d6df-715a-475d-b0b3-f48a8d61099d' # Categories
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
