columnSuffix: null
handle: canonicalUrl
instructions: 'Tell search engines the original source of duplicate content to index instead of this entry.'
name: 'Canonical Url'
searchable: false
settings:
  fullGraphqlData: false
  maxLength: 255
  showLabelField: false
  typeSettings:
    __assoc__:
      -
        - entry
        -
          __assoc__:
            -
              - sources
              -
                - singles
                - 'section:8e022768-bf63-43c1-ad96-2f193cd7f337' # Pages
                - 'section:5b37ab64-dc24-4a5e-b54e-7d4452e0e96b' # Posts
                - 'section:fcb6fd6a-e9fd-4794-a829-8a2e7cf8993c' # Team Members
      -
        - url
        -
          __assoc__:
            -
              - allowRootRelativeUrls
              - ''
            -
              - allowAnchors
              - ''
  types:
    - entry
    - url
translationKeyFormat: null
translationMethod: none
type: craft\fields\Link
