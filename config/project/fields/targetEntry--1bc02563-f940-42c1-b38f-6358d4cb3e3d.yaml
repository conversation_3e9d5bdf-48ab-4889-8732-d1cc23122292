columnSuffix: null
handle: targetEntry
instructions: null
name: 'Target Entry'
searchable: false
settings:
  allowSelfRelations: false
  branchLimit: null
  maintainHierarchy: false
  maxRelations: 1
  minRelations: 1
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Entry
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\entries\EntryCondition
      -
        - conditionRules
        -
          -
            __assoc__:
              -
                - class
                - craft\elements\conditions\HasUrlConditionRule
              -
                - uid
                - 469aaca6-b363-43d9-9319-109e10c11d07
              -
                - value
                - true
  selectionLabel: null
  showCardsInGrid: false
  showSiteMenu: true
  sources: '*'
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: none
type: craft\fields\Entries
