css: "/* .ck stops the class bleeding out the editor */\r\n.ck .highlight {\r\n    position: relative;\r\n    left: 0; top: 0;\r\n    padding: 5px 15px;\r\n    margin: 5px 0;\r\n    border: 1px #ccc dashed;\r\n}\r\n\r\n.ck .highlight:before {\r\n    position: absolute;\r\n    right: 0; top: 0;\r\n    padding: 0 6px;\r\n    background: #eee;\r\n    color: #666;\r\n    font-size: 9px;\r\n    text-transform: uppercase;\r\n}\r\n\r\n.ck .highlight:before {\r\n    content: \"Highlight\";\r\n}\r\n.button {\r\n    text-decoration: none;\r\n    padding: 8px 16px;\r\n    text-align: center;\r\n    display: inline-grid;\r\n    grid-auto-flow: column;\r\n    column-gap: 0.5em;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 4px;\r\n    position: relative;\r\n    margin: 0;\r\n    color: #ffffff;\r\n    background-color: #666;\r\n}"
headingLevels:
  - 2
  - 3
  - 4
  - 5
  - 6
name: Default
options:
  alignment:
    options:
      - left
      - center
      - right
  code:
    indentSequence: '  '
  htmlSupport:
    allow:
      -
        attributes: 'true'
        classes: 'true'
        name: div
  image:
    toolbar:
      - transformImage
      - 'imageStyle:inline'
      - 'imageStyle:block'
      - 'imageStyle:side'
      - 'imageStyle:wrapText'
      - 'imageStyle:breakText'
      - '|'
      - toggleImageCaption
      - imageTextAlternative
  link:
    decorators:
      openInNewTab:
        attributes:
          rel: 'noopener noreferrer'
          target: _blank
        label: 'Open in a new tab'
        mode: manual
  style:
    definitions:
      -
        classes:
          - button
        element: a
        name: Button
      -
        classes:
          - highlight
        element: div
        name: Highlight
toolbar:
  - heading
  - '|'
  - style
  - '|'
  - blockQuote
  - bold
  - italic
  - insertTable
  - link
  - anchor
  - bulletedList
  - numberedList
  - alignment
  - insertImage
  - mediaEmbed
  - createEntry
  - findAndReplace
  - undo
  - '|'
  - sourceEditing
