color: emerald
fieldLayouts:
  d0f0f833-260a-4e71-adea-13ff41b0a742:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-02-04T07:02:53+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: a9ed9c70-5ba3-4f6c-a05e-3476bcf5494f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-04T07:02:53+00:00'
            elementCondition: null
            fieldUid: 8a34a59c-d941-4815-9cbf-8d68ad54dfb8 # System Icon
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 04501379-8d5e-4fb5-808b-f394f292ae32
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-02-04T07:02:53+00:00'
            elementCondition: null
            fieldUid: fcc8d59a-9f6a-4005-90b4-00cb1d3e4294 # Alert Theme
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6f004a11-0481-4fc8-a233-36a9273f59fd
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-02-04T07:02:53+00:00'
            elementCondition: null
            fieldUid: dd77fc00-9cf0-4396-b3c2-ecf8c1b4cc93 # Alert Show
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6c7f24f9-4941-4803-8d8e-6259682fdc68
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-02-04T07:02:53+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: dd77fc00-9cf0-4396-b3c2-ecf8c1b4cc93 # Alert Show
                  uid: 2f8859c3-48ca-4e1d-94dc-a1f956cc38f3
                  value: true
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: d7352d39-aba5-4bc8-8168-2e7ade584eec # Wait to show again
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 76f56e5f-0593-4b0d-bc27-3538bec24f32
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-02-04T07:02:53+00:00'
            elementCondition: null
            fieldUid: cc9d5844-52b4-42df-b505-9f210c3902c6 # Alert Message
            handle: null
            includeInCards: false
            instructions: null
            label: Message
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0ac6deb2-0e2a-4bf9-a5d2-d538b65b8661
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: e1902313-9671-4c1a-b8c9-d3baf3f73980
        userCondition: null
handle: ribbonAlert
hasTitleField: true
icon: triangle-exclamation
name: 'Ribbon Alert'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
