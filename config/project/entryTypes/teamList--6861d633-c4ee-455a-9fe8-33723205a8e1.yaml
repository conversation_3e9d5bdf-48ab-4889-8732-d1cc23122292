color: cyan
fieldLayouts:
  c3ee666c-fa1a-4b05-8996-3e1bea185a6b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T01:36:53+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Admin Title'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 066a8886-0c95-421f-8703-f93335373627
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:23:23+00:00'
            dismissible: false
            elementCondition: null
            style: tip
            tip: 'Leave the Team Members field below empty to show all'
            type: craft\fieldlayoutelements\Tip
            uid: d9cc8736-7067-41dd-80aa-160dfc5b3771
            userCondition: null
          -
            dateAdded: '2024-12-27T01:23:23+00:00'
            elementCondition: null
            fieldUid: 79a6132f-d52b-48c7-a0be-2d245f7e4b37 # Team Members
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bdff6b60-f74b-4bf4-a3bf-70839340d2fe
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:23:23+00:00'
            elementCondition: null
            fieldUid: 05ea72d0-1cc0-4343-bb3b-049be6b22dab # Full Profile Page Links
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c71def73-7c3e-4276-a68a-07d947728053
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 6ccadea6-43cf-4e0f-850f-8628dd87dacf
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T01:25:13+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 652d500d-5d7f-4f58-851a-4d5f23f2a9db
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:25:13+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 75522898-2f73-43fe-99a5-7720b64a37ae
            userCondition: null
            warning: null
            width: 100
        name: 'Header + Footer'
        uid: c927e9a7-2dbe-4894-9e61-e37baf416df0
        userCondition: null
handle: teamList
hasTitleField: false
icon: plus
name: 'Team List'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Team List: { itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
