color: cyan
fieldLayouts:
  e117d9f3-3a02-4f77-be02-bece74286d86:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T00:53:16+00:00'
            elementCondition: null
            fieldUid: cd827c21-5686-4d89-bb58-7e9bd547703a # Content Source
            handle: contentSource
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1e45b855-68bd-4460-8e45-45abfb5ccb44
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-27T01:34:55+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Custom Admin Title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9e15ef6a-d3f4-4113-a2b8-7f0c6cb4fe93
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-27T00:53:16+00:00'
            elementCondition: null
            fieldUid: aaca8b2a-d03c-494a-a029-66f1758ac4d7 # Columns Abreast
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b94ea275-daad-417e-b4ba-3a95adc5dead
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-12-27T01:18:14+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\LineBreak
            uid: 3c7e918f-6a06-49cb-b343-cf8ae43933d3
            userCondition: null
          -
            dateAdded: '2024-12-27T00:53:16+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: cd827c21-5686-4d89-bb58-7e9bd547703a # Content Source
                  layoutElementUid: 1e45b855-68bd-4460-8e45-45abfb5ccb44
                  operator: in
                  uid: c1f682dd-3976-4b83-8496-cd5428b3f35a
                  values:
                    - customSetPageChildren
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: f2ebb14a-0569-49a5-b05f-fbe18f891850 # Parent Page
            handle: pagesGalleryParentPage
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 019b7e5d-0b8b-488f-8696-b37ebc707d57
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T00:53:16+00:00'
            elementCondition: null
            fieldUid: f34874a3-c925-46da-a4cf-2a26d4ceb67d # Additional pages
            handle: null
            includeInCards: false
            instructions: null
            label: 'Additional / Custom Pages'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0a6b3f12-75d3-4802-86a4-e19855b007fc
            userCondition: null
            warning: null
            width: 50
        name: Content
        uid: 9d602e1b-8bb7-42bc-acaa-8f462fab830d
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T00:58:07+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 89432ab5-73a6-4015-a8be-c12b397aa3c9
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:18:14+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bb7bf9dd-4523-48ee-a396-858c1c7416c7
            userCondition: null
            warning: null
            width: 100
        name: 'Header & Footer'
        uid: 301d6a85-b044-4150-bd6d-6039d6f48962
        userCondition: null
handle: pageGallery
hasTitleField: false
icon: grid
name: 'Page Gallery'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{ contentSource.label }{{ itemTitle ? '': '' ~ itemTitle : pagesGalleryParentPage.one() ?? false ? '': '' ~ pagesGalleryParentPage.one().title }}'
titleTranslationKeyFormat: null
titleTranslationMethod: site
