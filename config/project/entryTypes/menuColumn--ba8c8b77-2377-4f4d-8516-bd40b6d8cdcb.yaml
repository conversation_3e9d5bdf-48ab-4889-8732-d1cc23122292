color: blue
fieldLayouts:
  70632790-b941-4e7a-b53c-2ab7b3053e42:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-07T02:14:31+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: eb35186e-826c-436b-8d16-3f3282f5212b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-07T02:20:38+00:00'
            elementCondition: null
            fieldUid: 133aa064-7652-474e-8710-0408b3ed06be # Menu Items
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 257e5108-fa23-4442-80d3-5539ddc68207
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: b3a3780c-ddad-4741-8fef-bab11611cd6a
        userCondition: null
handle: menuColumn
hasTitleField: false
icon: bars
name: 'Menu Column'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Menu Column: { itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
