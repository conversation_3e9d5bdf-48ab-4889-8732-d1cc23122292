color: blue
fieldLayouts:
  9f3c2b95-e28d-4edc-97c6-58a767620240:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-07T04:03:53+00:00'
            elementCondition: null
            fieldUid: 507b3a27-edf1-40a7-9dc7-e6d81b09c69b # Menu Type
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 84277148-30fc-41df-9043-ef942a5a3bb6
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-07T04:03:53+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 507b3a27-edf1-40a7-9dc7-e6d81b09c69b # Menu Type
                  layoutElementUid: 84277148-30fc-41df-9043-ef942a5a3bb6
                  operator: ni
                  uid: 1a3828a4-87ac-494c-81ac-acd6c9150646
                  values:
                    - fullScreen
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: c3b48a17-c84b-4d49-8fd0-3f2527786254 # Maximum Menu Depth
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6d5a57b2-539d-463a-ab52-82c4841b1ace
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-07T04:58:42+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 507b3a27-edf1-40a7-9dc7-e6d81b09c69b # Menu Type
                  layoutElementUid: 84277148-30fc-41df-9043-ef942a5a3bb6
                  operator: in
                  uid: 93a32eb0-2e62-420e-adf5-7ca4838a43f0
                  values:
                    - dropdowns
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 9bd6d0d0-994c-489e-aec7-b0321188927b # Desktop Sub Navigation Toggle Trigger
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1665137a-ee26-49ab-ada3-d882a382c611
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 81585f43-9f44-4a47-898e-7c827fd99a39
        userCondition: null
handle: navigationConfiguration
hasTitleField: false
icon: bars
name: 'Navigation Configuration'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Navigation Configuration'
titleTranslationKeyFormat: null
titleTranslationMethod: site
