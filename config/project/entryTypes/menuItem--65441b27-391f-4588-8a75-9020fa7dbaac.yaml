color: blue
fieldLayouts:
  70e3a1d7-889e-47bb-afa5-8a0e673a299d:
    cardView:
      - 'layoutElement:9196373b-d516-4e00-9f83-f6e9bdc46567'
      - 'layoutElement:0b320148-a2ad-4d3a-9364-da7cb797315c'
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-02-07T02:18:50+00:00'
            elementCondition: null
            fieldUid: d6f4d67f-350d-4c39-a6d7-56a5f5f001af # Link
            handle: null
            includeInCards: true
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9196373b-d516-4e00-9f83-f6e9bdc46567
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-07T02:18:50+00:00'
            elementCondition: null
            fieldUid: 692981e9-ded2-4931-bee6-40070fe65c66 # Subtitle
            handle: null
            includeInCards: true
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0b320148-a2ad-4d3a-9364-da7cb797315c
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-07T02:18:50+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: d57f9a9f-1fbd-40ab-b874-90d556a3f251
            userCondition: null
          -
            dateAdded: '2025-02-07T02:18:50+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\EmptyFieldConditionRule
                  fieldUid: 5db558bc-3b1b-490f-bed2-5580e0996c12 # Navigation Link Helper
                  layoutElementUid: 27db34ca-2e9d-4007-9c7b-75e37ab1d10b
                  operator: empty
                  uid: 3cb5ca06-5806-4b40-a6a9-3ce867992f80
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 51b67fb2-64e0-4d93-93e0-5d2b4bed91f1 # Show All Children Pages
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 386dae78-680f-488f-b1a2-5418fb5d4f23
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-02-07T02:18:50+00:00'
            elementCondition:
              class: craft\elements\conditions\entries\EntryCondition
              conditionRules:
                -
                  class: craft\fields\conditions\LightswitchFieldConditionRule
                  fieldUid: 51b67fb2-64e0-4d93-93e0-5d2b4bed91f1 # Show All Children Pages
                  layoutElementUid: 386dae78-680f-488f-b1a2-5418fb5d4f23
                  uid: 1851db8c-4fe8-44d7-8ae9-78457c225728
                  value: false
              elementType: craft\elements\Entry
              fieldContext: global
            fieldUid: 5db558bc-3b1b-490f-bed2-5580e0996c12 # Navigation Link Helper
            handle: customSecondaryLinks
            includeInCards: false
            instructions: null
            label: 'Custom Secondary Links'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 27db34ca-2e9d-4007-9c7b-75e37ab1d10b
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: be24235e-a71e-4044-a78a-58d0e1522ab0
        userCondition: null
handle: menuItem
hasTitleField: false
icon: bars
name: 'Menu Item'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: 'Menu Item: { linkField.label }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
