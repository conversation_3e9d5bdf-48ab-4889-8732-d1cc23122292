color: amber
fieldLayouts:
  bf40ea19-69ef-490b-912c-84091f00a2eb:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2024-08-09T01:31:47+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Title
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 3b7c9964-62f6-4c11-b8f5-ff8eb92c2d2f
            userCondition: null
            warning: null
            width: 75
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 04c9645d-ce63-4f71-930f-f0438bd168f3 # Banner
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9027829f-8903-4ca3-b814-f9c6d06d865e
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 89303a1b-8fec-4e93-9b39-9b5d6f2d4f22 # Excerpt
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7e0d8427-8330-4884-8f01-1fb2ef45b12b
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 51c58fb1-fb1f-46df-b9d9-35b429089e14 # Attribution
            handle: null
            includeInCards: false
            instructions: null
            label: 'Override Author'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9e039ab9-f51d-424c-96f9-f67efa0e913d
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: bfb19cb5-8105-4c0d-92e0-70934b26e830 # Post Category
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5d7442ff-b892-4304-af9f-6c5ed6b1ea2e
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: d6b2cba9-666a-4f09-acb1-3033e0314580 # Post Tags
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e2c1e3bf-40c3-4a52-8d38-e0611903ef32
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 0adc5399-0e40-4d46-9443-a85046d2751d # Page Content
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: bf71599e-a312-4d02-a9c7-6454d95a9f25
            userCondition: null
            warning: null
            width: 100
        name: Posts
        uid: 546ca340-e25b-471a-9b84-ba9e7927f4e4
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: ed08dded-d02c-4a15-a218-b5e7b8370a35 # Enable Sidebar
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d354a99e-084d-47da-8fa9-eb31f0bd537d
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 98c2e58b-0203-4aa4-89af-4036d893dfca # Sidebar Addons
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 82bb5641-1a14-49c7-8e69-f497cb94a32c
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: e43d9f86-3721-4231-a956-77872157b77d # Bottom Bar Addons
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1bd79fc3-e66f-4e2e-ae83-8c85928e6a45
            userCondition: null
            warning: null
            width: 100
        name: Layout
        uid: 6d742542-e3ab-4264-a7ba-bc6003fe3626
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 4ed4074e-eb71-49d9-b9a9-05f308dcd29a # Navigation Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Navigation Title'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9bc06c50-809c-49b2-8552-481e084fe25b
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 2f4881f2-619d-484d-8bae-035941bc9724 # Navigation Visibility
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: *************-468a-8439-baa80f3353d7
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            heading: Meta
            type: craft\fieldlayoutelements\Heading
            uid: 42560c83-0362-485c-b04e-717d50fa3d78
            userCondition: null
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: afb06c07-eee2-4c2f-a623-7c5976c82755 # Meta Title
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1559193d-4965-44e0-9c02-2b87f09fa65e
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: f85a92fb-60aa-4278-84f7-0e9c0d7765f2 # Meta Description
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: cea7017f-c60d-48fd-878b-8166c26fe550
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:31:47+00:00'
            elementCondition: null
            fieldUid: 771d90ed-3e58-4e4b-8340-54e0af52cc39 # Meta Keywords
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d5144d0a-e2d2-49c8-8753-2bcc3acbf713
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-08-09T01:33:01+00:00'
            elementCondition: null
            fieldUid: 6c842807-a0bf-4772-9fac-7ac0332c105f # Canonical Url
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 691fa3b8-fefe-48fc-bcde-835ebf9025f0
            userCondition: null
            warning: null
            width: 100
        name: Settings
        uid: 1e143c17-8c84-4d97-8f7a-02ff17e3d166
        userCondition: null
handle: post
hasTitleField: true
icon: newspaper
name: Post
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{ itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: ''
