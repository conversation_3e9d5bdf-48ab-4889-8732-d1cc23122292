color: cyan
fieldLayouts:
  fb18d8af-f271-4c56-9cc7-2a6c12c884df:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T01:36:27+00:00'
            elementCondition: null
            fieldUid: 6f255c3b-0320-4611-a933-0be2b9c7f380 # Item Title
            handle: null
            includeInCards: false
            instructions: null
            label: 'Admin Title'
            providesThumbs: false
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 247d7359-73d0-4b3b-a9ba-37b197f3a32d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:21:47+00:00'
            elementCondition: null
            fieldUid: 1b30eb25-6fa1-45f1-b501-20cca5ac9e73 # Card Helper
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 247a37d2-cfc9-41a3-8bea-ffc585b8b71c
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 940794b7-aa97-4d10-b196-a70d72a6d265
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2024-12-27T01:23:52+00:00'
            elementCondition: null
            fieldUid: 4cb26117-09e7-45e0-b133-7fb01223c980 # Section Header
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 90a322bb-5592-4942-af4f-9065ab36497d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2024-12-27T01:23:52+00:00'
            elementCondition: null
            fieldUid: ec3238b0-5e12-4a47-8f6f-7d65e1de859f # Section Footer
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0e513c8d-d8e5-4fda-a432-dbdefbe5023b
            userCondition: null
            warning: null
            width: 100
        name: 'Header + Footer'
        uid: 03cbc3bb-1fb1-431a-aa6c-fb4862098ba4
        userCondition: null
handle: cardSet
hasTitleField: false
icon: plus
name: 'Card set'
showSlugField: false
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: '{ itemTitle }'
titleTranslationKeyFormat: null
titleTranslationMethod: site
