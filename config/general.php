<?php
/**
 * General Configuration
 *
 * All of your system's general configuration settings go in here. You can see a
 * list of the available settings in vendor/craftcms/cms/src/config/GeneralConfig.php.
 *
 * @see \craft\config\GeneralConfig
 */

return [
    // Global settings
    '*' => [
        // Default Week Start Day (0 = Sunday, 1 = Monday...)
        'defaultWeekStartDay' => 1,

        // Whether generated URLs should omit "index.php"
        'omitScriptNameInUrls' => true,

        // Control Panel trigger word
        'cpTrigger' => 'admin',

        // The secure key Craft will use for hashing and encrypting data
        'securityKey' => getenv('SECURITY_KEY'),

        'maxUploadFileSize' => '100mb',

        // Globals are global.
        'preloadSingles' => true,

        // Same site cookie settings (PHP 7.3+)
        // See: https://github.com/craftcms/cms/issues/4462
        'sameSiteCookieValue' => 'Lax',

        // Set up an alias for the file path
        'aliases' => [
            '@webroot' => CRAFT_BASE_PATH . '/web',
            '@web' => getenv('DEFAULT_SITE_URL'),
        ],
    ],

    // Dev environment settings
    'dev' => [
        // No cache!
        'enableTemplateCaching' => false,

        // Dev Mode (see https://craftcms.com/guides/what-dev-mode-does)
        'devMode' => true,
        'allowAdminChanges' => true,
    ],

    // BSTS / Bunnysites environment settings
    'test' => [
        // Dev Mode (see https://craftcms.com/guides/what-dev-mode-does)
        'devMode' => true,
        'allowAdminChanges' => true,
    ],

    // Staging environment settings
    'staging' => [
        'devMode' => true,
        'allowAdminChanges' => false,
    ],

    // Production environment settings
    'production' => [
        'devMode' => false,
        'allowAdminChanges' => false,

        // If not using a systemd queue.
        'runQueueAutomatically' => true,

        // No server tokens.
        'sendPoweredByHeader' => false,

        // Hardcode it (if you don't trust the env).
        'aliases' => [
            // '@web' => getenv('DEFAULT_SITE_URL'),
        ],
    ],
];
