/*
    Custom styles
*/

@use "../../00-settings" as *;
@use "../../05-utilities/helpers/_utility-mixins" as *;


// Custom elements are inline by default, and previously
// we would normally mount to divs - so make the element block display type
// by default.
:where(splide-slider) {
    display: block;
}

// Make sure the banners still load if no js loads
.no-js .splide {
    visibility: inherit !important;
}


// don't know why this isn't part of core but pagination is.
.splide:not(.is-overflow) .splide__arrows {
    display: none;
  }

.splide__arrows {
    position: absolute;
    top: 40%;
    left: 0;
    right: 0;
    z-index: 3;
}

.splide__arrow {
    position: absolute;
    background-color: rgba($color-black, 0);
    border: 0;
    cursor: pointer;
    padding-top: $spacing*2;
    padding-bottom: $spacing*2;
    transition: background $link-transition;
    display: grid;
    border-top-left-radius: $spacing*.5;
    border-bottom-left-radius: $spacing*.5;

    svg {
        @include set-svg-colour($color-white)
    }

    &:hover,
    &:focus,
    &:active {
        background-color: rgba($color-black, .75);
    }

    &--prev {
        left: 0;
        // transform: rotate(180deg);
    }

    &--next {
        right: 0;
    }
}

.splide__pagination {
    @include list-reset;
    gap: $spacing*2;
}

.splide__pagination__page {
    cursor: pointer;
    background-color: $color-grey-03;
    border: 0;
    border-radius: 200px;
    width: $spacing*1.5;
    height: $spacing*1.5;
    transition: background $link-transition;

    &.is-active {
        background-color: $color-primary;
    }

    &:hover,
    &:focus,
    &:active {
        background-color: $color-grey-06
    }
}

