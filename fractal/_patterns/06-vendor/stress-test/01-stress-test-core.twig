{# BROKEN BUILT-IN TAGS #}

    {# STATUS: broken #}
    {# E.g. use in KB project: https://github.com/Karmabunny/gold/blob/a962bd1327c6eb5cdf44b0849393ec685f0d6cd7/templates/events/_layouts/_event-tooltip.twig#L22-L24 #}
    {# {% apply %}
        testing123
    {% endapply %} #}

    {# STATUS: broken #}
    {# E.g. use in KB project: https://github.com/Karmabunny/gold/blob/a962bd1327c6eb5cdf44b0849393ec685f0d6cd7/templates/events/_layouts/_event-tooltip.twig#L22-L24 #}
    {# |raw filter will need to work *correctly* for this tag #}
    {# {% autoescape %}
        Everything will be automatically escaped in this block
        using the HTML strategy
    {% endautoescape %} #}

{# BROKEN BUILT-IN FILTERS  #}
    {# STATUS: broken #}
    {# E.g. use in KB project: https://github.com/Karmabunny/rewa/blob/454eac007e6af57e100174209959c7e914312278/templates/shop/_private/layouts/includes/checkout-progress.twig#L40 #}
    {# {% set items = [{ 'fruit' : 'apple'}, {'fruit' : 'orange' }] %}
    {% set fruits = items|column('fruit') %} #}

    {# STATUS: broken #}
    {# E.g. use in KB project: https://github.com/Karmabunny/gold/blob/a962bd1327c6eb5cdf44b0849393ec685f0d6cd7/templates/deals/_single.twig#L42 }
    {# https://github.com/twigjs/twig.js/pull/874 <-- dirty implementation of it #}
    {# {% set people = [
        {first: "Bob", last: "Smith"},
        {first: "Alice", last: "Dupond"},
    ] %}

    {{ people|map(p => "#{p.first} #{p.last}")|join(', ') }} #}

{# BROKEN BUILT-IN FUNCTIONS #}
    {# STATUS: broken #}
    {# E.g. use in KB project: https://github.com/Karmabunny/aber2/blob/8457518e9de198194685d0ef9f1f79d54ab71077/modules/searchplus/templates/algolia/_browseIndex.twig#L30 #}
    {# {{ constant('RSS', date) }} #}

    {# STATUS: WORKING! #}
    {# {% extends '@stress-test-child' %}

    {% block title %}Index{% endblock %}
    {% block head %}
        {{ parent() }}
        <style type="text/css">
            .important { color: #336699; }
        </style>
    {% endblock %}
    {% block content %}
        <h1>Index</h1>
        <p class="important">
            Welcome on my awesome homepage.
        </p>
    {% endblock %} #}

    {# STATUS: broken #}
    {# E.g. use in KB project (probs a 1 off though): https://github.com/Karmabunny/aber2/blob/8457518e9de198194685d0ef9f1f79d54ab71077/templates/_partials/_my-ak-signup.twig.twig#L17 #}
    {# https://github.com/twigjs/twig.js/issues/392 <-- known issue #}
    {# {{ include(template_from_string("Hello {{ name }}")) }} #}


{# Misc broken stuff (all require extra composer dependencies *and* not used by KB) #}
{# Tags #}
    {# sandbox #}
{# Filters #}
    {# data_uri #}
    {# country_name #}
    {# currency_name #}
    {# currency_symbol #}
    {# format_currency #}
    {# format_number #}
    {# format_date #}
    {# format_datetime #}
    {# html_to_markdown #}
    {# inky_to_html #}
    {# inline_css #}
    {# locale_name #}
    {# markdown_to_html #}
    {# timezone_name #}
    {# u #}
{# Functions #}
    {# html_classes #}
    {# country_timezones #}
    {# country_names #}
    {# currency_names #}
    {# language_names #}
    {# locale_names #}
    {# script_names #}
    {# timezone_names #}


{# Tags that twig.js docs say are broken (i.e. ???) but seemingly are not: #}
    {# |raw - at least it doesn't break compilation #}
    {# attribute() - seems to work #}
    {# block() - doesn't throw an error #}
    {# dump() - works! #}
    {# max() - works! #}
    {# min() - works! #}
    {# extends - no idea and I can't see the path that Fractal is using saying it is not a valid file (Twig.Template.prototype.importFile = function (file) {) <-- needs more discovery but theoretically twig.js supports it - may be a Fractal problem #}
    {# random - works! #}
    {# range - works! #}
    {# source - doesn't break compilation but we do we 'Template "x" is not defined' - maybe related to extends #}