/*
    Load MP core styles
*/

@use "magnific-popup-core" as *;


/*
    Our custom styles
*/

@use "../../00-settings" as *;



// Custom elements are inline by default, and previously
// we would normally mount to divs - so make the element block display type
// by default.
:where(magnific-gallery) {
    display: block;
}

.mfp-image-holder .mfp-content {
    max-width: 1000px;
}

.mfp-ajax-holder .mfp-content {
    background-color: $color-bg-default;
    margin: 0 auto;
    padding: 50px;
    max-width: 800px;
}

.mfp-inline-holder .mfp-content {
    background-color: $color-bg-default;
    width: var(--default-container);
    margin: 0 auto;
    padding: 5vw;
}
