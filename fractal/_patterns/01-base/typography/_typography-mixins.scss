
@use "../../00-settings" as *;

@mixin section-title {
    display: block;
    margin-top: 0;
    margin-bottom: 40px;
    line-height: normal;
    color: $color-primary;
}

@mixin subtitle {
    @include font-set('primary');
    display: block;
    font-size: var(--centi-font-size);
    color: $color-grey-05;
    margin-bottom: 0;
    text-transform: uppercase;
}

@mixin h1($colour: inherit) {
    font-size: var(--h1-font-size);
    line-height: var(--h1-line-height);

    @if $colour {
        color: $colour;
    }
}

@mixin h2($colour: inherit) {
    font-size: var(--h2-font-size);
    line-height: var(--h2-line-height);

    @if $colour {
        color: $colour;
    }
}

@mixin h3($colour: inherit) {
    font-size: var(--h3-font-size);
    line-height: var(--h3-line-height);

    @if $colour {
        color: $colour;
    }
}

@mixin h4($colour: inherit) {
    font-size: var(--h4-font-size);
    line-height: var(--h4-line-height);

    @if $colour {
        color: $colour;
    }
}

@mixin h5($colour: inherit) {
    font-size: var(--h5-font-size);
    line-height: var(--h5-line-height);

    @if $colour {
        color: $colour;
    }
}

@mixin h6($colour: inherit) {
    font-size: var(--h6-font-size);
    line-height: var(--h6-line-height);

    @if $colour {
        color: $colour;
    }
}

@mixin error {
    background: $color-utility-error;
    border-left: 4px solid $color-utility-error-dark;
    color: #FFF !important;
}

@mixin positive {
    background: $color-utility-positive;
    border-left: 4px solid $color-utility-positive-dark;
    color: #FFF !important;
}

@mixin warning {
    background: $color-utility-warning;
    border-left: 4px solid $color-utility-warning-dark;
    color: #FFF !important;
}

@mixin inline-list {
    list-style-type: none;
    padding-left: 0;
    display: flex;
    flex-flow: row wrap;
    gap: calc(#{$spacing}/2) $spacing;
}
