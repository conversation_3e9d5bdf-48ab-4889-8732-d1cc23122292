@use "../../02-components/forms/form-mixins" as *;
@use "../../00-settings" as *;
@use "../../02-components/buttons-and-links/button-mixins" as *;


:where(button) {
    padding: 0;
}

/* -------------------------------
Label Styles
---------------------------------- */
:where(label) {
    display: block;
}


/* -------------------------------
(Submit) Button Styles
---------------------------------- */
:where(button[type="submit"]) {
    @include button;
}

/* -------------------------------
Inputs and Textarea
---------------------------------- */
:where(
    [type="text"],
    [type="number"],
    [type="email"],
    [type="tel"],
    [type="search"],
    [type="password"],
    [type="url"],
    [type="date"],
    [type="time"],
    [type="file"],
    select[multiple=""],
    textarea) {
    @include form-textbox;
    @include form-textbox-styles;

    &.field--small {
        @include form-textbox('small');
    }
}

/* Restrict text area resizing to vertically only */
:where(textarea) {
    resize: vertical;
}

// The following is only required if a height is on the form-textbox mixin
//
// :where(textarea) {
//     min-height: var(--field-height);
//     height: unset;
// }


/* -------------------------------
Select Styles
---------------------------------- */
:where(select):not([multiple=""]) {
    @include field-select;

    // ensure fancy select menu text is not white on white
    option[value]:not([value=""]) {
        color: $color-body-font;
    }
}



/* -------------------------------
Fieldsets Styles
---------------------------------- */

/* Wrapper */
:where(fieldset) {
    border: none;
    padding: 0;
    margin: 0;
 }


:where([type="checkbox"]) {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;

    &:before {
        content: "";
        @include checkbox;
    }

    &:checked:after {
        content: "";
        @include checkboxChecked;
    }
}

:where([type="radio"]) {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;

    &:before {
        content: "";
        @include radio;
    }

    &:checked:before {
        background-color: $color-utility-neutral;
        border-color: $color-utility-neutral;
    }

    &:checked:after {
        content: "";
        @include radioChecked;
    }
}

:where(
    [type="text"],
    [type="number"],
    [type="email"],
    textarea,
    select)[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
}



/**
 * Placeholders
 */

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: $color-utility-placeholder;
}

::-moz-placeholder { /* Firefox 19+ */
    color: $color-utility-placeholder;
}

:-ms-input-placeholder { /* IE 10+ */
    color: $color-utility-placeholder;
}

:-moz-placeholder { /* Firefox 18- */
    color: $color-utility-placeholder;
}



/**
 * Autofill fields
 */

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    background-color: $color-grey-02;
    -webkit-text-fill-color: #000;
    -webkit-box-shadow: 0 0 0px 1000px $color-grey-02 inset;
}

input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
    outline: $color-grey-06 auto 5px;
    background-color: $color-grey-02;
    -webkit-text-fill-color: #000;
    -webkit-box-shadow: 0 0 0px 80px $color-grey-02 inset;
    transition: background-color 5000s ease-in-out 0s;
}
