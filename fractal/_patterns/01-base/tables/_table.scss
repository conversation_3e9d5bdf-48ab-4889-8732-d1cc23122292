
@use "../../00-settings" as *;

:where(table) {
    border-collapse: collapse;
    border: 1px solid $color-grey-02;
    width: 100%;
    margin-bottom: $spacing*2;

    .content-block & {
        margin-bottom: $spacing*3;
    }
}

:where(
    th,
    td) {
        text-align: left;
    padding: 5px 10px;
    border: 1px solid $color-grey-02;
}

:where(
    th,
    thead td) {
    background: rgba($color-grey-09, 5%);
}



/*
    Responsive table styles

    Requires a wrapping element with overflow auto to function
    header cells needs `min-width: max-content;` on mobile so content don't squish
    but it doesn't play well with grid layouts so hide on desktop.

    CKeditor wraps a table with <figure> and .table so we make
    that responsive by default here too.

*/

.responsive-table,
figure.table {
    overflow: auto;
    border: 0;
    margin-bottom: $spacing*2;
    background: linear-gradient(to right, #fff 30%, rgba(255, 255, 255, 0)),
        linear-gradient(to right, rgba(255, 255, 255, 0), #fff 70%) 0 100%,
        radial-gradient(
          farthest-side at 0% 50%,
          rgba(0, 0, 0, 0.2),
          rgba(0, 0, 0, 0)
        ),
        radial-gradient(
            farthest-side at 100% 50%,
            rgba(0, 0, 0, 0.2),
            rgba(0, 0, 0, 0)
          )
          0 100%;
    background-repeat: no-repeat;
    background-color: #fff;
    background-size: 40px 100%, 40px 100%, 14px 100%, 14px 100%;
    background-position: 0 0, 100%, 0 0, 100%;
    background-attachment: local, local, scroll, scroll;

    :where(table) {
        border: 0;
        margin-bottom: 0;
    }

    :where(th,
        thead td) {
        @include screen($bp1199, 'max') {
            min-width: max-content;
        }
    }
}
