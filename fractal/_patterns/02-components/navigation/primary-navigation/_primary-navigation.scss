@use "../../../00-settings" as *;
@use "../../../05-utilities/helpers/utility-mixins" as *;
@use "../../../02-components/buttons-and-links/_button-mixins" as *;

:root {
    --menu-item-vertical-border-width: 1px;
    --menu-item-padding-vertical: 5px;
    --menu-item-padding-horizontal: 8px;
    --menu-item-height: 48px;
    --toggle-menu-transition-speed: 350ms;
    --toggle-submenu-transition-speed: 200ms;
    --toggle-button-width: 48px;
}

@include screen($bp1200) {
    :root {
        --menu-item-padding-vertical: 0.5em;
        --menu-item-padding-horizontal: 0.5em;
    }
}


// mobile styles
@include screen($bpmobile, 'max') {
    :root {
        --toggle-submenu-transition-speed: 200ms;
        --menu-item-height: 24px;
    }
}

@include screen($bpdesktop) {
    :root {
        --toggle-button-width: 24px;
    }
}


.menu-btn {
    width: 48px;
    display: grid;
    place-items: center;
    cursor: pointer;
    position: relative;
    line-height: 1;
    z-index: 1;
    padding-top: 6px;

    * {
        pointer-events: none;
    }

    .top,
    .bottom {
        opacity: 1;
        transition: opacity var(--toggle-menu-transition-speed);
    }

    .middle-1,
    .middle-2 {
        transform-origin: center center;
        transition: transform var(--toggle-menu-transition-speed);
    }

    &[aria-pressed="true"] {
        .top,
        .bottom {
            opacity: 0;
        }

        .middle-1 {
            transform: rotate(45deg);
        }

        .middle-2 {
            transform: rotate(-45deg);
        }
    }
}

.menu-btn--dropdowns {
    @include screen($bpdesktop) {
        display: none;
    }
}

.menu-btn__label {
    font-size: var(--milli-font-size);
    display: block;
    width: 5ch;
    text-align: center;
}



/*
    All device styles
*/

.primary-nav-container {
    @include font-set('body');
    position: fixed;
    overflow-y: scroll;
    overflow-x: hidden;
    top: calc(var(--header-inner-height) + var(--ribbon-alerts-height));
    left: 100%;
    right: -100%;
    height: calc(100% - var(--header-inner-height) - var(--ribbon-alerts-height));
    width: 100%;
    background: $color-white;
    z-index: -1;
    padding-bottom: $spacing-large;
    transition:
        top var(--toggle-menu-transition-speed) ease-in-out,
        left var(--toggle-menu-transition-speed) ease-in-out,
        right var(--toggle-menu-transition-speed) ease-in-out,
        width var(--toggle-menu-transition-speed) ease-in-out,
        box-shadow var(--toggle-menu-transition-speed) ease-in-out;
    -webkit-transform: translate3d(0, 0, 0);

    @include screen($bp768) {
        background: rgba($color-white, 0.85);
        padding-block: var(--section-small);
    }

    @include screen($bp992) {
        display: grid;
        align-items: center;
        padding-bottom: 0;
        padding-block: $spacing-medium;
    }

    @include screen($bp1200) {
        height: calc(100vh - var(--header-inner-height) - var(--ribbon-alerts-height));
    }

    .header--sticky & {
        top: var(--header-inner-height);
        height: calc(100vh - var(--header-inner-height));
    }

    :where(ul) {
        @include list-reset;
        margin: 0;
    }

    :where(a) {
        color: inherit;
        text-decoration: none;
        display: block;
        transition: all 100ms ease;
        line-height: 1.25;
    }
}

.primary-nav-container--dropdowns {
    @include screen($bpdesktop) {
        all: unset;
    }
}

#content,
#footer {
    @include screen($bp768) {
        transition: filter var(--toggle-menu-transition-speed) ease-in-out;
        transition-delay: 0;
    }
}

.js-menu--open {
    body {
        overflow-y: hidden; // Stop scroll
    }

    // Blur menu background
    #content,
    #footer {
        @include screen($bp768) {
            filter: blur(20px);
            transition-delay: calc(var(--toggle-menu-transition-speed) / 2);
            transition-duration: calc(var(--toggle-menu-transition-speed) / 2);
        }
    }

    .primary-nav-container {
        left: 0;
        right: 100%;
        opacity: 1;
    }
}


.primary-nav {
    padding-inline: var(--default-container-gutter);
    display: grid;
}

:where(.primary-nav--full-screen) {
    @include screen($bp768) {
        grid-template-columns: repeat(2, 1fr);
        column-gap: 48px;
    }

    @include screen($bp992) {
        padding-bottom: 96px;
    }

    @include screen($bp1200) {
        gap: 48px;
        grid-template-columns: repeat(2, 1fr) 0.75fr;
    }

    @include screen($bp1400) {
        gap: 64px;
        padding-bottom: 112px;
    }
}

:where(.primary-nav--dropdowns) {
    --menu-item-padding-vertical: #{$spacing*1.5};
    --menu-item-padding-horizontal: 8px;

    padding-block: $spacing-large;

    @include screen($bpdesktop) {
        grid-template-columns: auto;
        grid-auto-flow: column;
        gap: $spacing-large;
        padding-block: 0;
    }
}

.primary-nav__column {
    display: grid;
    gap: $spacing-medium;
    padding-block: $spacing-large;
    border-top: $border;

    @include screen($bp992) {
        align-content: start;
        justify-content: start;
    }

    @include screen($bp1200) {
        border-top: none;
        padding-block: 0;
        gap: var(--h3-font-size);
    }
}

.primary-nav__column--stay {
    @include screen($bp768) {
        grid-row: span 2;
    }

    @include screen($bp1200) {
        grid-row: auto;
    }
}

.menu-item--home {
    :where(.primary-nav--full-screen) & {
        padding-block: #{$spacing-large - $spacing-xxsmall};
    }

    @include screen($bp768) {
        :where(.primary-nav--full-screen) & {
            grid-column: span 2;
        }
    }

    @include screen($bp1200) {
        :where(.primary-nav--full-screen) & {
            padding-block: 0;
            grid-column: span 3;
        }
    }

    .menu-item__link {
        :where(.primary-nav--full-screen) & {
            display: grid;
            grid-auto-flow: column;
            gap: 0.5em;
            justify-content: start;
            align-items: center;
        }

        &:before {
            content: none;
        }
    }
}

.primary-nav__column-title {
    @include font($primary-font, $fw-medium);
    margin-bottom: 0;
    font-size: var(--body-font-size);

    @include screen($bp992) {
        font-size: var(--h4-font-size);
    }

    :where(.primary-nav--dropdowns) & {
        display: none;
    }
}


.menu-item {
    position: relative;

    @include screen($bp992) {
        border: none;
        position: static;
    }

    :where(.primary-nav--dropdowns) & {
        @include screen($bpdesktop) {
            display: grid;
            grid-auto-flow: column;
        }
    }

    :where(.primary-nav--dropdowns) &--has-children {
        @include screen($bpdesktop) {
            position: relative;
        }
    }

    :where(.primary-nav--dropdowns) &--has-children:hover {
        @include screen($bpdesktop) {
            overflow: visible;
        }
    }
}

.menu-item__link {
    padding-block: var(--menu-item-padding-vertical);
    min-height: var(--menu-item-height);
    display: flex;
    align-items: center;
    position: relative;

    @include screen($bpdesktop) {
        min-height: unset;
    }

    :where(.primary-nav--full-screen) & {
        padding-block: $spacing;
    }

    &:hover,
    &:focus,
    &:active {
        color: $color-secondary;

        + .menu__menu-item__togg-submenu:after {
            filter: none;
        }
    }

    :where(.primary-nav--dropdowns) & {
        padding-inline: var(--menu-item-padding-horizontal);
    }

    .menu-item--depth-1 > & {
        @include font-set('accent');
    }

    :where(.primary-nav--full-screen) .menu-item--depth-1 > & {
        padding-block: $spacing-xxsmall;
    }

    :where(.primary-nav--dropdowns) .menu-item--depth-1 > & {
        @include screen($bpdesktop) {
            // Fill header height
            padding-block: calc((var(--header-inner-height) - 20px) / 2);
            padding-inline: var(--menu-item-padding-horizontal);
            transition:
                padding-block $header-transition ease-in-out;
        }
    }

    :where(.primary-nav--full-screen) .menu-item--depth-1 > & {
        font-size: var(--h4-font-size);

        @include screen($bp992) {
            font-size: var(--h3-font-size);
        }
    }

    :where(.primary-nav--full-screen) .menu-item--depth-2 > & {
        @include text-link;
    }


    :where(.primary-nav--dropdowns) .menu-item--depth-2 > & {
        @include screen($bpdesktop) {
            border-top: 1px solid $color-grey-01;
        }

        &:hover,
        &:focus,
        &:active {
            @include screen($bpdesktop) {
                border-color: $color-primary;
                background-color: $color-primary;
                color: $color-white;
            }
        }
    }

    .menu-item--current-item > & {
        color: $color-black;
        background-color: inherit;
    }

    .menu-item--current-item > & {
        &:hover,
        &:focus,
        &:active {
            color: $color-grey-01;
            background: rgba($color-primary,0.5);
        }
    }

    :where(.primary-nav--dropdowns) .menu-item--current-item > & {
        border-color: $color-secondary;
        background-color: $color-secondary;
        color: $color-white;

        &:hover,
        &:focus,
        &:active {
            @include screen($bpdesktop) {
                border-color: $color-primary;
                background-color: $color-primary;
                color: $color-white;
            }
        }
    }

    :where(.primary-nav--full-screen) .menu-item--current-item > & {
        &:hover,
        &:focus,
        &:active {
            @include screen($bpdesktop) {
                color: inherit;
                background-color: inherit;
            }
        }
    }

    .menu-item--current-ancestor > & {
        color: $color-black;
    }

    .menu-item--has-children > & {
        padding-right: calc(var(--toggle-button-width) + 4px);

        @include screen($bpdesktop) {
            padding-right: var(--menu-item-padding-horizontal);
        }
    }

    :where(.primary-nav--dropdowns.primary-nav--click) .menu-item--has-children > & {
        // Add padding for the absolute positioned subnav toggle
        @include screen($bpdesktop) {
            padding-right: calc(var(--toggle-button-width) + var(--menu-item-padding-horizontal));
        }
    }

    :where(.primary-nav--full-screen) .menu-item--has-children > & {
        @include screen($bp768) {
            padding-right: 0;
        }
    }
}

.menu-item__subtitle {
    font-size: var(--centi-font-size);
    margin-bottom: $spacing-xsmall;
    line-height: 1.5;

    @include screen($bp992) {
        font-size: var(--body-font-size);
    }
}

.subnav-toggle {
    display: grid;
    place-items: center;
    position: absolute;
    top: 0;
    width: var(--toggle-button-width);
    height: var(--menu-item-height);
    right: 0;
    text-indent: -999em;

    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        border-radius: 1px;
        transition: transform var(--toggle-submenu-transition-speed) ease-in-out;
        background: url(data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220px%22%20height%3D%2212px%22%20viewBox%3D%220%200%2020%2012%22%3E%3Cpath%20fill%3D%22%23FFF%22%20fill-rule%3D%22evenodd%22%20d%3D%22M9.86820415%2C11.9882042%20L19.1642043%2C2.83620405%20C19.2810323%2C2.71280235%2019.2810323%2C2.51960574%2019.1642043%2C2.39620404%20L17.5642043%2C0.82020402%20C17.5040579%2C0.759129508%2017.4219229%2C0.724739094%2017.3362043%2C0.724739094%20C17.2504857%2C0.724739094%2017.1683506%2C0.759129508%2017.1082043%2C0.82020402%20L9.64420415%2C8.15620413%20L2.18020404%2C0.79620402%20C2.05577411%2C0.674238298%201.85663396%2C0.674238298%201.73220403%2C0.79620402%20L0.132204009%2C2.39620404%20C0.0796503409%2C2.45577786%200.0511347695%2C2.5327699%200.0522040082%2C2.61220405%20C0.0511347695%2C2.6916382%200.0796503409%2C2.76863024%200.132204009%2C2.82820405%20L9.43620415%2C11.9882042%20C9.56063407%2C12.1101699%209.75977423%2C12.1101699%209.88420415%2C11.9882042%20L9.86820415%2C11.9882042%20Z%22%2F%3E%3C%2Fsvg%3E) no-repeat center center;
        filter: invert(0.66);
    }

    .menu-item--has-children-open > &::after {
        transform: rotate(180deg);
    }

    .menu-item--current-ancestor > &::after {
        filter: invert(0.9);
    }

    :where(.primary-nav--full-screen) & {
        @include screen($bpdesktop) {
            display: none;
        }
    }

    :where(.primary-nav--dropdowns).primary-nav--hover & {
        @include screen($bpdesktop) {
            display: none;
        }
    }

    :where(.primary-nav--dropdowns).primary-nav--click & {
        @include screen($bpdesktop) {
            height: 100%;
        }
    }

    // :where(.primary-nav--dropdowns).primary-nav--click &:after {
    //     @include screen($bpdesktop) {
    //         width: 100%;
    //         height: 100%;
    //     }
    // }
}

// Mobile submenu
.subnav {
    @include screen($bpmobile, 'max') {
        display: block;
        max-height: 0;
        opacity: 0;
        overflow: hidden;
        transition:
            height calc(var(--toggle-submenu-transition-speed)*2) ease-in-out,
            opacity calc(var(--toggle-submenu-transition-speed)*2) ease-in-out;

        .menu-item--has-children-open > & {
            max-height: 1000px;
            opacity: 1;
        }
    }

    :where(.primary-nav--dropdowns) & {
        @include screen($bpdesktop) {
            grid-row: 2;
            grid-column: 1 / -1;
            position: absolute;
            width: 240px;
            padding: 0;
            margin: 0;
            background: $color-white;
            box-shadow: 0 30px 50px -20px rgba(0, 0, 0, .3);
            visibility: hidden;
            overflow: visible;
            transition:
                height calc(var(--toggle-submenu-transition-speed)*2) ease-in-out,
                opacity calc(var(--toggle-submenu-transition-speed)*2) ease-in-out;
        }
    }

    :where(.primary-nav--dropdowns.primary-nav--hover) .menu-item--has-children:hover > & {
        @include screen($bpdesktop) {
            visibility: visible;
            opacity: 1;
            max-height: 1000px;
            opacity: 1;
        }
    }

    :where(.primary-nav--dropdowns.primary-nav--click) .menu-item--has-children-open > & {
        @include screen($bpdesktop) {
            visibility: visible;
            opacity: 1;
            max-height: 1000px;
            opacity: 1;
        }
    }

    :where(.primary-nav--full-screen) & {
        @include screen($bp992) {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 0;
        }
    }
}

