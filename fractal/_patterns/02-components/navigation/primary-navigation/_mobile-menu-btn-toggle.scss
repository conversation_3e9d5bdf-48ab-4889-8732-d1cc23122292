:root {
    --outer-line-offset: 9px;
}

@keyframes mobileMenuBtnTopOpen {
    0% {
        transform: translate(0, calc(var(--outer-line-offset) * -1));
    }
    50%, 70% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(0, 0) rotate(-45deg);
    }
}

@keyframes mobileMenuBtnTopClose {
    0% {
        transform: translate(0, 0) rotate(-45deg);
    }
    50%, 70% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(0, calc(var(--outer-line-offset) * -1));
    }
}

@keyframes mobileMenuBtnBottomOpen {
    0% {
        transform: translate(0, var(--outer-line-offset));
    }
    50%, 70% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(0, 0) rotate(45deg);
    }
}

@keyframes mobileMenuBtnBottomClose {
    0% {
        transform: translate(0, 0) rotate(45deg);
    }
    50%, 70% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(0, var(--outer-line-offset));
    }
}



@keyframes mobileMenuBtnMiddleClose {
    0% {
        opacity: 0;
    }
    50%, 70%, 100% {
        opacity: 1;
    }
}

@keyframes mobileMenuBtnMiddleOpen {
    0% {
        opacity: 1;
    }
    50%, 70%, 100% {
        opacity: 0;
    }
}

#top,
#middle,
#bottom {
    transform-origin: center center;
    animation-fill-mode: forwards;
    animation-duration: 400ms;
}

/* animations active */
#top {
    animation-name: mobileMenuBtnTopClose;
}
#middle {
    animation-name: mobileMenuBtnMiddleClose;
}
#bottom {
    animation-name: mobileMenuBtnBottomClose;
}

/* Close button */
[aria-pressed="true"] #top {
    animation-name: mobileMenuBtnTopOpen;
}
[aria-pressed="true"] #middle {
    animation-name: mobileMenuBtnMiddleOpen;
}
[aria-pressed="true"] #bottom {
    animation-name: mobileMenuBtnBottomOpen;
}
