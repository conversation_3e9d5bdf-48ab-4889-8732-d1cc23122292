
@use "../../00-settings" as *;

/*
    Breadcrumb navigayion styles that shrink to only the parent item on mobile
    since space is limited.
*/

.breadcrumb {
    list-style: none;
    margin: 1.5rem 0;
    padding: 0;
    font-size: 1.4rem;
    color: #848484;

    @media screen and (max-width: $bp767) {
        margin-top: 20px;
        margin-bottom: 10px;
        line-height: 1.4;
    }

    &::before {
        content: '\003c\00a0';
        font-weight: bold;
        position: relative;

        @include screen($bp768) {
            display: none;
        }

    }

    &__crumb {
        display: inline-block;

        @include screen($bp767, 'max') {
            display: none;

            &:nth-last-child(2) {
                display: inline-block;
            }
        }

        &:before {
            @include screen($bp768) {
                content: "\00a0/\00a0";
            }
        }

        &:first-child:before {
            @include screen($bp768) {
                content: unset;
            }

        }

    }

}


/*
    The full posts archive parent page is usually more useful that the year archive for posts,
    so this code shows that instead.
    TODO: Don't love that it's using a body class, would prefer a modifier on the list
*/
.posts {
    .breadcrumb__crumb {
        @include screen($bp767, 'max') {

            &:nth-last-child(2) {
                display: none;
            }

            &:nth-last-child(3) {
                display: inline-block;
            }
        }
    }
}
