
@mixin bleed-left($bg-colour: inherit, $width: 100vw) {
    position: relative;

    &:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        right: calc(100% - 0.5px); // Webkit zoom rounding error fix
        width: $width;
        background-color: $bg-colour;
    }
}

@mixin bleed-right($bg-colour: inherit, $width: 100vw) {
    position: relative;

    &:after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: calc(100% - 0.5px); // Webkit zoom rounding error fix
        width: $width;
        background-color: $bg-colour;
    }
}
