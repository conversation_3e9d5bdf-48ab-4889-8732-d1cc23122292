title: "Section component"
status: reference
collated: true
notes: "Top and bottom padding to denote a new area of content"
context:
   modifier: default
   text: "White section with default text"
variants:
 -
    name: "Small Section"
    context:
       modifier: "lightgrey section-small"
       text: "Small Section"
 -
    name: "Large Section"
    context:
       modifier: "white section-large"
       text: "Large Section"
 -
    name: "Primary Colour"
    context:
       modifier: "primary reverse-text"
       text: "Primary colour section with reverse text"
 -
    name: "Secondary Colour"
    context:
       modifier: "secondary reverse-text"
       text: "Secondary colour section with reverse text"
 -
    name: "Accent Colour 1"
    context:
       modifier: "accent-01 reverse-text"
       text: "Accent colour 1 section with reverse text"
 -
    name: "Accent Colour 2"
    context:
       modifier: "accent-02"
       text: "Accent colour 2 section"
 -
    name: "Alternative Colour"
    context:
       modifier: "alternative reverse-text"
       text: "Alternative base colour section with reverse text"