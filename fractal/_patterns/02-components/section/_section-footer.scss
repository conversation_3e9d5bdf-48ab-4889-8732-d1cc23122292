@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.section-footer {
    text-align: center;
    margin-top: $spacing*6;
}

.section-footer__cta {
    display: flex;
    gap: #{$spacing*2};
    align-items: center;
    justify-content: center;

    .button {
        @include font-set('primary');
        padding: #{$spacing*2} #{$spacing*3};
        font-size: 1.5rem;
        font-weight: 400;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.3s ease;
        min-width: 140px;

        &:first-child {
            background: transparent;
            color: $color-grey-08;
            border: 1px solid $color-grey-04;

            &:hover,
            &:focus,
            &:active {
                background: $color-grey-02;
                color: $color-grey-08;
                border-color: $color-grey-06;
            }
        }

        &:last-child {
            background: $color-grey-08;
            color: $color-white;
            border: 1px solid $color-grey-08;

            &:hover,
            &:focus,
            &:active {
                background: darken($color-grey-08, 10%);
                border-color: darken($color-grey-08, 10%);
            }
        }
    }
}
