@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;
@use "../../01-base/typography/_typography-mixins" as *;


.section-header {
    display: grid;
    text-align: center;
    margin-bottom: var(--section-small);
}

.section-header__subtitle {
    grid-row: 1;
    @include subtitle;
}

.section-header__cta {
    display: flex;
    gap: #{$spacing*2};
    align-items: center;
    justify-content: center;
}

.section-header__tagline {
    display: block;
    margin-inline: auto;
    width: calc(60% - (2 * var(--layout-column-gap)));
    margin-bottom: var(--paragraph-break);
}

.section-header--sidebar {
    text-align: left;
    margin-bottom: 0;
}
