
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;
@use "../../02-components/section/section-mixins" as *;

/* [01]
    Bleeds commented so as to not bloat the css unnecessarily.
    Uncomment to use
*/

.background--bleed + .background--bleed {
    margin-top: -0.25px; // Webkit Zoom rounding error fix
}

.bg-default {
    background-color: $color-bg-default;
}

.bg-default-tint-01 {
    background-color: $color-grey-01;

    &.background--bleed {
        @include bleed-left($color-grey-01);
        @include bleed-right($color-grey-01);
    }

    /* [01]
    &.background--bleed-left {
        @include bleed-left($color-grey-01);
    }

    &.background--bleed-right {
        @include bleed-right($color-grey-01);
    }
    */
}

.bg-primary {
    background-color: $color-primary;
    @include reverse-text;

    &.background--bleed {
        @include bleed-left($color-primary);
        @include bleed-right($color-primary);
    }

    /* [01]
    &.background--bleed-left {
        @include bleed-left($color-primary);
    }

    &.background--bleed-right {
        @include bleed-right($color-primary);
    }
    */
}

.bg-secondary {
    background-color: $color-secondary;

    &.background--bleed {
        @include bleed-left($color-secondary);
        @include bleed-right($color-secondary);
    }

    /* [01]
    &.background--bleed-left {
        @include bleed-left($color-secondary);
    }

    &.background--bleed-right {
        @include bleed-right($color-secondary);
    }
    */
}

.bg-alternative {
    background-color: $color-alternative;

    &.background--bleed {
        @include bleed-left($color-alternative);
        @include bleed-right($color-alternative);
    }

    /* [01]
    &.background--bleed-left {
        @include bleed-left($color-alternative);
    }

    &.background--bleed-right {
        @include bleed-right($color-alternative);
    }
    */
}

.bg-accent-01 {
    background-color: $color-accent-01;

    &.background--bleed {
        @include bleed-left(color-accent-01);
        @include bleed-right(color-accent-01);
    }

    /* [01]
    &.background--bleed-left {
        @include bleed-left(color-accent-01);
    }

    &.background--bleed-right {
        @include bleed-right(color-accent-01);
    }
    */
}

.bg-accent-02 {
    background-color: $color-accent-02;

    &.background--bleed {
        @include bleed-left(color-accent-02);
        @include bleed-right(color-accent-02);
    }
}
