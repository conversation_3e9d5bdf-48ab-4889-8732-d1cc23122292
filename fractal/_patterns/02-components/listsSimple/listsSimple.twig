
{% if list.type == 'unordered' %}
<ul class="{{ list.modifier }}">
{% for item in list.items %}
    <li>{{ item }}</li>
{% endfor %}
</ul>
{% endif %}

{% if list.type == 'ordered' %}
<ol class="{{ list.modifier }}">
{% for item in list.items %}
    <li>{{ item }}</li>
{% endfor %}
</ol>
{% endif %}

{% if list.type == 'fileList' %}
<ul class="{{ list.modifier }}">
{% for item in list.items %}
    <li>{{ item }}</li>
{% endfor %}
</ul>
{% endif %}
