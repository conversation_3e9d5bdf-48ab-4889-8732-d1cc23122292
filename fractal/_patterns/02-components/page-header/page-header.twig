<!-- Template: {{ _self }} -->

{% if pageSubtitle is not defined and entry ?? false and entry.subtitle ?? false %}
    {% set pageSubtitle = entry.subtitle %}
{% endif %}

{% set showBreadcrumb = showBreadcrumb is defined ? showBreadcrumb : true %}

<hgroup class="page-header">
    <h1 class="page-header__heading">
        {% if pageSubtitle ?? false %}
            <span class="page-header__subtitle">
                {{ pageSubtitle }}
            </span>
        {% endif %}
        <span class="page-header__title">{{ pageTitle }}</span>
    </h1>
    {% if showBreadcrumb ?? false %}
        {% include "01_core/_layouts/breadcrumb" %}
    {% endif %}
</hgroup>
