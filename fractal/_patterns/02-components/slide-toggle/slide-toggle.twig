<div class="archive">

    <h3 class="archive__title">Archive</h3>

    <button aria-pressed="true" aria-label="View all posts made in 2021" class="js--slide-toggle slide-toggle__trigger">2021</button>
    <ul class="archive__list archive__list-depth0" aria-expanded="true">
        <li class="archive__list__item">
            <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/eget-gravida-erat">
                Suspendisse eget gravida erat
            </a>
            <button aria-pressed="false" aria-label="View all posts made in September 2021" class="js--slide-toggle slide-toggle__trigger">September</button>
            <ul class="archive__list archive__list-depth1" aria-expanded="false">
                <li class="archive__list__item">
                    <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/eget-gravida-erat">
                        Suspendisse eget gravida erat
                    </a>
                </li>
            </ul>
        </li>
    </ul>
    <button aria-pressed="true" aria-label="View all posts made in 2020" class="js--slide-toggle slide-toggle__trigger">2020</button>
    <ul class="archive__list archive__list-depth0" aria-expanded="true">
        <li class="archive__list__item">
            <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/eget-gravida-erat">
                Suspendisse eget gravida erat
            </a>
            <button aria-pressed="false" aria-label="View all posts made in January 2020" class="js--slide-toggle slide-toggle__trigger">January</button>
            <ul class="archive__list archive__list-depth1" aria-expanded="false">
                <li class="archive__list__item">
                    <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/sed-congue-urna-vel-leo-cursus-venenatis">
                        Nam at volutpat mauris
                    </a>
                </li>
                <li class="archive__list__item">
                    <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/pellentesque-metus-metus-fringilla-quis-ex-interdum">
                        Nullam accumsan blandit lorem
                    </a>
                </li>
                <li class="archive__list__item">
                    <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/curabitur-ac-elit-id-augue-cursus-vulputate-vel-vitae-justo">
                        Scelerisque laoreet libero vel mattis nullam
                    </a>
                </li>
            </ul>
        </li>
    </ul>
    <button aria-pressed="true" aria-label="View all posts made in 2019" class="js--slide-toggle slide-toggle__trigger">2019</button>
    <ul class="archive__list archive__list-depth0" aria-expanded="true">
        <li class="archive__list__item">
            <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/eget-gravida-erat">
                Suspendisse eget gravida erat
            </a>
            <button aria-pressed="false" aria-label="View all posts made in December 2019" class="js--slide-toggle slide-toggle__trigger">December</button>
            <ul class="archive__list archive__list-depth1" aria-expanded="false">
                <li class="archive__list__item">
                    <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/aenean-pulvinar-metus-mi">
                        Curabitur sit amet lacus commodo
                    </a>
                </li>
                <li class="archive__list__item">
                    <a class="archive__list__item__link" href="http://seas.lucy.bunnysites.com/posts/sed-porta-gravida-hendrerit">
                        Nam consequat purus non auctor blandit
                    </a>
                </li>
            </ul>
        </li>
    </ul>
</div>

<script>
/* --------------------------------------
Slide Toggles
---------------------------------------*/
const slideToggles = document.querySelectorAll('.js--slide-toggle');

function toggleSlide(e) {

    if (e instanceof Event) {
        e.preventDefault();
        e.stopPropagation();
    }

    let trigger = e.target;

    if (trigger.getAttribute('aria-pressed') == 'false') {
        trigger.setAttribute('aria-pressed', 'true');
        trigger.nextElementSibling.setAttribute('aria-expanded', true);
    } else {
        trigger.setAttribute('aria-pressed', 'false');
        trigger.nextElementSibling.setAttribute('aria-expanded', false);
    }
}

if(slideToggles.length > 0) {

    slideToggles.forEach(trigger => {
        trigger.addEventListener("click", toggleSlide, false);
    });

}
</script>
