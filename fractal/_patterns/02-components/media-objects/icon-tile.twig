<!-- Template: {{ _self }} -->

{% set linkField = linkField ?? item.linkField ?? null %}
{% set showCta = showCta ?? true %}

{% tag linkField ?? false ? 'a' : 'div' with {
    class: [
        'icon-tile',
        modifierClass ?? false
    ],
    href: linkField ?? null
}
%}
    <div class="icon-tile__icon">
        {% if icon ?? false %}
            {% if icon is string and icon starts with '@' %}
                {{ svg(icon) }}
            {% elseif icon.filename ends with '.svg' %}
                {% set cacheKey = "asset:#{icon.id}:#{icon.dateUpdated|date('c')}" %}
                {% cache globally using key cacheKey for 3 hours %}
                {{ svg(icon)|attr({
                    class: 'icon-tile__icon--svg',
                    width: icon.width,
                    height: icon.height
                }) }}
                {% endcache %}
            {% else %}
                {{ icon.getImg({ mode: 'fit', width: 100, height: 100, format: 'webp' }, ['1.5x', '2x', '3x'])|attr({
                    class: 'icon-tile__icon--image',
                    loading: 'lazy',
                    role: icon.alt ? null : 'presentation'
                })
            }}
            {% endif %}
        {% endif %}
    </div>
    <div class="icon-tile__text {{ item.itemTitle is defined and item.itemTitle ? 'icon-tile__text--2-lines'}}">
        {% if item.itemTitle is defined %}
            <strong>{{ item.itemTitle }}</strong><br>
        {% endif %}
        {{ item.title|nl2br }}
    </div>
    {% if linkField ?? false and showCta ?? false %}
        <span class="icon-tile__link-label">View<span class="-vis-hidden"> {{ item.title }}</span>{{ svg('@webroot/assets/icon-system/icon_form_arrow-right.svg')|attr({ class: 'icon-tile__link-label__arrow' }) }}</span>
    {% endif %}
 {% endtag %}
