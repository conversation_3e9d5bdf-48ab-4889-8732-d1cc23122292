
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/_utility-mixins" as *;

a:where(.card) {
    text-decoration: none;
    color: inherit;
}

.card {

    --tb-padding: #{$spacing};
    --lr-padding: #{$spacing};

    @include screen($bp360) {
        --tb-padding: #{$spacing*2};
        --lr-padding: #{$spacing*2};
    }

    @include screen($bp560) {
        --tb-padding: #{$spacing*4};
        --lr-padding: #{$spacing*4};
    }

    background-color: $color-white;
    color: $color-body-font;
    display: flex;
    flex-direction: column;
    height: 100%;


    @container (width > 32ch) {
        --tb-padding: #{$spacing*2};
        --lr-padding: #{$spacing*2};
    }

    @container (width > 48ch) {
        --tb-padding: #{$spacing*4};
        --lr-padding: #{$spacing*4};
    }

    @container (width > 80ch) {
        flex-direction: row;
    }

    &[role="link"]:hover,
    &[role="link"]:focus,
    &[role="link"]:active {
        box-shadow:0 0 2px 0 rgba(0, 0, 0, 0.33)
    }
}

.card__heading {
    color: $color-secondary;
}

.card__content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    order: 1;
    padding: var(--tb-padding) var(--lr-padding);
    border-bottom-left-radius: $radius-default;
    border-bottom-right-radius: $radius-default;
    border: $border;
    border-top: 0;

}

.card__media {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $color-grey-01;
    aspect-ratio: 16 / 9;
    overflow: hidden;
    border-top-left-radius: $radius-default;
    border-top-right-radius: $radius-default;
}

.card__cta {
    margin-top: auto;
}

.card__media--icon {
    display: grid;
    place-items: center;
}

.card__media__icon {
    width: 64px;
    height: 64px;
}

.card__media--fallback {
    @include fallback-logo-image;
}

