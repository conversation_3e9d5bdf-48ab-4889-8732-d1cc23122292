<!-- Template: {{ _self }} -->

{% set cardVideo = cardEntry.video.one() ?? null %}
{% set cardImage = cardEntry.imageSingular.one() ?? cardEntry.banner.one() ?? false %}
{% set cardIcon = cardEntry.iconAsset.one() ?? null %}
{% set cardTag = cardTag ?? null %}
{% set cardLink = cardLink ?? cardEntry.linkField.value ?? null %}

{% tag cardTag ? cardTag : "div" with {
    class: [
        'card',
        card.modifierClass ?? null,
    ],
    href: cardTag == 'a' ? cardLink : null,
} %}

    <div class="card__content">

        <h3 class="card__heading">{{ cardEntry.itemTitle ?? cardEntry.navigationTitle ?? cardEntry.title }}</h3>

        {% if cardEntry.richtextBasicLinks ?? null %}
            {{ cardEntry.richtextBasicLinks }}
        {% endif %}

        {% if cardEntry.linkHelper.one() ?? null and not cardTag == 'a' %}

            {% include './01_core/_blocks/link-helper.twig' with {
                'linkHelper': {
                    modifierClass : 'button card__cta'
                }
            } %}

        {% elseif cardEntry.linkField ?? null and not cardTag == 'a' %}

            {% include '01_core/_blocks/individual-link' with {
                link: cardEntry.linkField ?? null,
                icon: staticIcon is defined ? staticIcon : null,
                'individualLink': {
                    modifierClass: 'button card__cta'
                }
            } %}

        {% endif %}

    </div>

    <div class="card__media card__media--{{ cardVideo ? 'video' : cardImage ? 'image' : cardIcon ? 'icon' : 'fallback' }}">

        {% if cardVideo ?? false %}
            {% include '02_components/video' with { video: cardVideo } %}
        {% elseif cardImage ?? false and cardImage|length > 1 %}
            {{ cardImage.getImg({ width: 1200, height: 675, format: 'webp', }, ['1.5x', '2x', '3x'])|attr({
                    class: 'card__image',
                    loading: 'lazy',
                    alt: cardImage.alt ?? null,
                    role: cardImage.alt ? null : 'presentation'
                })
            }}
        {% elseif cardIcon %}
            {% import "01_core/_macros/macros-assets.twig" as assetMacros %}
            {{ assetMacros.vectorSwitcher(cardIcon, 64, 64, 'card__media__icon' ) }}
        {% endif %}

    </div>

{% endtag %}
