<!-- Template: {{ _self }} -->

{% if fractal is not defined %}
    {% set profilePhoto = entry.profilePhoto.one() %}

    {% if profilePhoto %}
        {% set profilePhotoTransforms =
            profilePhoto.getImg({ width: 600, height: 600 }, ['1.5x', '2x', '3x'])|attr({
                loading: 'lazy',
                alt: 'Photo of ' ~ entry.title,
            })
        %}
    {% endif %}

{% else %}
    {# Doesn't support the different srcset resolutions (yet) #}
    {% set profilePhotoTransforms = "<img src=\"" ~ profilePhoto ~ "\">" %}
{% endif %}


{% tag profileLink ? 'a' : 'div' with {
    class: 'team-tile' ~ modifierClass ?? false,
    href: entry.url ?? null
} %}

    {% if profilePhoto %}
    <div class="team-tile__img">
        {{ profilePhotoTransforms|raw }}
    </div>
    {% else %}
    <div class="team-tile__img team-tile__img--fallback">

        <svg width="600px" height="600px" viewBox="0 0 600 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <rect id="Rectangle" fill="#0B1F41" x="0" y="0" width="600" height="600"></rect>
            </g>
        </svg>

    </div>
    {% endif %}

    <div class="team-tile__textarea">

        <h3 class="team-tile__title">{{ entry.title }}</h3>

        {% if entry.subtitle ?? false %}
        <p class="team-tile__position">{{ entry.subtitle }}</p>
        {% endif %}

        {% if not profileLink %}

            {% if entry.linkedin ?? false %}
            <p class="team-tile__social team-tile__social--linkedin">
                <a href="{{ entry.linkedin }}" target="_blank">
                Connect on LinkedIn
                <span class="icon">
                {{ svg('@webroot/assets/icon-system/icon_linkedin.svg') }}
                </span>
                </a>
            </p>
            {% endif %}

            {% if entry.facebook ?? false %}
            <p class="team-tile__social team-tile__social--linkedin">
                <a href="{{ entry.facebook }}" target="_blank">
                Connect on facebook
                <span class="icon">
                {{ svg('@webroot/assets/icon-system/icon_facebook.svg') }}
                </span>
                </a>
            </p>
            {% endif %}

        {% endif %}

    </div>

{% endtag %}
