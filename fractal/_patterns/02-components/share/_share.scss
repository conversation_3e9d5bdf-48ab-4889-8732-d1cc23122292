
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.share {
    display: flex;
    gap: var(--paragraph-break);
    align-items: center;
    flex-wrap: wrap;
    background: $color-grey-07;
    color: $color-white;
    padding: var(--section-small);

    &__title {
        flex: 1 1 auto;
        margin-bottom: 0;
    }

    &__list {
        flex: 0 0 auto;
        @include list-reset;
        display: flex;
        margin-bottom: 0;

        @supports (gap: 0.5em) {
            gap: 0.5em
        }

        &__item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 0.5em;

            @supports (gap: 0.5em) {
                padding: 0;
            }

            a {
                text-decoration: none;
                display: block;
                display: grid;
                place-items: center;
                width: $spacing*8;
                height: $spacing*8;
                border-radius: 50%;
                background-color: $color-white;
                padding: $spacing;
                color: $color-primary;

                &:hover,
                &:focus,
                &:active {
                    color: $color-secondary;
                }

                svg {
                    vertical-align: middle;

                    @include set-svg-colour {
                        transition: fill $link-transition;
                    }
                }
            }
        }
    }
}
