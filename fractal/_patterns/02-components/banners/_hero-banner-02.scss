
@use "../../00-settings" as *;

.hero-banner-02 {
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100% auto;
    position: relative;
}

.hero-banner-02:before {
    content: '';
    display: block;
    padding-top: 68.75%;
}

.hero-banner-02__heading {
    font-size: 2.4rem;
    line-height: 1.3;
    font-weight: bold;
}

.hero-banner-02__content {
    padding-top: $spacing-xlarge;
    padding-bottom: $spacing-xlarge;
}

.hero-banner-02__content .container > *:last-child {
    margin-bottom: 0;
}

@include screen($bp768) {
    .hero-banner-02__content {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba($color-white, .5);
    }
}

@include screen($bp992) {
    .hero-banner-02:before {
        padding-top: 56.25%;
    }
}

@include screen($bp1200) {
    .hero-banner-02:before {
        padding-top: 44%;
    }
}
