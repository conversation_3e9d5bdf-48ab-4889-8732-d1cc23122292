
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;


.inner-banner {
    position: relative;
    display: grid;
    justify-content: center;
    align-items: center;
    padding-top: var(--section);

    &__img {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        &:after {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;
            background-color: rgba($color-black, 0.3);
        }
    }

    &__content {

        @include reverse-text;
        z-index: 1;
        padding-block: var(--section-small);
        padding-inline: var(--default-container-gutter);

        @include screen($bp768) {
            padding-block: var(--section);
        }

        .breadcrumb {
            color: $color-white;

            a {
                color: rgba($color-white, 0.8);

                &:hover,
                &:focus {
                    color: $color-white;
                }
            }
        }
    }
}

