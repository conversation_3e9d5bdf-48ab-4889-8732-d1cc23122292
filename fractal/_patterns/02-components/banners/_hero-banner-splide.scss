
@use "../../00-settings" as *;

.hero-banner-splide {


}

.hero-banner {
    background: no-repeat center center;
    background-size: cover;
    background-color: black;
    color: $color-white;
    min-height: 50vh;
    position: relative;

    &::before {
        content: '\00a0';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0,0,0,.25);
    }

    &__text-container {
        position: relative;
        padding: $spacing*3 0;
    }

    &__heading {
        color: currentColor;
    }

    &__cta {
        margin-top: $spacing*2;
    }
}
