<!-- Template: {{ _self }} -->

{% if slider.sliderItems|length > 0 %}

<section class="hero-banner-slider">
    {% if slider.sliderItems|length > 1 %}
        {# splide wrapping bits #}

    <splide-slider
        data-splide-options='{
            "type": "loop",
            "autoplay": true,
            "interval": 4000,
            "arrows": true
        }'
    >

    {% include './02_components/_partials/custom-splide-arrows.twig' %}

    <div class="splide__track">
        <ul class="splide__list">
    {% endif %}

        {% for item in slider.sliderItems %}

        {% set bannerImage = item.banner.one() ?? null %}

        {% set bannerXlg = bannerImage.getUrl({
            mode: 'crop',
            width: 2400,
            height: 912,
            format: 'webp'
        }) ?? null %}

        {% set bannerLg = bannerImage.getUrl({
            mode: 'crop',
            width: 1600,
            height: 608,
            format: 'webp'
        }) ?? null %}

        {% set bannerMd = bannerImage.getUrl({
            mode: 'crop',
            width: 1200,
            height: 456,
            format: 'webp'
        }) ?? null %}

        {% set bannerSm = bannerImage.getUrl({
            mode: 'crop',
            width: 768,
            height: 431,
            format: 'webp'
        }) ?? null %}

        <li class="splide__slide hero-banner hero-banner--{{ loop.index }}">

            <style>
                .hero-banner--{{ loop.index }} {
                    background-image: url({{ bannerSm }});
                }
                @media screen and (min-width: 75em) { /* 1200px */
                    .hero-banner--{{ loop.index }} {
                        background-image: url({{ bannerXlg }});
                    }
                }
            </style>

            <div class="hero-banner__text-container container">
                <h2 class="hero-banner__heading">{{ item.itemTitle }}</h2>

                <div class="hero-banner__body-text">
                    {{ item.bannerContent ?? null }}
                </div>

                <div class="hero-banner__cta">

                    {% include "@button" with {
                        button: {
                            href: "https://google.com"
                        }
                    } %}
                </div>
            </div>


        </li>
        {% endfor %}

    {% if slider.sliderItems|length > 1 %}
            {# splide closing wrap bits #}
    </ul>
    </div>
    </splide-slider>
    {% endif %}

</section>

{% endif %}


{% if slider.sliderItems|length > 1 %}
{% js "/dist/js/splide.min.js" at head %}
{% endif %}
