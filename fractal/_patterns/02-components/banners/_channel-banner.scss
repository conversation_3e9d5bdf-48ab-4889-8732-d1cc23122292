
@use "../../00-settings" as *;

.channel-banner {
    position: relative;
    z-index: 1;
    border-top: $color-primary solid max(var(--section), 60px);

    &:before,
    &:after {
        content: "";
        position: absolute;
        top: calc(max(var(--section), 60px) * -1);
        bottom: calc(max(var(--section), 60px) * -1);
        width: 50vw;
        background-color: $color-primary;
        background-color: $color-primary;
        z-index: -1;
    }

    &:before {
        left: 100%;
    }

    &:after {
        right: 100%;
    }

    &--no-image {
        min-height: $spacing;

    }

    &__image {
        display: block;
        background-color: $color-grey-04;

        img {
            width: 100%;
        }
    }

    .button-back {
        color: $color-white;
        position: absolute;
        top: calc(max(var(--section), 60px) * -.75);
        z-index: 2;
    }
}
