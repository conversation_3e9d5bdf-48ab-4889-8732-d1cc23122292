
@use "../../00-settings" as *;

.entry-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;

    &.columns--full-width {
        grid-template-columns: 1fr;
    }

    &.columns--large {
        @include screen($bp768) {
            grid-template-columns:repeat(auto-fill, minmax(320px, 1fr));
        }

        @include screen($bp992) {
            grid-template-columns:repeat(auto-fill, minmax(600px, 1fr));
        }
    }

    &.columns--medium {
        @include screen($bp768) {
            grid-template-columns:repeat(auto-fill, minmax(320px, 1fr));
        }

        @include screen($bp992) {
            grid-template-columns:repeat(auto-fill, minmax(400px, 1fr));
        }
    }

    &.columns--small {
        @include screen($bp560) {
            grid-template-columns:repeat(auto-fill, minmax(320px, 1fr));
        }
    }

    &.columns--extra-small {
        grid-template-columns:repeat(auto-fill, minmax(240px, 1fr));
    }

    &.columns--tiny {
        grid-template-columns:repeat(auto-fill, minmax(160px, 1fr));
    }
}
