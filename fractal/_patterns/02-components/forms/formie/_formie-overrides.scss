@use 'sass:color';
@use "../../../00-settings" as *;

:root {
    --fui-font: var(--field-font-family);
    --fui-font-size: var(--body-font-size);

    --fui-color: #{$color-body-font};
    --fui-primary-color: #{$color-primary};
    --fui-primary-color-hover: #{darken($color-primary, 20%)};
    --fui-error: #{$color-utility-error};
    --fui-success: #{$color-utility-positive};
    --fui-gray-100: #{$color-grey-01};
    --fui-gray-200: #{$color-grey-02};
    --fui-gray-300: #{$color-grey-03};
    --fui-gray-400: #{$color-grey-04};
    --fui-gray-500: #{$color-grey-05};
    --fui-gray-600: #{$color-grey-06};
    --fui-gray-700: #{$color-grey-07};
    --fui-gray-800: #{$color-grey-08};
    --fui-gray-900: #{$color-grey-09};

    --fui-border: none; // applies to every single element
    --fui-border-radius: 0;
    --fui-border-color: #{$color-grey-02};

    --fui-focus-border-color: var($color-utility-neutral);
    --fui-focus-shadow: 0 0 0 3px #{color.change($color-utility-neutral, $saturation: 50%, $lightness: 50%)};

    --fui-field-gutter: var(--paragraph-break);
    --fui-field-min-width-2col: 15em;
    --fui-field-min-width-3col: 12em;
    --fui-field-min-width-4col: 10em;
    --fui-field-min-width-5col: 8em;

    --fui-loading-min-height: 1em;
    --fui-loading-height: 1em;
    --fui-loading-width: 1em;
    --fui-loading-margin-top: -0.5em;
    --fui-loading-margin-left: -0.5em;
    --fui-loading-border-width: 2px;
    --fui-loading-animation: loading 0.5s infinite linear;
    --fui-loading-left: 50%;
    --fui-loading-top: calc(50% - 1px);
    --fui-loading-z-index: 1;

    --fui-alert-padding: 1em;
    --fui-alert-line-height: 1.25em;
    --fui-alert-font-size: 0.875em;
    --fui-alert-font-weight: 500;
    --fui-alert-margin-bottom: 1em;
    --fui-alert-error-bg-color: #{lighten($color-utility-error, 85%)};
    --fui-alert-error-color: #{$color-utility-error-dark};
    --fui-alert-success-bg-color: #{lighten($color-utility-positive, 80%)};
    --fui-alert-success-color: #{$color-utility-positive-dark};

    --fui-btn-font-size: 0.875em;
    --fui-btn-line-height: 1.5;
    --fui-btn-display: inline-block;
    --fui-btn-text-align: center;
    --fui-btn-white-space: nowrap;
    --fui-btn-vertical-align: middle;
    --fui-btn-border-style: none;
    --fui-btn-text-decoration: none;
    --fui-btn-padding: var(--button-padding-vertical) var(--button-padding-horizontal);
    --fui-btn-border: var(--button-border-width) solid transparent;
    --fui-btn-radius: var(--button-border-radius);
    --fui-btn-font-weight: 500;
    --fui-btn-transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform;
    --fui-btn-transition-timing-function: #{$link-transition-timing-function};
    --fui-btn-transition-duration: #{$link-transition-duration};
    --fui-btn-opacity-disabled: var(--button-opacity-disabled);
    --fui-btn-container-padding: 1em 0 0 0;
    --fui-btn-container-margin: 0 -0.5em 0 -0.5em;
    --fui-btn-margin: 0 0.5em 0 0.5em;

    --fui-submit-btn-color: #fff;
    --fui-submit-btn-color-hover: #fff;
    --fui-submit-btn-spinner-color: #fff;
    --fui-submit-btn-bg-color: #{$color-secondary};
    --fui-submit-btn-bg-color-hover: #{$color-secondary-light};
    --fui-submit-btn-border-color: #{$color-secondary};
    --fui-submit-btn-border-color-hover: #{$color-secondary-light};

    --fui-link-transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform;
    --fui-link-transition-timing-function: #{$link-transition-timing-function};
    --fui-link-transition-duration: #{$link-transition-duration};
    --fui-link-text-decoration: underline;

    --fui-title-margin: 0 0 1em;
    --fui-title-font-size: 1.4em;
    --fui-title-font-weight: 700;

    --fui-page-title-margin: 0 0 1em;
    --fui-page-title-font-size: 1.125em;
    --fui-page-title-font-weight: 600;

    --fui-tabs-margin-bottom: 1em;
    --fui-tabs-font-size: 0.875em;
    --fui-tab-padding: 0.5em 1em;
    --fui-tab-margin-bottom: -1px;
    --fui-tab-border: 1px solid transparent;
    --fui-tab-active-font-weight: 500;
    --fui-tab-active-bg-color: #fff;
    --fui-tab-active-border-bottom-color: transparent;

    --fui-progress-height: 1.2em;
    --fui-progress-font-size: 0.8em;
    --fui-progress-font-weight: 500;
    --fui-progress-color: #fff;
    --fui-progress-bar-transition: width 0.3s ease;

    --fui-error-font-size: 0.875em;
    --fui-error-margin-top: 0.5em;

    --fui-label-font-size: 0.875em;
    --fui-label-line-height: 1.25;
    --fui-label-font-weight: 500;
    --fui-label-margin: 0.5em;
    --fui-label-error-color: #{$color-utility-error-dark};
    --fui-label-error-border-color: #{lighten($color-utility-error, 80%)};

    --fui-instructions-font-size: 0.875em;
    --fui-instructions-line-height: 1.25;
    --fui-instructions-margin: 0.5em;
    --fui-instructions-color: #{lighten($color-body-font, 40%)};

    --fui-check-font-size: 0.875em;
    --fui-check-line-height: 1.5;
    --fui-check-margin-bottom: 0.25em;
    --fui-check-margin-right: 1em;
    --fui-check-bg-color: #f0f1f4;
    --fui-check-label-padding-left: 1.8em;
    --fui-check-label-line-height: 1.5em;
    --fui-check-label-top: 5px;
    --fui-check-label-width: var(--fieldset-input-diameter);
    --fui-check-label-height: var(--fieldset-input-diameter);
    --fui-check-label-bg-color: #fff;
    --fui-check-label-transition: all 0.15s cubic-bezier(0.4,0,0.2,1);
    --fui-check-check-border-radius: 2px;
    --fui-check-check-bg-image: var(--fieldset-input-bg-image);
    --fui-check-check-bg-size: var(--fieldset-input-bg-size);
    --fui-check-bg-color-checked: #{$color-utility-neutral};
    --fui-check-border-color-checked: #{$color-utility-neutral};
    --fui-check-radio-border-radius: 50%;
    --fui-check-radio-bg-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E");
    --fui-check-radio-bg-size: var(--fieldset-input-bg-size);

    --fui-group-padding: 1em;

    --fui-input-font: var(--field-font-family);
    --fui-input-font-size: var(--field-font-size);
    --fui-input-line-height: var(--field-line-height);
    --fui-input-width: 100%;
    --fui-input-border: var(--field-border);
    --fui-input-border-radius: var(--field-radius);
    --fui-input-padding: var(--field-padding-vertical) var(--field-padding-horizontal);
    --fui-input-background-color: #{$color-grey-01};
    --fui-input-transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform;
    --fui-input-transition-timing-function: #{$link-transition-timing-function};
    --fui-input-transition-duration: #{$link-transition-duration};

    --fui-input-error-color: #{$color-utility-error-dark};
    --fui-input-error-border-color: #{lighten($color-utility-error, 80%)};
    --fui-input-error-box-shadow-focus: 0 0 0 3px hsla(0,83%,84%,.45);

    --fui-repeater-row-padding: 1em;
    --fui-repeater-row-margin-bottom: 1em;
    --fui-repeater-add-btn-padding-left: 2em;
    --fui-repeater-add-btn-top: 0.75em;
    --fui-repeater-add-btn-left: 0.75em;
    --fui-repeater-add-btn-width: 14px;
    --fui-repeater-add-btn-height: 14px;
    --fui-repeater-add-btn-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3Cpath fill='%23fff' d='M368 224H224V80c0-8.84-7.16-16-16-16h-32c-8.84 0-16 7.16-16 16v144H16c-8.84 0-16 7.16-16 16v32c0 8.84 7.16 16 16 16h144v144c0 8.84 7.16 16 16 16h32c8.84 0 16-7.16 16-16V288h144c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16z'/%3E%3C/svg%3E");
    --fui-repeater-remove-btn-top: 0;
    --fui-repeater-remove-btn-right: -14px;
    --fui-repeater-remove-btn-transform: translateY(-50%);
    --fui-repeater-remove-btn-border-radius: 50%;
    --fui-repeater-remove-btn-height: 0;
    --fui-repeater-remove-btn-width: 0;
    --fui-repeater-remove-btn-padding: 13px;
    --fui-repeater-remove-btn-text-indent: -9999px;
    --fui-repeater-remove-btn-icon-top: 50%;
    --fui-repeater-remove-btn-icon-left: 50%;
    --fui-repeater-remove-btn-icon-width: 9px;
    --fui-repeater-remove-btn-icon-height: 14px;
    --fui-repeater-remove-btn-icon-transform: translate(-50%,-50%);
    --fui-repeater-remove-btn-icon-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3Cpath fill='currentColor' d='m207.6 256 107.72-107.72c6.23-6.23 6.23-16.34 0-22.58l-25.03-25.03c-6.23-6.23-16.34-6.23-22.58 0L160 208.4 52.28 100.68c-6.23-6.23-16.34-6.23-22.58 0L4.68 125.7c-6.23 6.23-6.23 16.34 0 22.58L112.4 256 4.68 363.72c-6.23 6.23-6.23 16.34 0 22.58l25.03 25.03c6.23 6.23 16.34 6.23 22.58 0L160 303.6l107.72 107.72c6.23 6.23 16.34 6.23 22.58 0l25.03-25.03c6.23-6.23 6.23-16.34 0-22.58L207.6 256z'/%3E%3C/svg%3E");

    --fui-select-option-padding: 0.1em 0.4em;
    --fui-select-padding-right: 2em;
    --fui-select-bg-position: right 0.25em center;
    --fui-select-bg-size: 1.5em 1.5em;
    --fui-select-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='none'%3E%3Cpath d='m7 7 3-3 3 3m0 6-3 3-3-3' stroke='%239fa6b2' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");

    --fui-table-width: 100%;
    --fui-table-margin-bottom: 1em;
    --fui-table-border-collapse: collapse;
    --fui-table-row-padding: 0.2em;
    --fui-table-th-text-align: inherit;
    --fui-table-th-font-size: 0.75em;
    --fui-table-th-font-weight: 600;
    --fui-table-add-btn-padding-left: 2em;
    --fui-table-add-btn-top: 0.75em;
    --fui-table-add-btn-left: 0.75em;
    --fui-table-add-btn-width: 14px;
    --fui-table-add-btn-height: 14px;
    --fui-table-add-btn-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3Cpath fill='%23fff' d='M368 224H224V80c0-8.84-7.16-16-16-16h-32c-8.84 0-16 7.16-16 16v144H16c-8.84 0-16 7.16-16 16v32c0 8.84 7.16 16 16 16h144v144c0 8.84 7.16 16 16 16h32c8.84 0 16-7.16 16-16V288h144c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16z'/%3E%3C/svg%3E");
    --fui-table-remove-btn-border-radius: 50%;
    --fui-table-remove-btn-padding: 13px;
    --fui-table-remove-btn-text-indent: -9999px;
    --fui-table-remove-btn-top: 50%;
    --fui-table-remove-btn-left: 50%;
    --fui-table-remove-btn-width: 9px;
    --fui-table-remove-btn-height: 14px;
    --fui-table-remove-btn-transform: translate(-50%,-50%);
    --fui-table-remove-btn-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3Cpath fill='%23000' d='m207.6 256 107.72-107.72c6.23-6.23 6.23-16.34 0-22.58l-25.03-25.03c-6.23-6.23-16.34-6.23-22.58 0L160 208.4 52.28 100.68c-6.23-6.23-16.34-6.23-22.58 0L4.68 125.7c-6.23 6.23-6.23 16.34 0 22.58L112.4 256 4.68 363.72c-6.23 6.23-6.23 16.34 0 22.58l25.03 25.03c6.23 6.23 16.34 6.23 22.58 0L160 303.6l107.72 107.72c6.23 6.23 16.34 6.23 22.58 0l25.03-25.03c6.23-6.23 6.23-16.34 0-22.58L207.6 256z'/%3E%3C/svg%3E");

    --fui-signature-width: 100%;
    --fui-signature-height: 8em;
    --fui-signature-bg: #f9fafb;
    --fui-signature-remove-btn-top: 0;
    --fui-signature-remove-btn-right: -14px;
    --fui-signature-remove-btn-transform: translateY(-50%);
    --fui-signature-remove-btn-border-radius: 50%;
    --fui-signature-remove-btn-height: 0;
    --fui-signature-remove-btn-width: 0;
    --fui-signature-remove-btn-padding: 13px;
    --fui-signature-remove-btn-text-indent: -9999px;
    --fui-signature-remove-btn-icon-top: 50%;
    --fui-signature-remove-btn-icon-left: 50%;
    --fui-signature-remove-btn-icon-width: 9px;
    --fui-signature-remove-btn-icon-height: 14px;
    --fui-signature-remove-btn-icon-transform: translate(-50%,-50%);
    --fui-signature-remove-btn-icon-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg aria-hidden='true' data-prefix='far' data-icon='times' class='svg-inline--fa fa-times fa-w-10' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3Cpath fill='currentColor' d='m207.6 256 107.72-107.72c6.23-6.23 6.23-16.34 0-22.58l-25.03-25.03c-6.23-6.23-16.34-6.23-22.58 0L160 208.4 52.28 100.68c-6.23-6.23-16.34-6.23-22.58 0L4.68 125.7c-6.23 6.23-6.23 16.34 0 22.58L112.4 256 4.68 363.72c-6.23 6.23-6.23 16.34 0 22.58l25.03 25.03c6.23 6.23 16.34 6.23 22.58 0L160 303.6l107.72 107.72c6.23 6.23 16.34 6.23 22.58 0l25.03-25.03c6.23-6.23 6.23-16.34 0-22.58L207.6 256z'/%3E%3C/svg%3E");
    --fui-summary-padding: 1em
}

.fui-btn {
    transition: background 200ms ease-in-out;
}


.fui-alert {
    :where(&) *:last-child {
        margin-bottom: 0;
    }
}

.fui-page-container {
    margin-bottom: var(--paragraph-break);
}
