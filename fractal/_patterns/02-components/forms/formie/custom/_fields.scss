@use "../../../../00-settings" as *;
@use "../../../../02-components/forms/_form-mixins" as *;
@use "../../../../05-utilities/helpers/_utility-mixins" as *;
@use "../../../../02-components/buttons-and-links/_button-mixins" as *;

/* ----------------------------
Use if the form twig resets the classes
`resetClass: true`
------------------------------ */

[data-field-label] {
    @include field-label;
}

[id*="instructions"] {
    color: var(--fui-instructions-color);
    margin-top: #{$form-spacing * -1};
}

:where(.field-option) {
    @include fieldsetLabel;
}

.fui-checkbox [type="checkbox"] {
    &:before,
    &:after {
        content: none;
    }
}

[data-field-type="radio"],
[data-field-type="checkboxes"] {
    .field-input {
        align-self: center;
    }
}

.fui-repeater-add-btn,
.fui-table-add-btn {
    @include button;

    &:after {
        position: static;
        transform: none;
    }
}

.fui-table,
.fui-table th,
.fui-table td {
    border-color: transparent;
}

[data-field-type="signature"] {
    .field-input-wrapper {
        position: relative;
    }

    :where(canvas) {
        @include form-textbox-styles;
        width: 100%;
        display: block;
    }

    .fui-signature-clear-btn {
        position: absolute;
        right: $form-spacing;
        bottom: $form-spacing;
        border: var(--fui-signature-remove-btn-border, 1px solid var(--fui-border-color));
        border-radius: var(--fui-signature-remove-btn-border-radius, 50%);
        height: var(--fui-signature-remove-btn-height, 0);
        padding: var(--fui-signature-remove-btn-padding, 13px);
        position: absolute;
        right: var(--fui-signature-remove-btn-right, 14px);
        text-indent: var(--fui-signature-remove-btn-text-indent, -9999px);
        top: var(--fui-signature-remove-btn-top, 0);
        transform: var(--fui-signature-remove-btn-transform, translateY(-50%));
        width: var(--fui-signature-remove-btn-width, 0);

        &:after {
            content: "";
            background-image: var(--fui-signature-remove-btn-icon-bg-image, url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3Cpath fill='currentColor' d='m207.6 256 107.72-107.72c6.23-6.23 6.23-16.34 0-22.58l-25.03-25.03c-6.23-6.23-16.34-6.23-22.58 0L160 208.4 52.28 100.68c-6.23-6.23-16.34-6.23-22.58 0L4.68 125.7c-6.23 6.23-6.23 16.34 0 22.58L112.4 256 4.68 363.72c-6.23 6.23-6.23 16.34 0 22.58l25.03 25.03c6.23 6.23 16.34 6.23 22.58 0L160 303.6l107.72 107.72c6.23 6.23 16.34 6.23 22.58 0l25.03-25.03c6.23-6.23 6.23-16.34 0-22.58L207.6 256z'/%3E%3C/svg%3E"));
            background-repeat: no-repeat;
            display: block;
            height: var(--fui-table-remove-btn-height, 14px);
            left: var(--fui-table-remove-btn-left, 50%);
            position: absolute;
            top: var(--fui-table-remove-btn-top, 50%);
            transform: var(--fui-table-remove-btn-transform, translate(-50%,-50%));
            width: var(--fui-table-remove-btn-width, 9px);
        }

    }
}

.form-button-wrapper {
    display: flex;
    justify-content: space-between;
}

.form-submit-button {
    order: 1;
    align-self: flex-end;
}

.field-summary-block {
    margin-bottom: var(--paragraph-break);
}

.form-alert-success p {
    margin-bottom: 0;
}

.field--hidden-label :where(.field-label) {
    @include vis-hidden;
}
