@use "../../../../00-settings" as *;
@use "../../../../05-utilities/helpers/utility-mixins" as *;
@use "../../form-mixins" as *;
@use "../../../../01-base/typography/typography-mixins" as *;
@use "../../../../02-components/buttons-and-links/button-mixins" as *;


/* -----------------------------
Layout
----------------------------- */
.form-row,
.sub-field-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing*2;
    margin-bottom: $spacing*2;

    @include screen($bp992) {
        flex-wrap: nowrap;
        gap: $spacing*3;
    }
}

.field {
    flex: 1 1 100% !important;
    box-sizing: border-box;

    @include screen($bp560) {
        flex-basis: auto !important;
    }
}


/**
 * Multi-page breadcrumb styles
 */

//  TODO: make better
.freeform-pages {
    display: grid;
    grid-auto-flow: column;
    @include list-reset;
    display: flex;
    flex-flow: row nowrap;
    width: 100%;
    border: 1px solid $color-grey-03;

    li {
        background-color: $color-grey-01;
        min-height: $spacing*6;
        text-overflow: ellipsis;
        display: grid;
        grid-auto-flow: column;
        justify-content: center;
        align-items: center;
        padding: $spacing;
        flex: 1 1 auto;
        text-align: center;
        line-height: 1.12;
        font-size: 1.2rem;
        color: rgba($color-body-font, .5);

        @include screen($bp480) {
            font-size: 1.4rem;
            line-height: 1.25;
        }

        @include screen($bp768) {
            font-size: 1.6rem;
        }

        &:last-child::after {
            content: unset;
        }

        &.active {
            color: rgba($color-body-font, 1);
            font-weight: bold;
        }
    }
}


