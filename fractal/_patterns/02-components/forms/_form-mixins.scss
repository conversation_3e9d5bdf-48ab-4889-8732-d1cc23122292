
@use "../../00-settings" as *;

:root {
    --field-border: 1px solid #{$color-grey-01};
    --field-background: #{$color-grey-00};
    --field-text-color: #{$color-body-font};
    --field-padding-vertical: 0.75em;
    --field-padding-horizontal: 1em;
    --field-font-family: #{$primary-font};
    --field-font-size: var(--body-font-size);
    --field-line-height: 1.2;
    --field-radius: 2px;
    --field-vertical-whitespace: var(--paragraph-break);
    --fieldset-input-diameter: calc(var(--body-font-size) * 1.25);
    --fieldset-input-diameter-small: var(--centi-font-size);
    --fieldset-input-bg-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
    --fieldset-input-bg-size: 12px auto;
}

@mixin form-textbox-styles {
    border: var(--field-border);
    background-color: var(--field-background);
    color: var(--field-text-color);
    border-radius: var(--field-radius);
    // If you set a height here,
    // make sure you unset the height on <textarea>
    // see the forms/fields/_text.scss partial
}

@mixin form-textbox($size: 'default') {
    -webkit-appearance: none;
    display: block;
    width: 100%;
    font-family: var(--field-font-family);
    font-size: var(--field-font-size);
    line-height: var(--field-line-height);
    padding: var(--field-padding-vertical) var(--field-padding-horizontal);

    @if $size == 'small' {
        font-size: var(--centi-font-size);
        --field-padding-vertical: 0.4em;
        --field-padding-horizontal: 0.8em;
    }

    @if $size == 'large' {
        --field-padding-vertical: 0.8em;
    }
}

@mixin field-select($size: 'default') {
    @include form-textbox;
    @include form-textbox-styles;
    -moz-appearance: none;
    background-image:
        url(/assets/icon-system/icon_form_arrow-down.svg),
        url(/assets/icon-system/form_bg-fill.svg);
    background-repeat: no-repeat, repeat-Y;
    background-position: center right $form-spacing, center right;
    background-size: $form-spacing*3, $form-spacing*5;
    padding-right: $form-spacing*6.5;
}

@mixin field-label {
    font-weight: $fw-bold;
    margin-bottom: $form-spacing;
    line-height: normal;
    font-family: $primary-font;
}

@mixin field-label-required {
    content: "*";
    margin-left: 5px;
    color: $color-utility-error-dark;
}

@mixin field-message {
    margin: 0 0 $spacing;
    padding: $spacing $spacing*2;
    text-align: left;

    a {
        color: inherit;
    }
}


/* -------------------------------
Fieldsets Mixins
---------------------------------- */
@mixin fieldsetLabel {
    position: relative;
    cursor: pointer;
    display: grid;
    grid-auto-flow: column;
    gap: 8px;
    justify-content: start;
    font-weight: $fw-normal;
}

@mixin checkbox($small: false) {
    @include form-textbox-styles;
    display: block;
    width: var(--fieldset-input-diameter);
    height: var(--fieldset-input-diameter);

    @if $small {
        width: var(--fieldset-input-diameter-small);
        height: var(--fieldset-input-diameter-small);
    }
}

@mixin checkboxChecked($small: false) {
    @include checkbox($small);
    position: absolute;
    top: 0;
    background:
        $color-utility-neutral
        no-repeat
        center
        var(--fieldset-input-bg-image);
    background-size: var(--fieldset-input-bg-size);
    border-color: $color-utility-neutral;
    z-index: 1;
}

@mixin radio($small: false) {
    @include form-textbox-styles;
    display: block;
    border-radius: 100%;
    width: var(--fieldset-input-diameter);
    height: var(--fieldset-input-diameter);

    @if $small {
        width: var(--fieldset-input-diameter-small);
        height: var(--fieldset-input-diameter-small);
    }
}

@mixin radioChecked($small: false) {
    position: absolute;
    left: calc(var(--fieldset-input-diameter)/4);
    top: 50%;
    transform: translateY(-50%);
    width: calc(var(--fieldset-input-diameter)/2);
    height: calc(var(--fieldset-input-diameter)/2);
    border-radius: 100%;
    background: $color-white;
    z-index: 1;

    @if $small {
        width: calc(var(--fieldset-input-diameter-small)/2);
        height: calc(var(--fieldset-input-diameter-small)/2);
    }
}
