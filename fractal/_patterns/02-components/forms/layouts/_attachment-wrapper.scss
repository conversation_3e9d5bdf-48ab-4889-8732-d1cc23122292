@use "../../../00-settings" as *;
@use "../../../05-utilities/helpers/utility-mixins" as *;

.capsule-form-fields {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: start;

    .form-row {
        margin-bottom: 0;

        .field:first-child .field-input {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;

            &:focus {
                outline: 1px auto $color-utility-neutral;
            }
        }
    }

    [type="submit"] {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;

        /* offset the field label height
        margin-top: calc( 1em * 1.2 + #{$form-spacing});
        */
    }
}
