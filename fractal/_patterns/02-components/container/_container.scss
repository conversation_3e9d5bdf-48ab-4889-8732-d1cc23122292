
@use "../../00-settings" as *;

:root {
    --default-container: 88vw;
    --default-container-gutter: 6vw;
}

@include screen($bp768) {
    :root {
        --default-container: 92vw;
        --default-container-gutter: 4vw;
    }
}

@include screen($bp1600) {
    :root {
        --default-container: 1440px;
        --default-container-gutter: calc(50vw - (var(--default-container) / 2));
    }
}

@include screen($bp2400) {
    :root {
        --default-container-gutter: calc((#{$max-page-width} - var(--default-container)) / 2);
    }
}


.container {
    width: var(--default-container);
    margin-inline: auto;
}

/* [01] The bleed utility classes now live in the background colours file */

