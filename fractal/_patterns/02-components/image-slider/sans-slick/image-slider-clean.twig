<div class="slider js-generalist-slider" aria-label="gallery" tabindex="0">
	<ul class="slider__track" slider-track>

	{% for item in slider.sliderItems %}
		<li class="slider__item" slider-slide="{{ item.id }}">
		    <a href="{{ item.link }}">
		    	<img src="{{ item.img }}" alt="{{ item.img-alt-text }}">
		    </a>
		</li>

	{% endfor %}

	</ul>

	<div class="slider__dots" slider-controls>
		{% for item in slider.sliderItems %}
			<a href="{{ craft.app.request.segments |join('/') }}#slide-{{ loop.index }}" slider-dot-index="{{ loop.index0 }}" slide-target="{{ slider.id }}" type="button" role="tab" id="slider-controls" aria-controls="slider-controls" tabindex="{{ slider.id }}">
				<span>{{ loop.index }}</span>
			</a>
		{% endfor %}
	</div>

	<button class="slider__arrows previous">
		<span class="-vis-hidden">Previous</span>
		{# {{ svg('@webroot/assets/icon_chevron-left.svg') }} #}
	</button>

	<button class="slider__arrows next">
		<span class="-vis-hidden">Next</span>
		{# {{ svg('@webroot/assets/icon_chevron-right.svg') }} #}
	</button>
</div>

{# {% js %} #}
<script>
	window.addEventListener("load", function () {
		var generalistSliders = document.querySelectorAll('.js-generalist-slider');

		generalistSliders.forEach(sliderContainer => {
			initialiseSlider(sliderContainer);
		});

		function initialiseSlider( slider ) {
			let sliderContainer = slider;
			let sliderTrack = sliderContainer.querySelector('[slider-track]');
			let sliderControls = sliderContainer.querySelector('[slider-controls]');

			if(!!sliderTrack) {
				let sliderOptions = {
					root: sliderTrack,
					rootMargin: '0px',
					threshold: 0.9
				}

				let activeSlide = (entries, observer) => {
					entries.forEach(entry => {
						// Each entry describes an intersection change for one observed

						let activeControl = sliderControls.querySelector('[slide-target="' + entry.target.getAttribute('slider-slide') + '"]');

						if(entry.isIntersecting === true) {
							entry.target.classList.add('active');
							activeControl.classList.add('active');
						} else {
							entry.target.classList.remove('active');
							activeControl.classList.remove('active');
						}
					});
				};

				let sliderControlItems = sliderContainer.querySelectorAll('[slider-dot-index]');

				// attach an onClick event to each slider control button/link
				sliderControlItems.forEach(controlTrigger => {
					controlTrigger.addEventListener("click", showSlide, false);
				});


				let observer = new IntersectionObserver(activeSlide, sliderOptions);

				let slideItems = sliderTrack.querySelectorAll('[slider-slide]');

				// attach an Intersection Observer to each slider item
				slideItems.forEach(slide => {
					observer.observe(slide);
				});

				let buttonNext = sliderContainer.querySelector('.next');
				if(!!buttonNext) {
					buttonNext.addEventListener("click", nextSlide, false);
				}

				let buttonPrevious = sliderContainer.querySelector('.previous');
				if(!!buttonPrevious) {
					buttonPrevious.addEventListener("click", prevSlide, false);
				}
			}
		}


		function showSlide(e) {
			if (e instanceof Event) {
				e.preventDefault();
				e.stopPropagation();
			}

			let slideButton = e.target;

			// Get the index of the slider control button
			let buttonIndex = slideButton.getAttribute('slider-dot-index');

			let parentSlider = slideButton.parentNode.parentNode;
			let parentTrack = parentSlider.querySelector('[slider-track]');

			// set the slider element left scroll to show the target slide
			parentTrack.scrollLeft = buttonIndex * parseInt(parentTrack.querySelector('[slider-slide]').getBoundingClientRect().width);
		}


		// Auto scroll — every 5 seconds scroll to the next slide
		function nextSlide(e) {
			let $this = e.target;
			let $sliderTrack = $this.closest('.js-generalist-slider').querySelector('[slider-track]');
			let slideItemWidth = $sliderTrack.querySelector('[slider-slide]').getBoundingClientRect().width;


			// if slider has reached the last slide
			if( $sliderTrack.scrollLeft + slideItemWidth >= parseInt($sliderTrack.scrollWidth - 1)) {
				// Go back to slider one
				$sliderTrack.scrollLeft = 0;
			} else {
				$sliderTrack.scrollLeft = $sliderTrack.scrollLeft + slideItemWidth;
			}
		}


		function prevSlide(e) {
			let $this = e.target;
			let $sliderTrack = $this.closest('.js-generalist-slider').querySelector('[slider-track]');
			let slideItemWidth = $sliderTrack.querySelector('[slider-slide]').getBoundingClientRect().width;

			// if slider has reached the last slide
			if( $sliderTrack.scrollLeft == 0) {
				// Go back to slider one
				$sliderTrack.scrollLeft = (parseInt($sliderTrack.scrollWidth) - slideItemWidth);
			} else {
				$sliderTrack.scrollLeft = $sliderTrack.scrollLeft - slideItemWidth;
			}
		}
	});
</script>
{# {% endjs %} #}

