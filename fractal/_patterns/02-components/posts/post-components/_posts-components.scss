
@use "../../../00-settings" as *;
@use "../../../05-utilities/helpers/utility-mixins" as *;

/* --------------------------------------
    Post details
    Post macro for author and date.
--------------------------------------- */
.post-details {
    margin-bottom: 0;
    color: $color-grey-04;

    .post-detail {
        &__icon {
            width: 0.8em;
            height: 0.8em;

            @include set-svg-colour($color-grey-04);
        }
    }
}

/* --------------------------------------
    Post details
    Post macro for author and date.
--------------------------------------- */
@mixin post-inline-item-list {
    display: inline-grid;
    grid-auto-flow: column;
    align-items: center;
    margin-bottom: 0;
    color: $color-grey-04;
    column-gap: 0.5em;

    svg {
        height: 0.9em;
        width: 0.9em;

        @include set-svg-colour($color-grey-04);
    }
}



/* --------------------------------------
    Post categories
--------------------------------------- */
.post-single-categories {
    @include post-inline-item-list;

    &__label {
        @include vis-hidden;
    }
}

/* --------------------------------------
    Post tags
--------------------------------------- */
.post-single-tags {
    @include post-inline-item-list;

    &__label {
        @include vis-hidden;
    }

    &__item {
        position: relative;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        margin-left: 0.75em;

        &:before {
            content: "";
            position: absolute;
            top: 0;
            right: 100%;
            width: 0;
            height: 0;
            border-style: solid;
            border-color: transparent $color-grey-01 transparent transparent;
            border-width: 0.75em;
        }

        &:after {
            content: "";
            position: absolute;
            top: calc(50% - 2px);
            right: calc(100% - 2px);
            height: 4px;
            width: 4px;
            border-radius: 4px;
            background-color: $color-white;
        }
    }

    span {
        display: inline-block;
        margin-right: 0.5em;
    }
}


