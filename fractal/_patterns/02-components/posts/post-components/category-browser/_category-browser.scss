
@use "../../../../00-settings" as *;
@use "../../../../05-utilities/helpers/utility-mixins" as *;


.category-browser__nav {
    margin-bottom: 1.5em;
}

.category-browser__title {
    line-height: normal;
}

.category-browser__list__title {
    @include font-set('primary');
    color: $color-body-font;
    font-size: var(--body-font-size);
    font-weight: $fw-bold;
    margin-block: 0;
}

.category-browser__list {
    @include list-reset;
    margin: 0;

    /* - Depth 1 - */
    &.depth1 {
        li {
            display: block;
        }
    }

    /* - Depth 2 - */
    &.depth2 {
        margin-left: 0.5em;
    }
}

.category-browser__item {
    border-bottom: 1px solid $color-grey-03;

    &:last-child {
        border-bottom: none;
    }

    a {
        text-decoration: none;
        display: flex;
        justify-content: space-between;
        padding-top: 1em;
        padding-bottom: 1em;
        opacity: 0.85;
        line-height: normal;
        transition:
        color 250ms ease-in-out,
        background-color 250ms ease-in-out;

        &:hover,
        &:focus,
        &:active {
            opacity: 1;

            .category-links__item__title {
                text-decoration: underline;
            }
        }
    }

    /* Active / on / current */
    &.current-item > a {
        opacity: 1;
        font-weight: $fw-bold;

        &:hover,
        &:focus,
        &:active {
            opacity: 0.85;
        }
    }
}



