
@use "../../../00-settings" as *;

/* ----------------------------------------------
    Post hub - post and image lazy loading
------------------------------------------------- */
.post-grid__item--hidden,
.post-grid--unfiltered .post-grid__item--hidden,
.post-grid--filtered .post-grid__item--filtered-hidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}

.post-grid--unfiltered .post-grid__item--animate {
    animation: fadeIn 300ms ease;
    animation-fill-mode: forwards;
}

.post-grid--filtered .post-grid__item--filtered-pre-visible {
    opacity: 0;
}

.post-grid--unfiltered .post-grid__item--animate-grow,
.post-grid--filtered .post-grid__item--filtered-visible {
    animation: growInFade 350ms ease;
    animation-fill-mode: forwards;
}

.post-grid__item__image-loading .post-grid__img {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}

.post-grid__img-link {
    background-color: $color-grey-01;
    position: relative;
    overflow: hidden;
    aspect-ratio: 1200 / 630;
    display: block;
}

.post-grid__img-fallback {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
}


.post-grid__img-fallback {
    display: block;
    object-fit: contain;
    margin: auto;
    width: 66.66%;
    height: auto;
}
