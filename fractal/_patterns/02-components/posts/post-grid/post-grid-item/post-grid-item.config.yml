title: Post Grid / Gallery Item
status: ready
notes: The post grid requires classes to do the item filtering and lazy image loading. A couple of broken things here that prevent this from working currently (map filter breaks Fractal Twig parser + the macro import). Ideally, once these work, create a few variants where banner is false, and another where siteIdentityImage is false.
context:
  fractal: true
  entry:
    title: Suspendisse eget gravida erat
    url: https://www.google.com/
    excerpt: Suspendisse eget gravida erat. Morbi viverra scelerisque tellus, eu scelerisque eros. Praesent ac iaculis metus, ac iaculis erat. Nunc et velit sit amet massa accumsan commodo.

  # Craft Query Variables
  postCategoryList: category-nifty-things
  banner: true
  bannerSmall: https://via.placeholder.com/50x33
  bannerLarge: https://via.placeholder.com/600x400
  siteIdentityImage: true
  siteIdentityLarge: https://via.placeholder.com/600x400/48b183/ffffff?text=600x400%20(siteIdentity)
  craft:
    app:
      sites:
        currentSite:
          baseUrl: 'https://www.bunnysites.com/'
