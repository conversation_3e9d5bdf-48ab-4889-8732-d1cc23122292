<!-- Template: {{ _self }} -->

{% if fractal is not defined %}
    {# NOTE: The map filter here breaks in Fractal currently #}
    {% set postCategoryList = entry.postCategory.all() ?? false ? entry.postCategory.all()|map(c => c.title|lower|kebab)|join(',')  %}


    {% set cardImage = entry.banner.one() ?? siteIdentity.defaultSocialMediaImage.one() ?? false %}

{% endif %}

<article
    class="post-grid__item"
    data-categories="{{ postCategoryList|lower ?: null }}"
    data-post="1"
>
    <div class="post-grid__item__inner">

        <div class="post-grid__item__img-wrap">
            <a href="{{ entry.url }}" class="post-grid__img-link">
                {% if cardImage ?? false and cardImage|length > 1 %}
                    {{ cardImage.getImg({ width: 1200, height: 630, format: 'webp', }, ['1.5x', '2x', '3x'])|attr({
                            class: 'post-grid__item__image',
                            loading: 'lazy',
                            role: cardImage.alt ? null : 'presentation'
                        })
                    }}
                {% else %}
                    <img src="{{ craft.app.sites.currentSite.baseUrl }}assets/logo.svg" alt="" class="post-grid__img-fallback" loading="lazy"/>
                {% endif %}
            </a>
        </div>

        <div class="post-grid__item__body">

            <h2 class="post-grid__item__title">
                <a href="{{ entry.url }}">
                    <span class="-vis-hidden">Read more about </span>{{ entry.title|truncate(50) }}
                </a>
            </h2>

            <p class="post-grid__item__content">{{ entry.excerpt }}</p>

        </div>

        <footer class="post-grid__item__footer">

            <a href="{{ entry.url }}" class="post-grid__item__more-link button button--block">View post<span class="-vis-hidden"> about {{ entry.title|truncate(50) }}</span></a>

            {# NOTE: Broken import in Fractal currently #}
            {% import 'posts/_includes/macros-blog' as blogMacros %}
            {{ blogMacros.postDetails(entry, 'jS M') }}

        </footer>

    </div>
</article>
