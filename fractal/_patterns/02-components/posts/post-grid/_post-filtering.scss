
@use "../../../00-settings" as *;

/* ----------------------------------------------
    Post hub - Filter Lists
------------------------------------------------- */
.post-grid-filterlist {
    list-style-type: none;
    padding: 0;
    font-size: 0.85em;
    text-transform: uppercase;

    @include screen($bp560) {
        text-align: center;
    }

    @media screen and (min-width: 37.5em) {/* 600px */
        margin-left: -2px;
        margin-right: -2px;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    &__item {
        @include screen($bp560) {
            display: inline-block;
        }
    }

    .field-element {
        @include screen($bp560) {
            margin-bottom: 0;
        }
    }

    .button {
        @media screen and (min-width: 37.5em) {/* 600px */
            margin: 2px;
        }

        &:after {
            @media screen and (min-width: 37.5em) {/* 600px */
            content: none;
            }
        }

        &.button-inactive {
            @media screen and (min-width: 37.5em) {/* 600px */
                background-color: transparent;
                border-color: transparent;
                color: $color-grey-04;
            }
        }

        &:hover,
        &:focus {
            @media screen and (min-width: 37.5em) {/* 600px */
                background-color: $color-grey-06;
                border-color: $color-grey-06;
                color: $color-white;
            }
        }
    }
}

.field-element.field-element--post-grid-filterlist {
    margin-bottom: 35px
}

.js-post-grid-filterlist .js-post-grid-filterlist__select {
    margin-bottom: 24px;
}

.js-post-grid-filterlist__select,
.js-post-grid-filter-tag__select,
.post-grid-filter-cat__select {
    @include screen($bp560) {
        display: none !important;
    }
}

/* Hides the list if js is working, defaults to working list if not */
@media screen and (max-width: $bp559) {
    .js .js-post-grid-filterlist .post-grid-filterlist {
        display: none;
    }

    .js .js-post-grid-tag-filterlist .post-grid-filterlist {
        display: none;
    }
}

// Mobile dropdown styles
.post-grid-filter-cat__select {
    margin-bottom: $spacing*3;
}
