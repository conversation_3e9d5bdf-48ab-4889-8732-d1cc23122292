
@use "../../../../00-settings" as *;
@use "../../../../05-utilities/helpers/utility-mixins" as *;

.post-tile {
    @include screen($bp560) {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
    }
}

.post-tile-item {
    color: inherit;
    text-decoration: none;
    overflow: hidden;
    border: none;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    &:hover,
    &:focus,
    &:active {
        color: inherit;

        .post-tile-item__picture img {
            transform:scale(1.1)
        }
    }

    &:focus,
    &:active {
        .post-tile-item__picture:after {
            right: 0;
        }
    }

    @include screen($bp560) {
        margin-bottom: 0;
    }

    &__gallery {
        @include screen($bp560) {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
    }

    &__picture {
        flex: 0 0 40%;
        max-width: calc(40% - 15px);
        margin-right: 15px;

        display: block;
        position: relative;
        overflow: hidden;
        @include fallback-logo-image;

        @supports(aspect-ratio: 3/2) {
            aspect-ratio: 3/2;
        }

        &:after {
            content: '';
            background: rgba($color-black, 0.5);
            background-image: url(/assets/icon-system/icon_link-white.svg);
            background-size: 1em;
            background-repeat: no-repeat;
            background-position: center;

            position: absolute;
            left: 0;
            right: 100%;
            top: 0;
            bottom: 0;

            opacity: .9;
            z-index: 1;

            transition:
                right 250ms ease;

            display: flex;
            justify-content: center;
            align-items: center;
        }

        &:hover,
        &:focus,
        &:active {
            &:after {
                right: 0;
            }
        }

        img {
            transition:
                transform 250ms ease;

            @supports(aspect-ratio: 3/2) {
                object-fit: cover;
                height: 100%;
                width: 100%;
            }
        }
    }


    &__copy {
        flex: 0 0 60%;
    }

    &__title {
        font-size: var(--body-font-size);
        margin-bottom: 0;
    }

    .post-details {
        margin-bottom: 0;
    }
}
