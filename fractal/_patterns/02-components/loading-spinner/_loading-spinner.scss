@use "../../00-settings" as *;

.loading-spinner {
    @keyframes spinner {
        0% {
            transform: translate3d(-50%, -50%, 0) rotate(0deg);
        }
        100% {
            transform: translate3d(-50%, -50%, 0) rotate(360deg);
        }
    }

    & .spinner {
        display: block;
        position: relative;
        width: 64px;
        height: 64px;

        margin-inline: auto;

        &:before {
            animation: 1.5s linear infinite spinner;
            animation-play-state: inherit;
            border: solid 5px $color-grey-02;
            border-bottom-color: $color-primary;
            border-radius: 50%;
            content: "";
            width: 64px;
            height: 64px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate3d(-50%, -50%, 0);
            will-change: transform;
        }
    }
}