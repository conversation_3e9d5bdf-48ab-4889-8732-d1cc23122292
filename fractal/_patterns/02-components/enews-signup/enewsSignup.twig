<div class="section enews bg-default-tint-01">
    <div class="container">

        {% if enews.richtextBasicLinks ?? false %}
        <h2>{{ enews.title }}</h2>
        {% endif %}

        {% set form = craft.formie.forms({ handle: 'enewsSignup' }).one() %}
        {% include '02_components/form' with {
            selectedForm: form,
            'formComponent': {
                modifierClass: 'enews__form',
            },
            styleCapsule: true
        } %}

        {% if enews.richtextBasicLinks ?? false %}
            <p class="enews__footnote">{{ enews.richtextBasicLinks|striptags('<br><a><strong><b><emphasis><em>')|raw }}</p>
        {% endif %}

        {% include 'patterns/02-components/lists/socialList.twig' with {
            socialList: {
                title: 'Connect',
            }
        } %}

    </div>
</div>

