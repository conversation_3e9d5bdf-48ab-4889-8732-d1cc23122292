
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.image-gallery__figure {
    position: relative;
    display: block;
    overflow: hidden;
    margin: 0;
}

.image-gallery__figure__caption {
    text-align: center;
    margin-top: 0;
    padding: $spacing*.5;
    line-height: var(--compact-line-height);
}

.image-gallery__slider {
    .splide__slide {
        img {
            user-select: none;
        }

        &.active img {
            animation: fadeIn 350ms ease-in;
	        animation-fill-mode: forwards;
        }
    }

    .image-gallery__figure {
        margin-bottom: 0;
    }

    &.columns-1 {
        .image-gallery__figure,
        .image-gallery__image {
            width: 100%;
        }
    }

}

.image-gallery__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;

    &.columns-2,
    &.columns-4 {
        grid-template-columns: repeat(2, 1fr);
    }

    &.columns-3,
    &.columns-5,
    &.columns-6  {
        grid-template-columns: repeat(3, 1fr);
    }

    &.columns-4 {
        @include screen($bp768) {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    &.columns-5 {
        @include screen($bp768) {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    &.columns-6 {
        @include screen($bp768) {
            grid-template-columns: repeat(6, 1fr);
        }
    }

    figure,
    img {
        width: 100%;
    }

    .image-gallery__figure  {
        position: relative;

        &:hover,
        &:focus,
        &:active {
            figcaption {
                opacity: 1;
            }
        }
    }
}

.popup-gallery {
    text-decoration: none;
    @include popup-signifier;
}

.image-gallery__image {
    border-radius: $radius-default;
}

:where(.image-gallery__singular) .image-gallery__image {
    width: 100%;
}
