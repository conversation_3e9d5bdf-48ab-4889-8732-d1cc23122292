{# {% include '../../templates/02_components/_layouts/tab-nav.twig' with { modifierClass: 'sidebar-widget' } %} #}



<div class="tab-exp-nav js-tab-expando-nav">
	<div class="tab-exp-nav__list js-tabs-list" role="tablist" aria-label="Programming Languages">
		<button class="tab-exp-nav__list__btn" role="tab" aria-selected="true" id="js">
			JavaScript
		</button>
		<button class="tab-exp-nav__list__btn" role="tab" aria-selected="false" id="ruby">
			Ruby
		</button>
		<button class="tab-exp-nav__list__btn" role="tab" aria-selected="false" id="php">
		PHP
		</button>
	</div>

	<div class="tab-exp-nav__panel" role="tabpanel" aria-labelledby="js">
		<div class="tab-exp-nav__panel__contents">
			<h2 class="tab-exp-nav__panel__contents__heading">Javascript</h2>
			<p>JavaScript is great!</p>
			<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Repellat asperiores rerum assumenda dolorem dicta, excepturi veniam! Suscipit blanditiis quia reiciendis rerum cupiditate, neque cumque vel, illum natus iure minus obcaecati?</p>
		</div>
		<div class="tab-exp-nav__panel__more-btn" aria-role="button" aria-labelledby="js">Read more</div>
	</div>
	<div class="tab-exp-nav__panel" role="tabpanel" aria-labelledby="ruby">
		<div class="tab-exp-nav__panel__contents">
			<h2 class="tab-exp-nav__panel__contents__heading">Ruby</h2>
			<p>Ruby is great</p>
			<p>Lorem ipsum dolor sit amet, suscipit blanditiis quia reiciendis rerum cupiditate, neque cumque vel, illum natus iure minus obcaecati?</p>
		</div>
		<div class="tab-exp-nav__panel__more-btn" aria-role="button" aria-labelledby="ruby">Read more </div>

	</div>
	<div class="tab-exp-nav__panel" role="tabpanel" aria-labelledby="php">
		<div class="tab-exp-nav__panel__contents">
			<h2 class="tab-exp-nav__panel__contents__heading">PHP</h2>
			<p>PHP is great!</p>
			<p>Lorem ipsum dolor sit ametsuscipit obcaecati?</p>
		</div>
		<div class="tab-exp-nav__panel__more-btn" aria-role="button" aria-labelledby="php">Read more about php</div>

	</div>

</div>

{# {% js %} #}

<script>
	// Fancy content tabs that are expando-like on mobile

	(function() {

		// Prep any tab/expandos at the first chance.
		if (document.readyState === 'complete') {
			init();
		} else {
			document.addEventListener("DOMContentLoaded", init);
		}

		let timer = 0;

		function init() {
			const tabs = document.querySelector('.js-tab-expando-nav');
			if (!tabs) return;

			const tabButtons = tabs.querySelectorAll('[role="tab"]');
			const tabPanels = Array.from(tabs.querySelectorAll('[role="tabpanel"]'));
			const tabPanelsContent = Array.from(tabs.querySelectorAll('[role="tabpanel"] .tab-exp-nav__panel__contents'));
			const tabPanelOpen = tabs.querySelectorAll('.tab-exp-nav__panel__more-btn');

			// Collect panel heights for expando animations.
			// It's important this happens before they're hidden or we'll get the wrong size!
			tabPanelsContent.forEach(panel => {
				panelHeight = panel.scrollHeight + "px";
				panel.style.height = panelHeight;
			});

			tabButtons.forEach(button => {
				button.addEventListener('click', handleTabClick);
			});

			tabPanelOpen.forEach(button => {
				button.addEventListener('click', panelExpand);
			});

			// Perform tab/expando switcheroo on a debounced resize.
			window.addEventListener("resize", function() {
				clearTimeout(timer);
				timer = setTimeout(resize, 250);
			});

			// Also do it immediately.
			resize();

			function resize() {
				// The 'mobile' breakpoint.
				if(window.innerWidth < 768) {
					tabPanelsContent.forEach(panel => {
						panel.classList.add('tabpanel--closed');
					});

					tabPanels.forEach(panel => {
						panel.hidden = false;
					});

					tabButtons.forEach(button => {
						button.hidden = true;
					});

					tabPanelOpen.forEach(button => {
						button.classList.remove('tab-exp-nav__panel__more-btn--hide');
					});

				} else {
					// Desktop things.

					// Re-show all the panels.
					tabPanelsContent.forEach(item => {
						item.classList.remove('tabpanel--closed');
					});

					tabPanels.forEach(panel => {
						panel.hidden = true;
					});

					// Show all the tabs.
					tabButtons.forEach(button => {
						button.hidden = false;
					});

					// find the associated tabPanel and show it!
					const openPanel = document.querySelector('[aria-selected="true"]');
					const buttonId = openPanel.id;
					const tabPanel = document.querySelector('[role=tabpanel][aria-labelledby=' + buttonId + ']');
					tabPanel.hidden = false;
				}
			}

			function panelExpand(event) {
				const buttonId = event.currentTarget.getAttribute('aria-labelledby');
				const currentPanel = document.querySelector('[role=tabpanel][aria-labelledby=' + buttonId + ']');

				currentPanel.querySelector('.tab-exp-nav__panel__contents').classList.remove('tabpanel--closed');
				event.currentTarget.classList.add('tab-exp-nav__panel__more-btn--hide');
			}

			function handleTabClick(event) {
				// hide all tab panels

				tabPanels.forEach(panel => {
					panel.hidden = true;
				});
				// mark all tabs as unselected
				tabButtons.forEach(tab => {
					// tab.ariaSelected = false;
					tab.setAttribute('aria-selected', false);
				});
				// mark the clicked tab as selected
				event.currentTarget.setAttribute('aria-selected', true);

				// find the associated tabPanel and show it!
				const buttonId = event.currentTarget.id;
				const tabPanel = document.querySelector('[role=tabpanel][aria-labelledby=' + buttonId + ']');
				tabPanel.hidden = false;
			}
		}
	})();

</script>

{# {% endjs %} #}
