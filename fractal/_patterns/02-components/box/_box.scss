
@use "../../00-settings" as *;
@use "box-mixins" as *;

.box {
    @include box;
}

.box--small {
    @include box('small');
}

.box--large {
    @include box('large');
}

.box-lined {
    @include box-lined;
}

.box-lined--grey {
    @include box-lined($color-grey-06);
}

.box-lined--primary {
    @include box-lined($color-primary);
}

.box-lined--secondary {
    @include box-lined($color-secondary);
}


.box-lined-bottom {
    @include box-lined($accentBorder: 'bottom');

    &.box-lined--grey {
        @include box-lined($color-grey-06, 'bottom');
    }

    &.box-lined--primary {
        @include box-lined($color-primary, 'bottom');
    }

    &.box-lined--secondary {
        @include box-lined($color-secondary, 'bottom');
    }
}
