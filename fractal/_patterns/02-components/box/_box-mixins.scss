
@use "../../00-settings" as *;

:root {
    --box-padding: var(--body-font-size);
    --box-padding-regular: var(--box-padding);
    --box-padding-large: var(--box-padding);
}

@include screen($bp360) {
    :root {
        --box-padding: 24px;
        --box-padding-regular: var(--box-padding);
        --box-padding-large: var(--box-padding);
    }
}

@include screen($bp768) {
    :root {
        --box-padding-regular: 32px;
        --box-padding-large: 40px;
    }
}


@mixin box-lined($colour: $color-primary, $accentBorder: 'left') {
    border: $border;
    border-radius: $radius-default;
    margin-bottom: $fs-body;

    @if $accentBorder == 'left' {
        border-left: $radius-default solid $colour;
    }

    @if $accentBorder == 'bottom' {
        border-bottom: $radius-default solid $colour;
    }
}

@mixin box($size: 'regular') {
    padding: var(--box-padding);

    > *:last-child {
        margin-bottom: 0;
    }

    @if $size == 'regular' {
        padding: var(--box-padding-regular);
    }

    @if $size == 'large' {
        padding: var(--box-padding-large);
    }
}

@mixin box-lined($colour: $color-primary, $accentBorder: 'left') {
    border: 1px solid $color-grey-01;
    border-radius: $radius-default;
    margin-bottom: $fs-body;

    @if $accentBorder == 'left' {
        border-left: $radius-default solid $colour;
    }

    @if $accentBorder == 'bottom' {
        border-bottom: $radius-default solid $colour;
    }
}
