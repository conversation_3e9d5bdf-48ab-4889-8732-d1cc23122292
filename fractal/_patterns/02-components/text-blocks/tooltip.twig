<p>
	<span class="tooltip {{ tooltip.modifier }}">
		<span class="tooltip__title">{{ tooltip.title }}</span>
		<span class="tooltip__icon">
			<!-- icon_i taken from icon system -->
			<svg class="icon_i" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M16.609 3.205c.071.817-.161 1.545-.697 2.184-.536.639-1.213.994-2.03 1.066-.818.071-1.546-.161-2.185-.697-.639-.536-.994-1.213-1.066-2.03-.072-.83.16-1.562.696-2.194A2.902 2.902 0 0 1 13.358.477a2.902 2.902 0 0 1 2.184.688c.638.53.994 1.21 1.067 2.04zm-1.12 15.097l1.779-.156c-1.222 3.339-3.183 5.126-5.883 5.362-1.092.096-1.934-.061-2.526-.471-.592-.41-.927-1.067-1.006-1.972-.046-.528.101-1.476.443-2.844l1.581-6.263c.123-.485.179-.797.167-.938-.013-.141-.054-.241-.124-.3-.07-.06-.176-.082-.317-.07-.599.052-1.236.877-1.913 2.475L6 13.273c1.228-3.41 3.22-5.235 5.98-5.476.974-.086 1.752.086 2.333.514.582.429.91 1.06.982 1.894.054.611-.102 1.601-.467 2.97l-1.556 5.747c-.224.813-.328 1.319-.31 1.519.**************.*************.194.089.335.076.305-.026.673-.325 1.102-.895.429-.57.746-1.107.95-1.61z" fill="#FFF" fill-rule="nonzero"/></svg>
		</span>
		 <div class="tooltip__content">
			<div class="tooltip__header">
				<p class="tooltip__header__title">{{ tooltip.title }}</p>
			</div>
			<div class="tooltip__body">
				{{ tooltip.content }}
			</div>
			<div class="tooltip__footer">
				<p>
					{{ tooltip.footer }}
				</p>
			</div>
		</div>
	</span>
</p>

<script>
	var windowWidth = $(window).width();
	var windowHeight = $(window).height();
	var hoverBreakpoint = 1080;
	var $currentTooltip;

	// fluid tooltip function
	function fnTooltipFluid() {
		$currentTooltip = $(this);
		console.log('$currentTooltip: ' + $currentTooltip);

		// tooltip offset - position relative to document
		var offset = $currentTooltip.offset();

		// tooltip width
		var tooltipWidth = $currentTooltip.width();
		console.log('tooltipWidth: ' + tooltipWidth);

		// tooltip height
		var tooltipHeight = $currentTooltip.height();

		// tooltip content width
		var tooltipContentInitialWidth = $currentTooltip.find('.tooltip__content').width(); // note width given sans-padding
		var tooltipContentPaddingLeft = parseInt($currentTooltip.css("padding-left"));
		var tooltipContentPaddingRight = parseInt($currentTooltip.css("padding-right"));
		var tooltipContentWidth = (tooltipContentInitialWidth + tooltipContentPaddingLeft + tooltipContentPaddingRight);
		console.log('tooltipContentWidth: ' + tooltipContentWidth);

		// tooltip min-content width
		var tooltipContentMinWidth = parseInt($currentTooltip.find('.tooltip__content').css("min-width"));
		console.log('tooltipContentMinWidth: ' + tooltipContentMinWidth);

		// tooltip content height
		var tooltipContentHeight = $currentTooltip.find('.tooltip__content').height();


		if( $currentTooltip.hasClass('tooltip--icon') ) {
			// if the tooltip has an icon

			if (tooltipWidth >= tooltipContentMinWidth) {
				// the tooltip's width is greater than or equal to it's content's width

				// sit the tooltip's anchor on the right side, under the icon
				$currentTooltip.addClass('tooltip--right');
				$currentTooltip.removeClass('tooltip--left');

			} else if ( tooltipWidth < tooltipContentMinWidth && offset.left < tooltipContentMinWidth){
				// the tooltip is less wide than it's minimum content width
				// and
				// the tooltip is within the minimum content's width close to the left side of the browser window

				// sit the tooltips anchor on the left side, under the icon
				$currentTooltip.addClass('tooltip--left');
				$currentTooltip.removeClass('tooltip--right');

			} else if ( tooltipWidth < tooltipContentMinWidth && offset.left >= ($(window).width() - tooltipContentMinWidth) ){
				// the tooltip is less wide than it's minimum content width
				// and
				// the tooltip is within the minimum content's width close to the right side of the browser window

				// sit the tooltip's anchor on the right side, under the icon
				$currentTooltip.addClass('tooltip--right');
				$currentTooltip.removeClass('tooltip--left');
			};
		} else {
			// if the tooltip doesn't have an icon
			if ( tooltipWidth < tooltipContentMinWidth && offset.left >= ($(window).width() - tooltipContentMinWidth) ){
				// the tooltip is less wide than it's minimum content width
				// and
				// the tooltip is within the minimum content's width close to the right side of the browser window

				 // anchor it right, stretching out leftwards
				$currentTooltip.addClass('tooltip--right');
				$currentTooltip.removeClass('tooltip--left');
			} else {
				// Anchor the tooltip left, stretching out rightwards
				$currentTooltip.addClass('tooltip--left');
				$currentTooltip.removeClass('tooltip--right');
			};
		};
	};

	// set the tooltip fluid function to fire on hover on desktop and click/press on touchscreens
	if ($(window).width() >= hoverBreakpoint) {
		// for tooltips activated on hover
		$('.tooltip--fluid').on("mouseover", fnTooltipFluid );
	} else {
		// for tooltips activated on press (touchscreens)
		$('.tooltip--fluid').on("click", fnTooltipFluid );
	};
</script>
