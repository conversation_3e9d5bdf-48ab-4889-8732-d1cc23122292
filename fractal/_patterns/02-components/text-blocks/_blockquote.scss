
@use "../../00-settings" as *;

:where(blockquote) {
    margin: var(--body-font-size) 0;
    padding: 20px 25px 20px;
    background: $color-grey-01;
    font-size: 1.12em;
    font-style: italic;
    position: relative;
    clear: both;
    border-left: 4px solid $color-secondary;
    border-radius: $radius-default;


    :where(*):first-child {
        margin-top: 0;
    }

    :where(*):last-child {
        margin-bottom: 0;
    }
}

@include screen($bp992) {
    .blockquote--left {
        float: left;
        width: 45%;
        margin-left: 0px;
        margin-right: 20px;
        clear: right;
    }

    .blockquote--right {
        float: right;
        width: 45%;
        margin-left: 20px;
        margin-right: 0px;
        clear: left;
    }
}
