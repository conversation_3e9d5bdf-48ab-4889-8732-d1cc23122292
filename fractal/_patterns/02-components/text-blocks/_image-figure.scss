
@use "../../00-settings" as *;

@mixin imageAlignment {
    &.image-full {
        display: block;
        text-align: center;
        float: none;
        width: 100%;
        margin-left: 0;
        margin-right: 0;
        background: none;
        padding: 0;
    }

    &.image-right {
        @include screen($bp768) {
            max-width: 50%;
            margin-left: calc(var(--body-font-size)*2);
            float: right;
        }
    }

    &.image-left {
        @include screen($bp768) {
            max-width: 50%;
            margin-right: calc(var(--body-font-size)*2);
            float: left;
        }
    }

    &.image-center {
        display: block;
        text-align: center;

        img {
            display: block;
            margin-right: auto;
            margin-left: auto;
        }
    }
}

:where(.content-block figure) {

    display: inline-block;
    background: none;
    margin: 0 0 var(--body-font-size);
    padding: 0;

    @include imageAlignment;

    &.widget-video {
        display: block;
        padding-bottom: 56.25%;
        margin: 0 0 1em;
    }

}

:where(figure.content-image) {
    display: inline-block;
    background: none;
    margin-top: var(--body-font-size);
    margin-bottom: var(--body-font-size);
    margin-left: 0;
    margin-right: 0;
    padding: 0;

}

// CKeditor image styles
:where(figure.image) {
    display: block;
    text-align: center;
    float: none;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    background: none;
    padding: 0;

}

// CKeditor image styles
.image-style {

    &-align-right {
        @include screen($bp768) {
            max-width: 50%;
            width: auto;
            margin-left: calc(var(--body-font-size)*2);
            float: right;
        }
    }

    &-align-left {
        @include screen($bp768) {
            max-width: 50%;
            width: auto;
            margin-right: calc(var(--body-font-size)*2);
            float: left;
        }
    }

    &-align-center {
        display: block;
        text-align: center;

        img {
            display: block;
            margin-right: auto;
            margin-left: auto;
        }
    }

    &-side {
        @include screen($bp768) {
            max-width: 50%;
        }
        @include screen($bp768) {
            max-width: 50%;
        }

    }
}

:where(figcaption) {
    color: $color-grey-05;
    font-size: var(--milli-font-size);
    margin-top: 0.2em;
    display: block;
}

