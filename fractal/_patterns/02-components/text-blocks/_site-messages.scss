
@use "../../00-settings" as *;
@use "../../01-base/typography" as *;

.site-messages,
[data-field-error-messages] {
    list-style: none;
    margin: 0;
    padding: 0;
}

.site-messages__item,
[data-field-error-message],
.form-alert-error,
.form-alert-success {
    display: block;
    margin: 0 0 15px;
    padding: 10px 20px;
    vertical-align: bottom;
    text-align: left;
    font-size: 16px;
    font-size: 1.6rem;
    line-height: 1.2em;

    &:last-child {
        margin-bottom: 0;
    }

    &.site-messages__item--mb {
        margin-bottom: $fs-body;
    }
}


.site-messages .error,
[data-field-error-message],
.form-alert-error {
    @include error;
}

.site-messages .confirm,
.form-alert-success {
    @include positive;
}

.site-messages .warning {
    @include warning;
}

.site-messages a {
    color: $color-white;
}

.form-alert-error p {
    margin-bottom: 0;
}
