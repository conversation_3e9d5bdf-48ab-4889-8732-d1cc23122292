
@use "../../00-settings" as *;

.content-block {
    display: grid;
    column-gap: var(--layout-column-gap);

    @include screen($bp992) {
        grid-template-columns: 1fr;
    }

    &.half-media-right {
        @include screen($bp992) {
            grid-template-columns: 1fr 1fr;
        }
    }

    &.half-media-left {
        @include screen($bp992) {
            grid-template-columns: 1fr 1fr;
        }
    }

    &.half-two-copy {
        row-gap: 0;

        @include screen($bp992) {
            grid-template-columns: 1fr 1fr;
        }
    }

    &.third-media-right {
        @include screen($bp1200) {
            grid-template-columns: 2fr 1fr;
        }

    }

    &.third-media-left {
        @include screen($bp1200) {
            grid-template-columns: 1fr 2fr;
        }
    }

    &.third-copy-right {
        @include screen($bp1200) {
            grid-template-columns: 2fr 1fr;
        }

    }

    &.third-copy-left {
        @include screen($bp1200) {
            grid-template-columns: 1fr 2fr;
        }
    }

    &--no-gap {
        column-gap: 0 !important;
    }

    &-valign--top {
        align-items: start;
    }

    &-valign--center {
        align-items: center;
    }

    &-valign--bottom {
        align-items: flex-end;
    }
}


:where(
    .page-layout--skew .content-block:not(
            .bg-default,
            .bg-white
        ) .content-block__copy
    ) {
    padding-inline: var(--layout-column-gap);
}



:where(.content-block__copy),
:where(.content-block__media) {
    width: 100%;
}

[class*="half"] .content-block__media,
[class*="third"] .content-block__media {
    display: block;
    margin: 0 auto var(--paragraph-break);
}

[class*="half"] .content-block__media {
    @include screen($bp992) {
        margin: 0;
    }
}

[class*="third"] .content-block__media {
    @include screen($bp992) {
        margin: 0 0 var(--paragraph-break);
    }

    @include screen($bp1200) {
        margin: 0;
    }
}

.content-block__copy {
    .half-media-left & {
        @include screen($bp992) {
            order: 2;
        }
    }

    .third-media-left &
    .third-copy-right & {
        @include screen($bp1200) {
            order: 2;
        }
    }

    .half-media-right & {
        @include screen($bp992) {
            order: -1;
        }
    }

    .third-media-right &,
    .third-copy-left & {
        @include screen($bp1200) {
            order: -1;
        }
    }

    .half-media-right &,
    .half-media-left &,
    .third-media-right &,
    .third-media-left &,
    .third-copy-right &,
    .third-copy-left & {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
}

[class*="half"] .content-block__copy {
    @include screen($bp992) {
        > *:last-child {
            margin-bottom: 0;
        }
    }
}

[class*="third"] .content-block__copy {
    @include screen($bp1200) {
        > *:last-child {
            margin-bottom: 0;
        }
    }
}

.content-block--no-gap {
    &.half-media-right .content-block__copy {
        @include screen($bp992) {
            padding-right: min(var(--layout-gutter), $spacing*8);
        }
    }
    &.half-media-left .content-block__copy {
        @include screen($bp992) {
            padding-left: min(var(--layout-gutter), $spacing*8);
        }
    }

    &.third-media-right .content-block__copy {
        @include screen($bp1200) {
            padding-right: min(var(--layout-gutter), $spacing*8);
        }
    }

    &.third-media-left .content-block__copy {
        @include screen($bp1200) {
            padding-left: min(var(--layout-gutter), $spacing*8);
        }
    }
}

.full-bleed {
    &.half-media-right .content-block__copy {
        @include screen($bp992) {
            padding-left: var(--default-container-gutter);
        }
    }
    &.half-media-left .content-block__copy {
        @include screen($bp992) {
            padding-right: var(--default-container-gutter);
        }
    }

    &.third-copy-right .content-block__copy {
        @include screen($bp1200) {
            padding-left: var(--default-container-gutter);
        }
    }

    &.third-media-left .content-block__copy {
        @include screen($bp1200) {
            padding-right: var(--default-container-gutter);
        }
    }
}

