
@use "../../00-settings" as *;
@use "../../01-base/typography" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

:root {
    --ribbon-alerts-height: 0px;
}

.ribbon-alert {
    background-color: $color-utility-neutral;
    @include reverse-text;
    text-align: center;
    padding: $spacing*1.25 0 $spacing*1.5;
    font-size: var(--centi-font-size);
    line-height: 1.15;
    font-family: $primary-font;
    position: relative;

    &--warning {
        @include warning;
    }

    &--negative {
        @include error;
    }

    &--positive {
        @include positive;
    }
}

.ribbon-alert__container {
    display: flex;
    gap: $spacing;
    align-items: center;
    padding-inline: $spacing-large;
}

.ribbon-alert p {
    margin: 0;
}

.ribbon-alert__text {
    flex: 1 1 auto;
    display: grid;
    grid-auto-flow: column;
    gap: $spacing;
}

.ribbon-alert__closebtn {
    border: 0;
    background: transparent;
    color: inherit;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    width: 35px;
    display: grid;
    justify-content: center;
    align-items: center;
    transform-origin: center;
    transform: rotate(0);
    transition: transform $link-transition;
    cursor: pointer;

    @include screen($bp768) {
        position: unset;
    }

    @include screen($bp1400) {
        width: 46px;
    }

    * {
        pointer-events: none;
    }

    &:hover,
    &:focus,
    &:active {
        transform: rotate(90deg);

    }

    @include set-svg-colour;
}

.ribbon-alert--hidden {
    display: none;
}


