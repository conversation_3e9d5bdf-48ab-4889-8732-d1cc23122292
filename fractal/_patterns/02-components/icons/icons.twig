
    <p>Please see the notes tab for Icon System usage</p>

    <p>
be careful about setting attr on an SVG, because it clobbers any exising attr in fractal, but not on the front end at this point.
</p>


    <h3>Icons available for use:</h3>


    <div class="sg-icon-list">
    {% for item in filePathNames.items %}
        <div>
        <p>
            <span class="icon icon--32px">
                    {{ svg('@webroot/assets/' ~ item) }}
            </span>
        </p>
        <p>{{ item }}</p>
        </div>

    {% endfor %}
    </div>



    <h4>Form specific icons:</h4>
        <p style="font-size:1.2rem;">note: do not change icon source colour
            <br>[unless you deliberately want to affect the form icons]</p>
        <div style="border: 1px solid #ccc; border-radius: 4px; background-color: #f2f2f2; padding: 8px;">

       {{ svg('@webroot/assets/icon-system/icon_form_datepicker.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_datetimepicker.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_timepicker.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_arrow-left.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_arrow-right.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_arrow-down.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_arrow-down-sm.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_tick-sm.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_tick-md.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_tick-lg.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_tick-green.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_cross-red.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_cross.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_plus.svg') }}

       {{ svg('@webroot/assets/icon-system/icon_form_minus.svg') }}
    </div>

