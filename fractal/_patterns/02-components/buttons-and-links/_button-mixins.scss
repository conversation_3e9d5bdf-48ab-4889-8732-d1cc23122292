
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/_utility-mixins" as *;

:root {
    --button-border-width: 0;
    --button-border-radius: 64px;
    --button-padding-vertical: 0.75em;
    --button-padding-horizontal: 1.25em;
    --button-large-font-size: 1.5em;
    --button-opacity-disabled: 0.4;
}


@mixin button-normalise {
    vertical-align: middle;
    white-space: normal;
    cursor: pointer;
    opacity: 1;
    text-decoration: none;
    font: inherit;
}

@mixin button-base($size: 'default') {
    @include button-normalise;
    text-align: center;
    display: inline-grid;
    grid-auto-flow: column;
    column-gap: 0.5em;
    justify-content: center;
    align-items: center;
    border-radius: var(--button-border-radius);
    position: relative;
    margin: 0;
    transition:
        background-color $link-transition,
        border-color $link-transition,
        color $link-transition;
    @include button-size($size);
    @include button-typography;

    &:focus {
        outline: 1px auto $color-utility-neutral;
    }
}

@mixin button-typography() {
    line-height: 1.2;
    @include font-set('primary-bold');
}

@mixin button-disabled($background: $color-accent-01, $colour: $color-white) {
    border: none;
    box-shadow: none;
    opacity: var(--button-opacity-disabled);
    cursor: default;

    &:hover,
    &:focus,
    &:active {
        background-color: $background;
        box-shadow: none;
        color: $colour;
        opacity: var(--button-opacity-disabled);
    }
}

@mixin button($background: $color-accent-01, $colour: $color-white) {
    @include button-base;
    @include button-theme($background, $colour);
    @include button-svg;
}

@mixin button-theme($background: $color-accent-01, $colour: $color-white) {
    color: $colour;
    background: $background;
    border: var(--button-border-width) solid $background;
    transition:
        background-color $link-transition,
        border-color $link-transition,
        color $link-transition;


    &:hover,
    &:focus,
    &:active {
        background-color: darken($background, 10%);
        border-color: darken($background, 10%);
        color: $colour;
    }

    &[disabled] {
        @include button-disabled($background, $colour);
    }
}

@mixin button-svg {
    svg {
        height: 1em;
        width: 1em;

        [stroke*="#"] {
            stroke: currentColor;
        }

        [fill*="#"] {
            fill: currentColor;
        }
    }
}

/* --------------------------------
Text Link
----------------------------------- */

@mixin text-link($colour: $color-accent-01, $padding: false) {
    @include button-typography;
    color: $colour;
    background: transparent;
    text-decoration: none;
    transition:
        transform $link-transition,
        background-color $link-transition,
        color $link-transition;

    @if $padding {
        @include button-height-regular;
    }

    &:after {
        content: " >";
    }

    svg {
        height: 1em;
        width: 1em;

        @include set-svg-colour($colour);
    }

    &:hover,
    &:focus,
    &:active {
        background-color: transparent;
        color: $colour;

        svg {
            @include set-svg-colour($colour);
        }
    }
}

/* --------------------------------
Sizes
----------------------------------- */

@mixin button-size($size: 'default') {
    padding: var(--button-padding-vertical) var(--button-padding-horizontal);

    @if $size == 'small' {
        font-size: var(--centi-font-size);
    }

    @if $size == 'large' {
        --button-padding-vertical: 0.8em;
        font-size: var(--button-large-font-size);
    }
}
