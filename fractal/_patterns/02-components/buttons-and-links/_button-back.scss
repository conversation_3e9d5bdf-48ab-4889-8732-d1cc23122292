
@use "../../00-settings" as *;
@use "../buttons-and-links/button-mixins" as *;

.button-back {
    @include button-normalise;
    text-align: center;
    display: inline-grid;
    grid-auto-flow: column;
    column-gap: 0.5em;
    justify-content: center;
    align-items: center;
    border-radius: var(--button-border-radius);
    position: relative;
    margin: 0;
    padding: var(--button-padding-vertical) 0;
    transition:
        background-color $link-transition,
        border-color $link-transition,
        color $link-transition;
    @include button-typography;
    text-transform: uppercase;

    &:before {
        content: "\25C0";
    }
}
