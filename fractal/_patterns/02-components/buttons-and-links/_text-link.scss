
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;
@use "../../02-components/buttons-and-links/_button-mixins" as *;


:root {
    --text-link-colour: #{$color-accent-01};
    --text-link-hover-colour: darken(#{$color-accent-01}, 20%);
}

a {
    color: var(--text-link-colour);
    transition:
        color $link-transition,
        opacity $link-transition;

    &:hover,
    &:active,
    &:focus {
        color: var(--text-link-hover-colour);
    }
}

/* Address `outline` inconsistency between Chrome and other browsers. */
a:focus {
    outline: thin dotted;
}

/* Improve readability when focused and also mouse hovered in all browsers. */
a:active,
a:hover {
    outline: 0;
}

/* Include file type and size in document links */
a.document::after {
    content: " (" attr(data-ext) " " attr(data-size) ")";
}

:target {
    animation: highlightPulse 700ms ease;
    outline-offset: 4px;
}

.active-target {
    animation: highlightPulseOutline 2100ms ease;
}

.subtle-link {
    @include subtle-links;
}

.text-link {
    @include text-link;
}

// Custom text link for section footer
.text-link--footer {
    @include font-set('primary'); // Use normal weight instead of bold
    color: $color-grey-08;
    background: transparent;
    text-decoration: none;
    font-size: 1.6rem;
    line-height: 1.2;
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
    transition: color $link-transition;

    &:after {
        content: "›";
        font-size: 2em;
        transition: transform $link-transition;
    }

    &:hover,
    &:focus,
    &:active {
        color: $color-grey-06;

        &:after {
            transform: translateX(2px);
        }
    }
}
