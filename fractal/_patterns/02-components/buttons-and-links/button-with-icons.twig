
{% if button.href ?? false %}
<a href="{{ button.href }}"
{% else %}
<button
{% endif %}

    class="button {{ button.modifier ?? false ? button.modifier : '' }}"
    {% if button.ariaLabel ?? false %}
    aria-label="{{ button.ariaLabel }}"
    {% endif %}
    {% if button.title ?? false %}
    title="{{ button.title }}"
    {% endif %}
    >


    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path fill="#fff" d="M22.5555556,20.8888889 L17.4444444,15.4444444 C18.7777778,13.8888889 19.4444444,11.8888889 19.4444444,9.77777778 C19.4444444,5 15.6666667,1 10.7777778,1 C6,1 2,4.77777778 2,9.66666667 C2,9.77777778 2,9.77777778 2,9.88888889 C2,14.7777778 5.88888889,18.6666667 10.6666667,18.7777778 C12.4444444,18.7777778 14.2222222,18.2222222 15.6666667,17.2222222 L20.8888889,22.6666667 C21.3333333,23.1111111 22,23.1111111 22.4444444,22.6666667 C22.4444444,22.6666667 22.4444444,22.6666667 22.4444444,22.6666667 C23,22 23,21.3333333 22.5555556,20.8888889 Z M10.7777778,3.22222222 C14.3333333,3.22222222 17.3333333,6 17.4444444,9.66666667 C17.4444444,13.2222222 14.6666667,16.2222222 11,16.3333333 C7.33333333,16.4444444 4.44444444,13.5555556 4.33333333,9.88888889 C4.33333333,9.88888889 4.33333333,9.77777778 4.33333333,9.77777778 C4.33333333,6.22222222 7.11111111,3.22222222 10.7777778,3.22222222 Z"/>
    </svg>

    {{ svg('@webroot/assets/icon-system/icon_search.svg') }}


    {{ button.text ?? 'find out more' }}

{% if button.href ?? false %}
</a>
{% else %}
</button>
{% endif %}
