title: "Button component"
collated: true
status: wip
notes: This needs to be reworked to pass in the icon
context:
  button:
    modifier: " "
    text: "Read more"
variants:
  -
    name: small
    context:
      button:
        modifier: button--small
        text: "small button"
  -
    name: y-small
    context:
      button:
        modifier: button--y-small
        text: "Y small button"
  -
    name: large
    context:
      button:
        modifier: button--large
        text: "large button"
  -
    name: alt
    context:
      button:
        modifier: button--alt
        text: "alt button"
  -
    name: secondary
    context:
      button:
        modifier: button--secondary
        text: "secondary button"
  -
    name: neutral
    context:
      button:
        modifier: button--neutral
        text: "neutral button"
  -
    name: Warning
    context:
      button:
        modifier: button--warning
        text: "warning button"
  -
    name: positive
    context:
      button:
        modifier: button--positive
        text: "positive button"
  -
    name: block
    context:
      button:
        modifier: button--block
        text: "block button"
  -
    name: disabled
    context:
      button:
        modifier: button--disabled
        text: "disabled button"
  -
    name: l-arrow-after
    context:
      button:
        modifier: -l-arrow-after
        text: 'Left arrow after'
  -
    name: l-arrow-before
    context:
      button:
        modifier: -l-arrow-before
        text: 'Left arrow before'
  -
    name: r-arrow-after
    context:
      button:
        modifier: -r-arrow-after
        text: 'Right arrow after'
  -
    name: r-arrow-before
    context:
      button:
        modifier: -r-arrow-before
        text: 'Right arrow before'
