
@use "../../00-settings" as *;
@use "../../01-base/typography/typography-mixins" as *;
@use "../../02-components/buttons-and-links/button-mixins" as *;

.cta-box {
    background-color: $color-grey-01;
    padding: 32px;
    border-radius: $radius-default;
}

.cta-box__heading {
    margin-top: 0;
    line-height: normal;

    span {
        display: block;
    }
}

.cta-box__subtitle {
    @include subtitle;
}

.cta-box__title {
    @include h3;
}

.cta-box__cta {
    a {
        @include button;
    }
}

.cta-box__content {

    > *:last-child {
        margin-bottom: 0;
    }
}

.cta-box:where(.bg-default) {
    border: $border;
}
