<!-- Template: {{ _self }} -->

{% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
{% block addOnContent %}

    <h3 class="cta-box__heading">
    {% if addOnEntry.subtitle|length %}
        <span class="cta-box__subtitle">{{ addOnEntry.subtitle }}</span>
    {% endif %}

    {% if addOnEntry.itemTitle|length %}
        <span class="cta-box__title">{{ addOnEntry.itemTitle }}</span>
    {% endif %}
    </h3>

    {% if addOnEntry.richtext|length %}
        <div class="cta-box__content">

            {{ addOnEntry.richtext }}

            {% if addOnEntry.linkHelper|length > 0 %}
            <p class="cta-box__cta">
                {% include './01_core/_blocks/link-helper.twig' with { entry: addOnEntry } %}
            </p>
            {% endif %}

        </div>
    {% endif %}

{% endblock %}
{% endembed %}
