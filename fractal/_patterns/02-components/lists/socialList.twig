{# If not passed into the view, set the social array #}
{% if socialArray is not defined %}
{# Create an array of all the possible social account based on the fields created for the site identity global set #}
{% set socialArray = socialList.socialArray ?? ['facebook', 'instagram', 'youtube', 'linkedin', 'twitter', 'tiktok'] %}
{% endif %}

{# Ensure that the social list is only rendered when one or more of the site identity social fields has a value #}
{% set socialFieldHasValue = false %}
{% for name in socialArray %}
    {% if siteIdentity[name]|length > 0 %}
        {% set socialFieldHasValue = true %}
    {% endif %}
{% endfor %}

{# Render the social list #}
{% if socialFieldHasValue %}
{% if socialList.title ?? false %}
<div class="social-list-wrapper"> {# Add a wrapper to keep the social list and title coupled together #}
    <h3>{{ socialList.title }}</h3>
{% endif %}
    <ul class="social-list {{ socialList.modifier ?? null }}">
    {% for item in socialArray %}
        {% include 'patterns/02-components/lists/socialListItem.twig' with { socialListItem: {
                name: item,
                link: siteIdentity[item]
            }
        } %}
    {% endfor %}
    </ul>
{% if socialList.title ?? false %}
</div>
{% endif %}
{% endif %}
