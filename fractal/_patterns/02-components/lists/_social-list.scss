
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.social-list {
    list-style: none;
    padding: 0;
    margin: 0px;
    margin-left: $spacing-xsmall;
    margin-right: $spacing-xsmall;
    display: grid;
    grid-auto-flow: column;
    gap: $spacing-small;
    justify-content: flex-start;

    &--right {
        justify-content: flex-end;
    }

    &--center {
        justify-content: center;
    }

    &__item {
        display: inline-block;

        a {
            color: inherit;
            display: block;
        }
    }

    @include set-svg-colour;

    &--primary {
        color: rgba($color-primary, .7);
    }

    &--grey {
        color: $color-grey-04;
    }

    &--circle {
        a {
            display: block;
            display: flex;
            justify-content: center;
            align-items: center;
            width: $spacing*5;
            height: $spacing*5;
            border-radius: 50%;
            padding: $spacing;
            background-color: currentColor;
            transition: background-color $link-transition;
        }

        @include set-svg-colour($color-white);
    }
}
