# Run with "python3 check-for-missing-yaml-config-files.py"
# It will print out any .twig files that do not have a corresponding
# .config.yml file *or* any corresponding .config.yml files that *exist*
# but are empty.

# In general, we want to have a corresponding .config.yml file
# for every Fractal component so that we can explicitly set
# status, among other things.

import os
import re

def search_for_twig_files(root_dir):
    for root, dirs, files in os.walk(root_dir):
        # List of all .twig files in the current directory
        twig_files = [f for f in files if f.endswith('.twig')]

        # Process .config.yml files
        yml_files = [f for f in files if f.endswith('.config.yml')]
        yml_filenames = {re.sub(r'\.config\.yml$', '', f): os.path.join(root, f) for f in yml_files}

        for twig_file in twig_files:
            # Remove leading numbers and hyphens and .twig extension
            twig_file_base = re.sub(r'^\d+-', '', os.path.splitext(twig_file)[0])

            # Check if a corresponding .config.yml file exists
            if twig_file_base in yml_filenames:
                yml_file_path = yml_filenames[twig_file_base]
                if os.path.getsize(yml_file_path) == 0:
                    print(f"[EMPTY]   Twig file {twig_file} *exists* but is empty: {yml_file_path}")
            else:
                print(f"[MISSING] Twig file without corresponding .config.yml: {os.path.join(root, twig_file)}")

# Search current directory
search_for_twig_files('./')
