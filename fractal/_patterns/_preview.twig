<!doctype html>
<html lang="en" class="no-js">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Preview Layout</title>

    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <script type="text/javascript">var ROOT = '/';</script>
    <script type="text/javascript" src="//ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    {# This loads any bundled/CSS + JS from our Vite manifest file #}
    {# We can't directly do a 'link'/'script' tag here because our bundles #}
    {# have a hash in their output names #}
    {{ vite('@webroot/dist/.vite/manifest.json') }}

    {% block css %}
    {# Amusingly, need to work out how to link to the fractal folder below #}
    <link rel="stylesheet" href="/fractal/_patterns/styleguide.css">
    {% endblock %}

    {% block bodyModClasses %}{% endblock %}
</head>
<body>

    <style>
        body {
            padding: 20px;
        }
    </style>


    {% if _target.isCollated %}

    <dl class="styleguide">
        {{ yield }}
    </dl>
    {% else %}

    {{ yield }}

    {% endif %}

</body>
</html>

