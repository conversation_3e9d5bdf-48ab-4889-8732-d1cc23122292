
@use "../../00-settings" as *;

/* -----------------------------
Visibility Utilities
------------------------------*/

@mixin vis-hidden() {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;

    &.focusable:active,
    &.focusable:focus {
        clip: auto;
        height: auto;
        margin: 0;
        overflow: visible;
        position: static;
        width: auto;
    }
}

@mixin vis-hidden-reset() {
    clip: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    position: static;
    width: auto;
}


/* -----------------------------
Clearfix
------------------------------*/
@mixin clearfix() {
    &:before,
    &:after {
        content: " ";
        display: table;
    }
    &:after {
        clear: both;
    }
}


/* -----------------------------
SVG
------------------------------*/
@mixin set-svg-colour($color: currentColor) {
    :where([stroke*="#"]) {
        stroke: $color;
    }

    :where([fill*="#"]) {
        fill: $color;
    }

    :where([stroke*="#"]),
    :where([fill*="#"]) {
        @content
    }
}


/* -----------------------------
List Mixins
------------------------------*/
@mixin list-reset() {
    list-style-type: none;
    padding-left: 0;
}


/* -----------------------------
Fallback logo image
------------------------------*/
@mixin fallback-logo-image($theme: '') {
    background: $color-grey-01;
    background-image: url(../assets/logo.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 50%;

    @if $theme == 'primary' {
        background-color: $color-primary;
        background-image: url(../assets/logo-mono.svg);
    }

    @if $theme == 'reverse' {
        background-color: $color-white;
        background-image: url(../assets/logo-mono-reverse.svg);
    }
}
/*
Setting this means we can animate the linear gradient background on hover
It need to be globally scoped to work, don't move this into the mixin.
The magic is setting the 'syntax' allowing the browser understand how to interpolate between two values.
*/
@property --popup-gradient-darkness {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 33%;
}

/* -----------------------------
Popup indicator
------------------------------*/
@mixin popup-signifier() {
    position: relative;

    &:hover,
    &:focus,
    &:active {
        .popup-gallery__icon {
            --popup-gradient-darkness: 66%;
        }
    }

    .popup-gallery__icon {
        --popup-gradient-darkness: 33%;
        position: absolute;
        right: 0;
        top: 0;
        width: 48px;
        height: 48px;
        padding: 8px 4px 20px 24px;
        display: grid;
        justify-content: start;
        align-content: end;
        background: linear-gradient(-135deg, rgba(0,0,0,var(--popup-gradient-darkness)) 0%, rgba(0,0,0,0) 50%);
        transition:--popup-gradient-darkness 0.25s,
    }
}
