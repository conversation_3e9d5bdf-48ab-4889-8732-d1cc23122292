
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

/* ---- Hidden ---- */
.-hidden {
    display: none !important;
    visibility: hidden;
}

/* ---- Invisible ---- */
.-invisible {
    visibility: hidden;
}

/* ---- Visibility Hidden ---- */
%vis-hidden,
.-vis-hidden {
    @include vis-hidden;
}

/* ---- Hide / Show  ---- */
// Display none utility classes
.hide {
    display: none !important;
}

.show {
    display: initial !important;
}

@each $key, $value in $breakpoints-list {
    .hide--#{$key} {
        @include screen(#{$value}) {
            display: none !important;
        }
    }

    .show--#{$key} {
        display: none !important;

        @include screen(#{$value}) {
            display: initial !important;
        }
    }
}
