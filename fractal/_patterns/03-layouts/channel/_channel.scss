
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;
@use "../../02-components/section/section-mixins" as *;

.channel {
    .mainbar {
        position: relative;
        z-index: 1;
    }

    .page-header {
        margin-bottom: var(--paragraph-break);
        text-align: left;
        padding: var(--section-small) var(--section-small) 0;
        display: flex;
        flex-direction: column;

        &__heading {
            margin-bottom: 0;
        }

        &__title {
            font-size: inherit
        }

        .breadcrumb {
            margin: 0.5em 0 0;
        }
    }

    &__content {
        padding: 0 var(--section-small) var(--section) var(--section-small);

        > *:last-child {
            margin-bottom: 0;
        }
    }

    .bottombar {
        padding-block: var(--section);
        margin-top: var(--section);
        background-color: $color-primary;
        @include bleed-left;
        @include bleed-right;
        @include reverse-text;
        display: grid;
        gap: var(--section-small);
    }
}
