
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.footer {

    &__body {
        background: $color-primary;
        @include reverse-text;

        &__container {
            display: grid;
            grid-template-columns: repeat(3, auto);
        }
    }

    &__endcap {
        background: $color-primary;
        @include reverse-text;
        padding-block: var(--section-small);

        &__container {
            display: grid;
            gap: 8px;

            @include screen($bp560) {
                grid-auto-flow: column;
                grid-template-columns: 1fr; // will push the second item to the far right
            }
        }
    }

    &__text {
        margin-top: 1em;

        @include screen($bp992) {
            margin-top: 0;
        }
    }

    &__technical-links {
        @include list-reset;
        display: inline;

        li {
            @include screen($bp560) {
                display: inline;
            }

            &:after {
                content: "\2003";

                @include screen($bp1600) {
                    display: inline-block;
                    width: 40px;
                }
            }

            a {
                text-decoration: none;
            }
        }
    }
}


.associations {
    @include list-reset;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: $spacing;

    img {
        max-width: 220px;
    }
}
