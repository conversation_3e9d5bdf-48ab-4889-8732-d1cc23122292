
@use "../../00-settings" as *;
@use "../../02-components/section/section-mixins" as *;

:root {
    --header-section-height: 54px;
    --header-inner-height: var(--header-section-height);
    --header-index: 100;
}

@include screen($bpdesktop) {
    :root {
        --header-section-height: 88px;
        --header-inner-height: 88px;
    }
}

.header--shrink {
    // We need to separate out the original header height from the shrunk header
    // The #header need to remain the original height, or else you'll get the jitters.
    @include screen($bpdesktop) {
        --header-inner-height: 64px;
    }
}

#js-header {
    overflow-x: clip;
    height: var(--header-section-height);
    transition:
        height $header-transition;

    @include screen($bp2400) {
        width: $max-page-width;
        margin: 0 auto;
    }
}

.header {
    position: relative;
    height: var(--header-inner-height);
    padding-block: 8px;
    background: $color-white;
    border-bottom: $border;
    transition:
        height $header-transition;
    z-index: var(--header-index);

    @include screen($bp2400) {
        max-width: $max-page-width;
        margin: 0 auto;
    }
}

.header__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;

    @include screen($bpmobile, 'max') {
        background-color: inherit; /* [menu stacking] */
        @include bleed-left; /* [menu stacking] */
        @include bleed-right; /* [menu stacking] */
    }
}

.header__nav {
    @include screen($bpdesktop) {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .social-list {
        height: 40px;
        opacity: 1;
        transition:
            height $header-transition,
            opacity $header-transition;

        @include screen($bpmobile, 'max') {
            display: none;
        }
    }
}

.header--sticky .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;

    @include screen($bp2400) {
        margin: 0;
        left: calc(( (var(--vw, 1vw) * 100) - #{$max-page-width}) / 2);
        right: calc(( (var(--vw, 1vw) * 100) - #{$max-page-width}) / 2);
    }
}

.header__logo {
    flex: 0 0 auto;
    display: block;
    transition:
        margin-bottom $header-transition;

    @include screen($bpdesktop) {
        margin-bottom: 0.5em;
    }

    svg {
        display: block;
        height: calc(var(--header-inner-height) * 0.8);
        width: calc((var(--header-inner-height) * 0.8) * 2.661654);
        transition:
            width $header-transition,
            height $header-transition,
            margin-bottom $header-transition;
    }
}

// Ensure the js event fall on the button, not the svg nor label
.book-button * {
    pointer-events: none;
}

.book-button[aria-pressed="true"] {
    background-color: $color-body-font;

    .icon-search {
        display: none;
    }
}

.book-button[aria-pressed="false"] {
    .icon-close {
        display: none;
    }
}

.book-button__label {
    text-align: center;
    min-width: 44px;
}


.header__nav {
    display: grid;
    grid-auto-flow: column;
    align-items: center;
    gap: $spacing-medium;

    @include screen($bpdesktop) {
        gap: $spacing-xxlarge;
    }
}

/* [menu stacking]
This code is necessary so that the mobile menu and its box shadow sits "behind the header".
The z-index stacking basically means we need to raise another child higher than the header in order for that child to sit above the collapsible menu
The container needs a white background to obstruct the box shadow, there's no use sitting a transparent element above the collapsible menu.
 */
