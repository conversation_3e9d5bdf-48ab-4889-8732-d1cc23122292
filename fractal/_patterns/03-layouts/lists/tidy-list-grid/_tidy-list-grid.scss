
@use "../../../00-settings" as *;
@use "../../../05-utilities/helpers/utility-mixins" as *;

/*
    Tidy List grid

    On large desktop it adjusts based on the number of items:
    - 1 full width
    - 2 two cols
    - 3 three cols
    - 4 four cols
    - 5+ three cols - container width
    - 5 five cols - full bleed
    - 6 six cols - full bleed
*/

.tidy-list-grid {
    @include list-reset;
    margin: 0;
    display: grid;
    gap: var(--tight-gap);

    @include screen($bp768) {
        display: inline-grid;
        min-width: 100%;
        grid-template-columns: unset;
        grid-auto-columns: 1fr;

        > :nth-child(2):last-child,
        > :nth-child(4):last-child {
            grid-column: 2;
        }
    }

    @include screen($bp1200) {

        > :nth-child(3):last-child,
        &.container-width > :last-child:nth-child(3),
        &.full-bleed > :last-child:nth-child(3),
        &.container-width > :nth-last-child(n + 5) ~ :nth-child(3),
        &.full-bleed > :nth-last-child(n + 5) ~ :nth-child(3) {
            grid-column: 3;
        }

    }

    &__item {
        container: default-card / inline-size;
    }

    @include screen($bp1400) {
        :where(.container) & > :nth-child(4n):last-child,
        :where(.page-layout--center, .home) &.container-width > :nth-child(4n):last-child,
        :where(.page-layout--center, .home) &.full-bleed > :nth-child(4n):last-child {
            grid-column: 4;
        }
    }

    @include screen($bp1600) {
        .page-layout--center &.full-bleed > :nth-child(5n):last-child {
            grid-column: 5;
        }
    }

    @include screen($bp1800) {
        .page-layout--center &.full-bleed > :nth-child(6n):last-child {
            grid-column: 6;
        }
    }


    &--gap-0 {
        --gap: 0;
    }
}
