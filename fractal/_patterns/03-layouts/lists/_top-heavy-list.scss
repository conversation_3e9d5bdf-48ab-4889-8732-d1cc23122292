
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

/* ---- top-heavy-list ---- */

/* To display a small list of products nicely  */
// works best with max of 7 items, 8+ is a fixed grid

.top-heavy-list {
    display: grid;
    gap: var(--gap);

    container: default-card / inline-size;

    @include screen($bp768) {
        grid-template-columns: repeat(2, 1fr);
    }

    @include screen($bp992) {
        &--3,
        &--6 {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    @include screen($bp1200) {
        gap: var(--tight-gap);
        grid-template-columns: repeat(12, 1fr);

        &__item {
            grid-column: span 3;
        }
    }

    @include screen($bp992) {
        &--3,
        &--6 {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    &--3 &__item:first-child,
    &--5 &__item:first-child,
    &--7 &__item:first-child,
    &--odd &__item:first-child {
        @media screen and (min-width: $bp768) and (max-width: $bp1199) {
            grid-column: 1 / -1;
        }
    }
    &--3 &__item,
    &--6 &__item {
        @include screen($bp1200) {
            grid-column: span 1;
        }
    }


    &--1 &__item,
    &--2 &__item {
        @include screen($bp1200) {
            grid-column: span 6;
        }
    }

    &--5 &__item {
        @include screen($bp1200) {
            grid-column: span 4;
        }
    }
    &--1 &__item,
    &--2 &__item,
    &--5 &__item:nth-child(-n+2) {
        @include screen($bp1200) {
            grid-column: span 6;
        }
    }

    &--7 &__item:nth-child(-n+3) {
        @include screen($bp1200) {
            grid-column: span 4;
        }
    }
}

