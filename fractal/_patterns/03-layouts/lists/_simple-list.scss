
@use "../../05-utilities/helpers/utility-mixins" as *;
@use "../../00-settings" as *;


.simple-list {
    @include list-reset;
    display: grid;
    gap: calc(var(--tight-gap) * .5);


    &__item {
        container: default-card / inline-size;
        padding-top: calc(var(--tight-gap) * .5);
        border-top: 1px solid $color-grey-03;

        &:first-child {
            padding-top: 0;
            border-top: 0;
        }

        &:after {
            clear: both;
        }
    }
}
