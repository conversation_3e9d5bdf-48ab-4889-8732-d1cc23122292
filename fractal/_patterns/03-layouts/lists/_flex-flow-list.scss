
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

// For displaying things like icon tiles eg. when odd numbers look not in grid

.flex-flow-list {
    @include list-reset;
    display: flex;
    gap: $spacing*2 $spacing*2;
    flex-flow: row wrap;
    justify-content: space-between;


    @include screen($bp992) {
        column-gap: $spacing*8;
    }

    &__item {
        container: default-card / inline-size;
        flex: 1 0 0;
        display: flex;
        min-width: 20ch;
        align-items: flex-end;

    }
}
