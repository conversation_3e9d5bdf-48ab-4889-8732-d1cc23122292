
@use "../../../00-settings" as *;
@use "../../../05-utilities/helpers/utility-mixins" as *;

.fixed-grid {
    @include list-reset;
    margin: 0;
    display: grid;
    gap: var(--tight-gap);

    &__item {
        container: default-card / inline-size;
    }

    @include screen($bp768) {
        grid-template-columns: 1fr 1fr;
    }

    @include screen($bp1200) {

        &--cols-2 {
            grid-template-columns: 1fr 1fr;
        }

        &--cols-3,
        &--cols-4,
        &--cols-5 {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @include screen($bp1400) {
        &--cols-4 {
            grid-template-columns: repeat(4, 1fr);
        }
        &--cols-5 {
            grid-template-columns: repeat(5, 1fr);
        }

    }


}
