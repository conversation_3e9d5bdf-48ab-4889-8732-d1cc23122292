
@use "../../00-settings" as *;

:root {
    --layout-column-gap: 6vw;
    --layout-gutter: calc(var(--default-container-gutter) - var(--layout-column-gap));

    --page-header-row: 1;
}

@include screen($bp768) {
    :root {
        --layout-column-gap: 32px;
    }
}

@include screen($bp1600) {
    :root {
        --layout-column-gap: 48px;
    }
}

.page-layout--skew {
    // a skewed layout
    // pros: give appropriate space for sidebar elements
    // cons: ends up being very informationally dense.
    // note the additional columns (columns 1 & 4) have a dual purpose of centring the content blocks in the same way `margin: auto` would but still allows us to have full bleed content elements)
    display: grid;
    grid-template-columns: var(--layout-gutter) 1fr 2fr var(--layout-gutter);
    grid-template-rows: auto;
    column-gap: var(--layout-column-gap);

    @include screen($bp1200) {
        grid-auto-flow: dense;
    }

    > *,
    :where(.mainbar) {
        // stack all mainbar, sidebar and bottom bar content in the same column on smaller screens
        grid-column: 2/4;
    }

    :where(.mainbar) {
        @include screen($bp1200) {
            // sit the mainbar to the side of the sidebar on desktop
            grid-column: 3/4;
        }
    }

    :where(.sidebar) {
        @include screen($bp1200) {
            // sit the sidebar to the side of the mainbar on desktop
            grid-column: 2/3;
        }
    }

    :where(.full-bleed) {
        width: 100%;
        grid-column: 1 / -1;
    }

    :where(.page-header),
    :where(.bottombar),
    :where(.container-width) {
        // A utility class for a container wide element
        grid-column: 2 / -2;
    }
}

.page-layout--center {
    // a centred layout
    // pros: prioritises comfortable content line length
    // cons: the sidebars / edge columns are awkward to use for any kind of content
    display: grid;
    grid-template-columns: var(--layout-gutter) 1fr 3fr 1fr var(--layout-gutter);
    column-gap: var(--layout-column-gap);

    @include screen($bp1200) {
        grid-auto-flow: dense;
    }

    > *,
    :where(.mainbar) {
        grid-column: 3;

        @include screen($bp768, 'max') {
            grid-column: 2 / -2;
        }
    }

    :where(.content-block) {
        columns: 3;
    }

    :where(.sidebar) {
        @include screen($bp1200) {
            grid-column: 2 / 3;
            grid-row: calc(var(--page-header-row) + 1)/-1;
        }
    }

    :where(.full-bleed) {
        width: 100%;
        grid-column: 1 / -1;
    }

    :where(.page-header),
    :where(.bottombar),
    :where(.container-width) {
        // A utility class for a container wide element)
        grid-column: 2 / -2;
    }
}

.page-layout--wide {
    // a wide layout
    // pros: great for index or listing pages that will have minimal text content and need maximum screen real estate
    // cons: huge and terrible line length
    // note the additional columns (columns 1 & 3 which are 1fr each) have a dual purpose of centring the content blocks in the same way `margin: auto` would but still allows us to have full bleed content elements)
    display: grid;
    grid-template-columns: var(--layout-gutter) auto var(--layout-gutter);
    column-gap: var(--layout-column-gap);

    > *,
    :where(.mainbar),
    :where(.page-header) {
        grid-column: 2 / 3;
    }

    :where(.full-bleed) {
        width: 100%;
        grid-column: 1 / -1;
    }
}

:where(.page-header) {
    grid-row: var(--page-header-row);
}


:where(.sidebar) {
    display: grid;
    row-gap: $spacing-xlarge;
}
