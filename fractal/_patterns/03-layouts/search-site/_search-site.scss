
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;
@use "../../01-base/typography/typography-mixins" as *;

.search-site {
    &__results-header {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 8px;
        align-items: center;
        margin-bottom: var(--paragraph-break);

        h2 {
            margin-bottom: 0;
        }
    }

    &__count {
        background-color: $color-primary;
        @include reverse-text;
        width: calc(var(--h2-font-size) * 2);
        height: calc(var(--h2-font-size) * 2);
        border-radius: calc(var(--h2-font-size) * 2);
        display: grid;
        place-items: center;
        align-content: center;
        font-weight: $fw-bold;
        line-height: 1;

        &__quantity {
            display: block;
            font-size: calc(var(--body-font-size) * 1.2);
            margin-bottom: 0.1em;
        }

        &__label {
            display: block;
            text-transform: uppercase;
            font-size: 1rem;
        }
    }

}


/**
 * See simple-list.scss for results listing styles
 */

.search-result {
    display: block;
    text-decoration: none;
    color: currentColor;
    padding: $spacing*2 $spacing*4 $spacing*2 $spacing*0;
    transition: all $link-transition;

    &:hover,
    &:focus,
    &:active {
        color: currentColor;
        background-color: $color-white;
        padding-inline: $spacing*2;
    }

    &__title {
        @include h4($color-secondary);
        margin-bottom: $spacing;
    }

    &__role,
    &__breadcrumb {
        font-size: 1.4rem;
        @include font-set('primary-bold');
        color: $color-grey-07;
    }
}
