@use '../00-settings/breakpoints' as *;


:root {
    --body-font-size: 1.6rem;
    --body-line-height: 1.5;
    --compact-line-height: normal;

    // headings
    --h1-font-size: 2.4rem;
    --h1-line-height: 1.25;

    --h2-font-size: 2.4rem;
    --h2-line-height: 1.5;

    --h3-font-size: 2.2rem;
    --h3-line-height: inherit;

    --h4-font-size: 2rem;
    --h4-line-height: 1.5;

    --h5-font-size: 1.8rem;
    --h5-line-height: inherit;

    --h6-font-size: 1.6rem;
    --h6-line-height: inherit;

    /* [02] */
    // big
    --giga-font-size: 5rem;
    --mega-font-size: 4.4rem;
    --kilo-font-size: 4rem;

    // small
    --milli-font-size: 1.2rem;
    --centi-font-size: 1.4rem;
}


@include screen($bp360) {
    :root {
        --h1-font-size: 3rem;

        --h2-font-size: 2.8rem;
    }
}

@include screen($bp768) {
    :root {
        // headings
        --h1-font-size: 4rem;
        --h1-line-height: 1.2;

        --h2-font-size: 3.2rem;
        --h2-line-height: 1.25;

        --h3-font-size: 2.4rem;
        --h3-line-height: inherit;

        --h4-font-size: 2.2rem;
        --h4-line-height: 1.5;

        --h5-font-size: 2rem;
        --h5-line-height: inherit;

        /* [02] */
        // big
        --giga-font-size: 6.4rem;
        --mega-font-size: 5.6rem;
        --kilo-font-size: 4.8rem;

    }
}
@include screen($bp1200) {
}

/*
    Basic system font stacks
*/

$system-font: Open Sans, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, roboto, noto, arial, sans-serif;

$system-serif-font: Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;

/*
    Brand fonts
*/

$primary-font-face: null !default;

$accent-font-face: null !default;

/*
    Font stacks
*/

$primary-font: $primary-font-face, $system-font;

$accent-font: $accent-font-face, $system-serif-font;


/*
    Font Family key for Fractal
*/

$font-families: (
    primary: $primary-font,
    accent: $accent-font
) !default;


/* Weights */

$fw-normal: 400;

$fw-medium: 600;

$fw-bold: 800;


$font-weight-list: (
    fw-normal: $fw-normal,
    fw-bold: $fw-bold
) !default;


/* Size */

$fs-small: var(--milli-font-size);

$fs-body: var(--body-font-size);

/* -----
* NOTES
* [02]
* A series of classes for setting massive type; for use in heroes, mastheads,
* promos, etc.
* As per: csswizardry.com/2012/02/pragmatic-practical-font-sizing-in-css
------ */


/*
    Font Mixins
*/

@mixin font($font-family: $accent-font, $font-weight: $fw-normal, $font-style: false) {
    font-family: $font-family;
    font-weight: $font-weight;

    @if $font-style {
        font-style: $font-style;
    }
}

@mixin font-set($set: 'primary') {

    @if $set == 'primary' {
        font-family: $primary-font;
        font-weight: $fw-normal;
    }

    @if $set == 'primary-bold' {
        font-family: $primary-font;
        font-weight: $fw-bold;
    }

    @if $set == 'accent' {
        font-family: $accent-font;
        font-weight: $fw-bold;
    }

}

