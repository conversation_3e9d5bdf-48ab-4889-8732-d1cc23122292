@use 'breakpoints' as *;
@use 'colors' as *;

/* -----------------------------
Spacing
-------------------------------- */
$spacing:                   8px;

$spacing-xxsmall:           $spacing*.25;

$spacing-xsmall:            $spacing*.5;

$spacing-small:             $spacing;

$spacing-medium:            $spacing*2;

$spacing-large:             $spacing*3;

$spacing-xlarge:            $spacing*4;

$spacing-xxlarge:           $spacing*8;


$spacing-sizes: (
    spacing:    $spacing,
    xxsmall:    $spacing-xxsmall,
    xsmall:     $spacing-xsmall,
    small:      $spacing-small,
    medium:     $spacing-medium,
    large:      $spacing-large,
    xlarge:     $spacing-xlarge,
    xxlarge:    $spacing-xxlarge
) !default;


/* -----------------------------
Grid gaps/spacing
-------------------------------- */

:root {
    --tight-gap: #{$spacing*1.5}; // Good for row gaps
    --gap: #{$spacing*1.5}; // Good for column gaps
    --wide-gap: #{$spacing*1.5}; // Good for column gaps on wide templates
}

@include screen($bp480) {
    :root {
        --gap: #{$spacing*2};
        --wide-gap: #{$spacing*2};
    }
}

@include screen($bp768) {
    :root {
        --tight-gap: #{$spacing*3};
        --gap: #{$spacing*4};
        --wide-gap: #{$spacing*4};
    }
}

@include screen($bp1400) {
    :root {
        --tight-gap: #{$spacing*4};
        --gap: #{$spacing*6};
        --wide-gap: #{$spacing*8};
    }
}


/* -----------------------------
style variables
-------------------------------- */
$radius-default:            4px; // note: button and form radius set separately

$radius-large:            #{$spacing-medium}; // note: button and form radius set separately

$radius-full:            1000px; // note: button and form radius set separately

$form-spacing:            8px;

$link-transition-duration: 250ms;

$link-transition-timing-function: ease-in-out;

$link-transition:   250ms ease-in-out;

$border-width: 2px;

$border-color: #{$color-grey-02};

$border: $border-width solid $border-color;

/* -----------------------------
Header
-------------------------------- */
$header-transition: 350ms ease;

$header-index: 100;


/* -----------------------------
Page
-------------------------------- */
$max-page-width: 2400px;
