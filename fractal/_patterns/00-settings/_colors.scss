
// Brand colours

$color-primary: 					#87292A; // BROWN

$color-primary-light:               lighten($color-primary, 80%);

$color-secondary:                   #AD5332; // CARAMEL

$color-secondary-light:             lighten($color-secondary, 60%);

$color-alternative:                 #F3E9E9; // optional - unused

$color-accent-01: 				    #35ab75; // GREEN

$color-accent-02:                   #FFD23F; //optional - unused

/* ------------------------------------------------------
**** Don't forget to update the Panel Layout Colours when updating above
        - modules/contentpanel/assets/css/BackgroundColourField.css
        - modules/contentpanel/fields/PanelSettingsField.php
------------------------------------------------------ */


$brand-color-list: (
    primary: $color-primary,
    secondary: $color-secondary,
    alternative: $color-alternative,
    accent-01: $color-accent-01,
    accent-02: $color-accent-02
) !default;


// Neutral Colours

$color-white:						#ffffff;

$color-grey-00:                     #f1f1f1;

$color-grey-01:                     #e2e2e2;

$color-grey-02:						#d4d4d4;

$color-grey-03:						#b8b8b8;

$color-grey-04:						#9c9c9c;

$color-grey-05:						#7f7f7f;

$color-grey-06:						#636363;

$color-grey-07:						#474747;

$color-grey-08:						#2b2b2b;

$color-grey-09:						#0f0f0f;

$color-black:						#000000;


$neutral-color-list: (
    "white": $color-white,
    grey-01: $color-grey-01,
    grey-02: $color-grey-02,
    grey-03: $color-grey-03,
    grey-04: $color-grey-04,
    grey-05: $color-grey-05,
    grey-06: $color-grey-06,
    grey-07: $color-grey-07,
    grey-08: $color-grey-08,
    grey-09: $color-grey-09,
    "black": $color-black
) !default;


// Utility Colours

$color-utility-positive-dark:		#0E6F22;

$color-utility-positive:		    #26883A;

$color-utility-positive-pastel:		#DFF6E4;

$color-utility-warning-dark:		#B95818;

$color-utility-warning:		        #D9681C;

$color-utility-warning-pastel:		#FDF3ED;

$color-utility-error-dark:          #9E0E0B;

$color-utility-error:               #BA210F;

$color-utility-error-pastel:        #FDEEEC;

$color-utility-neutral-dark:		#005FC6;

$color-utility-neutral:				#0192d0;

$color-utility-neutral-pastel:		#EBF4FF;

$color-utility-selection:           #FFC57D;

$color-utility-placeholder:         $color-grey-04;


$utility-color-list: (
    positive-dark: $color-utility-positive-dark,
    positive: $color-utility-positive,
    positive-pastel: $color-utility-positive-pastel,
    alert-dark: $color-utility-warning-dark,
    alert: $color-utility-warning,
    alert-pastel: $color-utility-warning-pastel,
    warning-dark: $color-utility-error-dark,
    warning: $color-utility-error,
    warning-pastel: $color-utility-error-pastel,
    neutral-dark: $color-utility-neutral-dark,
    neutral: $color-utility-neutral,
    neutral-pastel: $color-utility-neutral-pastel,
    selection: $color-utility-selection,
    placeholder: $color-utility-placeholder,
) !default;


// Body font color

$color-body-font: $color-grey-08;

$color-bg-default: $color-white;
