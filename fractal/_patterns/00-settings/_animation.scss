@use '../00-settings/colors' as *;

/* Fade in */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

/* Fade in down */
@keyframes fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translateY(-30px);
                transform: translateY(-30px);
    }
}

/* Fade in up */
@keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translateY(30px);
                transform: translateY(30px);
    }
    to {
        opacity: 1;
        -webkit-transform: translateY(0);
                transform: translateY(0);
    }
}

/* Fade in up margin */
@keyframes fadeInUpMargin {
	from {
		opacity: 0;
		margin-bottom: -20px;
	}
	to {
		opacity: 1;
		margin-bottom: 0;
	}
}


/* Grow in fade */
@keyframes growInFade {
	from {
		opacity: 0;
		-webkit-transform: scale(0.9);
		        transform: scale(0.9);
	}
	to {
		opacity: 1;
		-webkit-transform: none;
		        transform: none;
	}
}

/* ---- Highlight pulse ---- */
@keyframes highlightPulse {
	40% {
		-webkit-transform: scale(1.25);
		        transform: scale(1.25);
	}
}

/* ---- Highlight pulse with outline ---- */
@keyframes highlightPulseOutline {
	0% {
        outline: 3px solid $color-utility-selection;
        outline-offset: 4px;
        -webkit-transform: scale(1);
		        transform: scale(1);
    }
	13% {
		-webkit-transform: scale(1.25);
		        transform: scale(1.25);
	}
    33% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
    75% {
        outline: 3px solid $color-utility-selection;
        outline-offset: 4px;
    }
    100% {
        outline: 3px solid transparent;
        outline-offset: 4px;
    }
}
