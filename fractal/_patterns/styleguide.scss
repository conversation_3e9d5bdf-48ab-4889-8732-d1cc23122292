
@use "00-settings" as *;


/*
    Custom Fractal Styleguide styles
*/

.Prose {
    max-width: 55em;
}

.sg-palette-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
    grid-gap: $spacing*2;

    &--small {
        grid-gap: $spacing;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}


.sg-icon-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    grid-gap: $spacing*2;
    margin-bottom: $spacing*4;
    font-size: 1.2rem;
    text-align: center;
}

.sg-palette-list__item {
    padding: 5px;
    display: grid;
    grid-template-rows: $spacing*10 auto;
}

.sg-palette-list__item code {
    padding: 4px 6px;
    line-height: 1.3;

}

.sg-palette-list__item__swatch {
    padding: 5px;
    border-radius: 8px 8px 0 0;
}

.sg-palette-list__item__swatch code {
    background: rgba(255,255,255,.5);
    color: #000;

}



@each $key, $value in $brand-color-list {
    .sg-palette-list__item__swatch--#{$key} {
        background: #{$value};

        code::after {
            content: '#{$value}';
        }
    }
}


@each $key, $value in $neutral-color-list {
    .sg-palette-list__item__swatch--#{$key} {
        background: #{$value};

        code::after {
            content: '#{$value}';
        }
    }
}


@each $key, $value in $utility-color-list {
    .sg-palette-list__item__swatch--#{$key} {
        background: #{$value};

        code::after {
            content: '#{$value}';
        }
    }
}


/*
    Spacing
*/

@each $key, $value in $spacing-sizes {
    .var-content--#{$value}::after {
        content: '#{$value}';
    }
}


/*
    Fonts
*/

@each $key, $value in $font-families {
    .var-content--#{$key}-font::after {
        content: '#{$value}';
    }
}


@each $key, $value in $font-families {
    .var-content--#{$key}-font::after {
        content: '#{$value}';
    }
}


@each $key, $value in $font-weight-list {
    .var-content--#{$key}::after {
        content: '#{$value}';
    }
}


code {
    border-radius: 2px;
    color: green;

    pre & {
        border: 1px solid $color-grey-02;
        color: inherit;
    }
}

.styleguide {

    dt {
        font-weight: bold;
        margin-bottom: $spacing*.5;
        &::after {
            content: ':';
        }
    }

    dt {
        margin-top: $spacing*2;
    }
}
