
## Refactored structure...

Loosely based on the ITCSS architecture by from <PERSON>. Here is a quick overview... https://developer.helpscout.com/seed/glossary/itcss/

This structure is basically the ideal cascade order, with the exception being some vendor files. I've included some examples and an explanation.

### Example structure

```
00-settings-tools
    _fonts.scss
    _colors.scss
    _breakpoints.scss
    _utilities.scss

01-base
    typography
        typography-mixins
    table
    form
    image
    embed-object (eg. videos, iframes etc)

02-components
    pseudo-elements

    button
    form
    icon
    header
    footer
    primary-navigation
    posts
    page-header
    search
    embed-widgets (eg. videos, iframes etc)

03-layouts
    section
    page-layout
    card-grid
    tidy-grid
    form
        attachment-wrapper

04-templates


05-utilities
    clearfix
    wrap
    content-align

06-vendor
    splide
    flexboxgrid
    craftCommerce

```


### `00-settings-tools`
Variable configurations and global mixins for things like colors, fonts, sizes, etc… the file names would be prefixed for their content type.

if this folder was compiled on it's own and loaded, there wouldn't be any visible changes.


### `01-base`
CSS resets and normalising rules.
Style rules for bare HTML elements (like h1 or button).
No class styles here.


### `02-components`
Style rules for UI components. This will also eventually contain the twig patterns that the Craft will `{% include %}`


### `03-layouts`
Style rules for elements responsible for layout or structuring.
These layouts will more often than not contain smaller 'components'.
eg. `.header`, contains `.primary-navigation` and `.logo`.
This will, ideally, contain twig patterns that the Craft will `{% include %}`, but might not be possible.


### `04-templates`
This is for demoing the different layouts in their full templates/entry types with public URLs. Sass doesn't live in this folder, just twig and yaml demos for the Fractal pattern library. Don't worry about this for now, this is just for your info..

### `05-utilities`
These are powerful, **single purpose** classes for small/quick fixes and styles. eg. hiding something for screen-readers, animations, content alignment.


### `06-vendor`
This are styles/imports for third party styles and plugins like Splide, Craft Commerce etc.


# Notes about where exisiting content goes.

I think we avoid using classes on templates like `.channel` and `.inner` to style children html tags like `.mainbar`.

The specificity is problematic, from both a naming convention and code hierarchy/maintainability perspective. For example, I have created new craft structure sections and then been asked to style them like the posts, so now anyone come to the code late could be confused.

I think `04-pages/channel/_channel.scss` should be called something like 'narrow-layout', and the code changed from

```
.channel {
    .mainbar {
        ...
    }
}
```

to

```
.mainbar--narrow-layout {
    ...
}
```

----

Current `_utilities.scss` file maybe split into 2 files, `_vars-layout.scss` (with the spacing, header, max-width vars) and `_vars-theme.scss` ( file-path and standard radius and any other custom vars go here)

----

`_pseudo-elements.scss` should be turned into mixins then included.

