---
title: HTML & Twig Guidelines
label: HTML & Twig Guidelines

---

## HTML Syntax


Always quote attributes, even if they would work without. This reduces the chance of accidents, and is a more familiar format to the majority of developers. For all this would work (and is valid):

```
<div class=box>
```

…this format is preferred:

```
<div class="box">
```

The quotes are not required here, but err on the safe side and include them.


## Meaningful whitespace

As with our style rulesets, it is possible to use meaningful whitespace in your HTML. You can denote thematic breaks in content with three (3) empty lines, for example:

```
    <header class="page-head">
      ...
    </header>



    <main class="page-content">
      ...
    </main>



    <footer class="page-foot">
      ...
    </footer>

```

Separate independent but loosely related snippets of markup with a single empty line, for example:

```
    <ul class="primary-nav">

      <li class="primary-nav__item">
        <a href="/" class="primary-nav__link">Home</a>
      </li>

      <li class="primary-nav__item  primary-nav__trigger">
        <a href="/about" class="primary-nav__link">About</a>

        <ul class="primary-nav__sub-nav">
          <li><a href="/about/products">Products</a></li>
          <li><a href="/about/company">Company</a></li>
        </ul>

      </li>

      <li class="primary-nav__item">
        <a href="/contact" class="primary-nav__link">Contact</a>
      </li>

    </ul>
```

This allows developers to spot separate parts of the DOM at a glance, and also allows certain text editors—like Vim, for example—to manipulate empty-line-delimited blocks of markup.
