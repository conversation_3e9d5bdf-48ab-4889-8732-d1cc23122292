---
title: Craft & Fractal Twig templates 
label: Craft & Fractal Twig templates 

---

## Overall Structure

Our templates structures are loosely based on a modular '[Atomic Design](https://atomicdesign.bradfrost.com/table-of-contents/)' architecture (not to be confused with 'Atomic CSS', actually quite different)

The goal is for code to be as modular, reusable, extendable and flexible as possible to build and also come back to later. A 'system', not a bunch of pages, so if a client says. I want that existing thing over in this place, we can easily and quickly do that because that kind of request comes up often.

The [Fractal](https://fractal.build/) Pattern library is a tool we're using to try and document that design system.

Ideally, <PERSON> pulls twig templates from the Fractal folder, so that the live site code and the pattern library code is **the same**, because the less work need to maintain the library, the more likely it is to be maintained and correct (and consequently, useful for a team).

## Craft Twig template structure

Craft loads twig from the `templates/` folder ~~and so any twig files that use queries needs to live there~~. The end goal is to have all components/partials live within `fractal/`, and due to the [Fractal CraftCMS plugin](https://github.com/Karmabunny/craft-fractal), we are able to create a mapping between components/partials that live in the `fractal/` folder and use *those* within our Craft `templates/` by using @ + Fractal component handle (e.g. `@button`). You can find a GUI version of the Fractal component map by going to **Craft admin panel → Utilities → Fractal**.

### template base (aka core)
All of our templates '[extend](https://twig.symfony.com/doc/3.x/tags/extends.html)' from one twig file, `templates/01_core/main.twig`

The `template/01_core` folder has the following subfolders:


#### `template/01_core/_blocks`

This folder contains small, discrete code blocks that are either used on `main.twig` or throughout every template.

They are generally not visual but are more functional in nature and are likely entirely dependant on Craft queries. The visual blocks ideally belong in the `fractal` folder . 

eg. meta data, script tags or a redirect

#### `template/01_core/_layouts`

This folder contains the layout twig templates eg. header, primary navigation, footer, etc. 

Ideally much of these layouts would have the html moved to folders in the `fractal` folder and these partials would just be queries that then uses [`include`](https://twig.symfony.com/doc/3.x/tags/include.html) to grab it from there.


#### `template/01_core/_macros`
SURPRISE! Our core site-wide [macros](https://twig.symfony.com/doc/3.x/tags/macro.html) live here.


#### `template/01_core/_emails`
This folder contains email template for the site. For example the password reset, or new form submission emails. 


#### `template/01_core/_forms`

We normally use the Freeform Form Craft plugin and this folder contains all the required templates for that, include email templates, which exend the base templates in `template/01_core/_emails`
