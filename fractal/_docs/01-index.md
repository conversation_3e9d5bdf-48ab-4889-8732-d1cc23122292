---
title: <PERSON><PERSON><PERSON><PERSON> Front End Code Guidelines
---

Could also call it Code Standards to make it less related to 'style guide'?

Things to include in these docs:

## Recommended tools

- VSCode
    - User Snippets (See code resource folder)
    - Extensions:
        - Code Spell Checker
        - Australian English - Code Spell Checker extension
        - VS DocBlockr
        - es6-string-html
        - Gitlens
        - Indent on Paste
        - Live Share
        - Twig Language 2
        - PHP Intelephense - if you're poking around with a Sprout CMS site
        - Markdown Preview Enhanced (CTRL+K + V to open formatted preview side window) - useful for reading fractal _docs

- Image optimiser
    eg. [Optimage](https://optimage.app/) (Paid) or [Image Optim](https://imageoptim.com/mac) (Free/open source)
- Bulk image resizer
    eg. [Optimage](https://optimage.app/) (Paid)

---

## Principles...

We work as a team, be nice to the next person who has to work with your code. It could be you.

Leave notes

https://blog.shimin.io/4-ways-to-make-your-front-end-doc-site-more-useful/

https://cssguidelin.es/#selector-intent

https://github.com/Karmabunny/kb-frontend-guidelines-questionnaire

https://www.markdownguide.org/cheat-sheet

https://medium.com/eightshapes-llc/tokens-in-design-systems-25dd82d58421


https://csswizardry.com/2017/05/writing-tidy-code/

https://primer.style/css/principles

https://primer.style/css/principles/accessibility

https://primer.style/css/principles/scss



template file generator:

https://www.hygen.io/
