---
title: Design Variables
---

Something offered in Pat Lab, and also in this Fractal demo. http://bits.24ways.org/docs/tokens


## Colour Palettes
(06-abstracts/_colors.scss)

### Brand Colours

<div class="sg-palette-list">

   <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--primary">
            <code></code>
        </div>
        <code>$color-primary</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--secondary">
            <code></code>
        </div>
        <code>$color-secondary</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--alternative">
            <code></code>
        </div>
        <code>$color-alternative</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--accent-01">
            <code></code>
        </div>
        <code>$color-accent-01</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--accent-02">
            <code></code>
        </div>
        <code>$color-accent-02</code>
    </div>

</div>




### Neutral Colours

<div class="sg-palette-list sg-palette-list--small">

   <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--white">
            <code></code>
        </div>
        <code>$color-white</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-01">
            <code></code>
        </div>
        <code>$color-grey-01</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-02">
            <code></code>
        </div>
        <code>$color-grey-02</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-03">
            <code></code>
        </div>
        <code>$color-grey-03</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-04">
            <code></code>
        </div>
        <code>$color-grey-04</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-05">
            <code></code>
        </div>
        <code>$color-grey-05</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-06">
            <code></code>
        </div>
        <code>$color-grey-06</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-07">
            <code></code>
        </div>
        <code>$color-grey-07</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-08">
            <code></code>
        </div>
        <code>$color-grey-08</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--grey-09">
            <code></code>
        </div>
        <code>$color-grey-09</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--black">
            <code></code>
        </div>
        <code>$color-black</code>
    </div>

</div>


### Utility Colours

<div class="sg-palette-list">

   <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--positive">
            <code></code>
        </div>
        <code>$color-utility-positive-dark</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--positive-subtle">
            <code></code>
        </div>
        <code>$color-utility-positive</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--alert">
            <code></code>
        </div>
        <code>$color-utility-warning-dark</code>
    </div>
   <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--alert-subtle">
            <code></code>
        </div>
        <code>$color-utility-warning</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--warning">
            <code></code>
        </div>
        <code>$color-utility-error-dark</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--warning-subtle">
            <code></code>
        </div>
        <code>$color-utility-error</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--neutral">
            <code></code>
        </div>
        <code>$color-utility-neutral</code>
    </div>
    <div class="sg-palette-list__item">
        <div class="sg-palette-list__item__swatch sg-palette-list__item__swatch--neutral-subtle">
            <code></code>
        </div>
        <code>$color-utility-neutral-pastel</code>
    </div>

</div>

## Sizing note

The spacing scale is a base-8 scale. We chose an base-8 scale because eight is a highly composable number.


### Spacing
(06-abstracts/_utilities.scss)

<table>
    <thead>
        <tr>
            <th style="min-width: 16ch">Variable</th>
            <th>Value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><code>$spacing</code></td>
            <td>
                <span class="var-content--spacing"></span>
            </td>
        </tr>
        <tr>
            <td><code>$spacing-xxsmall</code></td>
            <td>
                <span class="var-content--spacing-xxsmall"></span>
            </td>
        </tr>
        <tr>
            <td><code>$spacing-xsmall</code></td>
            <td>
                <span class="var-content--spacing-xsmall"></span>
            </td>
        </tr>
        <tr>
            <td><code>$spacing-small</code></td>
            <td>
                <span class="var-content--spacing-small"></span>
            </td>
        </tr>
        <tr>
            <td><code>$spacing-medium</code></td>
            <td>
                <span class="var-content--spacing-medium"></span>
            </td>
        </tr>
        <tr>
            <td><code>$spacing-large</code></td>
            <td>
                <span class="var-content--spacing-large"></span>
            </td>
        </tr>
        <tr>
            <td><code>$spacing-xlarge</code></td>
            <td>
                <span class="var-content--spacing-xlarge"></span>
            </td>
        </tr>
        <tr>
            <td><code>$spacing-xxlarge</code></td>
            <td>
                <span class="var-content--spacing-xxlarge"></span>
            </td>
        </tr>
    </tbody>
</table>



## Fonts
(06-abstracts/_fonts.scss)

### Font family stacks

<table>
    <thead>
        <tr>
            <th style="min-width: 16ch">Variable</th>
            <th>Value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><code>$primary-font</code></td>
            <td>
                <span class="var-content--primary-font"></span>
            </td>
        </tr>
        <tr>
            <td><code>$secondary-font</code></td>
            <td>
                <span class="var-content--accent-font"></span>
            </td>
        </tr>
    </tbody>
</table>


### Font Mixins

These are the Brand typography sets you should stick to using.

These will give you the font family, weight, text decoration and text style patterns you should use.

<table>
    <thead>
        <tr>
            <th style="min-width: 16ch">Mixin name</th>
            <!-- <th>Values</th> -->
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><code>@mixin font-set('primary')</code></td>
            <!-- <td></td> -->
        </tr>
        <tr>
            <td><code>@mixin font-set('primary-bold')</code></td>
            <!-- <td></td> -->
        </tr>
        <tr>
            <td><code>@mixin font-set('accent')</code></td>
            <!-- <td></td> -->
        </tr>
        <tr>
            <td><code>@mixin font-set('accent-bold')</code></td>
            <!-- <td></td> -->
        </tr>
    </tbody>
</table>

