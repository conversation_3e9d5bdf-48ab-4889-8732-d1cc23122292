---
title: Fractal Gotchas
label: Fractal Gotchas

---
- You can order individual Fractal components/patterns inside a folder by prefixing with a number (e.g. `00-component-small.twig` and `01-component-big.twig`), but **config files cannot have prefixed numbers**. To have a config file that matches an ordered Twig component, simply omit the numbers from the config filename. E.g. `00-component-small.twig` would have a corresponding YAML config file named `component-small.config.yml`.
- Named macros break the Fractal Twig parser (https://twig.symfony.com/doc/3.x/tags/macro.html#named-macro-end-tags). Also see this job for other (currently) broken things: https://app.clickup.com/t/860qutx62
- If you name a component the same name as the folder (e.g. **button/button.twig** or **button/01-button.twig**), it hides the rest of the components/partials in the folder! Why? Who knows!
