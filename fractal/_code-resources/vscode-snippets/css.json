{
	// Place your snippets for css here. Each snippet is defined under a snippet name and has a prefix, body and
	// description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the
	// same ids are connected.
	// Example:
	// "Print to console": {
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
	"inset": {
		"prefix": "iset",
		"body": ["top: ${1:0};", "bottom: ${2:0};","left: ${3:0};", "right: ${4:0};"]
	},
	"hover": {
		"prefix": "hov",
		"body": ["&:hover,", "&:focus," "&:active {", "    ${1}", "}"]
	},
	"title": {
		"prefix": "title",
		"body": ["/*", "    ${1:title}" "*/"]
	},
	"inline comment": {
		"prefix": "/*",
		"body": ["/* [${1:1}]. */"]
	},
	"placeholder-url": {
		"prefix": "place",
		"body": ["https://picsum.photos/${1:1600}/${2:900}"]
	},
	"file-path": {
		"prefix": "filepath",
		"body": ["@webroot/assets/${1}"]
	},
	"object-fit": {
		"prefix": "obj",
		"body": ["width: 100%;", "height: 100%;", "object-fit: ${1:cover};"]
	},
	"note-comment": {
		"prefix": "note",
		"body": ["/* [${1:number}.] */"]
	},
}
