{
	// Place your snippets for html here. Each snippet is defined under a snippet name and has a prefix, body and
	// description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the
	// same ids are connected.
	// Example:
	// "Print to console": {
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
	"php tag": {
		"prefix": "php",
		"body": "<?php $1 ?>",
		"description": "basic empty php tag"
	},
	"placeholder-url": {
		"prefix": "place",
		"body": ["https://picsum.photos/${1:1600}/${2:900}"]
	},
	"placeholder-img": {
		"prefix": "place-img",
		"body": ["<img class=\"\" src=\"https://picsum.photos/${1:1600}/${2:900}\" alt=\"\">"]
	}
}
