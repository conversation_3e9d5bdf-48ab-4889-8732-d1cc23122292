{
	// Place your snippets for yaml here. Each snippet is defined under a snippet name and has a prefix, body and
	// description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the
	// same ids are connected.
	// Example:
	// "Print to console": {
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// },
	"Variants": {
		"prefix": "variants",
		"body": [
			"variants:",
			"-",
			"  name: ${1:VARIANT-NAME}",
			"  context:",
			"    ${2:VARIABLE-HANDLE}:",
		]
	},
	"One variant": {
		"prefix": "variant",
		"body": [
			"-",
			"  name: ${1:VARIANT-NAME}",
			"  context:",
			"    ${2:VARIABLE-HANDLE}:",
		]
	},
	"Title": {
		"prefix": "title",
		"body": [
			"title: $1",
		]
	},
	"Context": {
		"prefix": "context",
		"body": [
			"context: $1",
		]
	},
	"Status": {
		"prefix": "status",
		"body": [
			"status: ${1:wip}",
		]
	},
	"Modifier": {
		"prefix": "modifier",
		"body": [
			"modifier: '$1'",
		]
	},
	"Preview": {
		"prefix": "preview",
		"body": [
			"preview: '@preview-${1:grey}'",
		]
	}
}
