<?php
namespace modules;

use Craft;
use craft\events\RegisterTemplateRootsEvent;
use craft\events\TemplateEvent;
use craft\web\View;
use modules\TwigExtension;
use wrav\oembed\services\OembedService;
use yii\base\Event;

/**
 * This currently loads in fractal patterns and our twig extensions.
 *
 * If you've got site specific code please create a fresh module.
 */
class Module extends \yii\base\Module
{
    /** @inheritdoc */
    public function init()
    {
        // This is required for controller discovery.
        Craft::setAlias('@modules', __DIR__);

        if (Craft::$app->getRequest()->getIsConsoleRequest()) {
            $this->controllerNamespace = 'modules\\console\\controllers';
        } else {
            $this->controllerNamespace = 'modules\\controllers';
        }

        parent::init();

        $extension = new TwigExtension();
        Craft::$app->view->registerTwigExtension($extension);

        Craft::setAlias('@patterns', CRAFT_BASE_PATH . '/fractal/_patterns');

        // Load in patterns template root.
        Event::on(
            View::class,
            View::EVENT_REGISTER_SITE_TEMPLATE_ROOTS,
            function(RegisterTemplateRootsEvent $event) {
                $event->roots['patterns'] = CRAFT_BASE_PATH . '/fractal/_patterns';
            }
        );

        // Render oembed tags using the oembed plugin.
        // These tags are usually produced by CKeditor.
        if (Craft::$app->plugins->isPluginInstalled('oembed')) {
            Event::on(
                View::class,
                View::EVENT_AFTER_RENDER_PAGE_TEMPLATE,
                function(TemplateEvent $event) {
                    if (str_contains($event->output, '<oembed')) {
                        $service = OembedService::instance();
                        $event->output = $service->parseTags($event->output);
                    }
                }
            );
        }
    }
}
