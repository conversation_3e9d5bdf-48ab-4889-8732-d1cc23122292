<?php
/**
 * @link      https://github.com/Karmabunny
 * @copyright Copyright (c) 2020 Karmabunny
 */

namespace modules;

use Craft;
use craft\helpers\UrlHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

/**
 *
 */
class TwigExtension extends AbstractExtension
{

    /** @inheritdoc */
    public function getFilters() {
        return [
            new TwigFilter('cc2kc', [$this, 'cc2kc']),
            new TwigFilter('kc2cc', [$this, 'kc2cc']),
            new TwigFilter('cc_spacer', [$this, 'ccSpacer']),
            new TwigFilter('truncate', [$this, 'truncate']),
            new TwigFilter('json_pretty', [$this, 'jsonPretty']),
            new TwigFilter('busty', [$this, 'bustyUrl']),
        ];
    }


    /** @inheritdoc */
    public function getFunctions() {
        return [
            new TwigFunction('truncate', [$this, 'truncate']),
            new TwigFunction('json_pretty', [$this, 'jsonPretty']),
            new TwigFunction('busty', [$this, 'bustyUrl']),
        ];
    }


    /**
     * Trim a string if it's too long. Adds a ... ellipsis character.
     *
     * @param null|string $text
     * @param int $length
     * @return string
     */
    public function truncate(?string $text, int $length): string
    {
        if (empty($text)) {
            return '';
        }
        else if (mb_strlen($text) > $length) {
            return mb_substr($text, 0, $length - 1) . '…';
        }
        else {
            return $text;
        }
    }


    /**
     * Shorthand pretty print JSON.
     *
     * @param mixed $data
     * @return string
     */
    public function jsonPretty($data): string
    {
        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }


    /**
     * Changes camelCase to kebab-case
     *
     * @param string $var
     * @return string
     */
	public function cc2kc(?string $var)
	{
		if (is_string($var) && strlen($var))
		{
			$var = preg_replace_callback('/(^|[a-z])([A-Z])/', function($matches) {
				return strtolower(strlen("\\1") ? "$matches[1]-$matches[2]" : "\\2");
			},
			$var);
		}

		return $var;
	}


     /**
     * Changes kebab-case to camelCase
     *
     * @param string $var
     * @return string
     */
	public function kc2cc(?string $var)
	{
		if (is_string($var) && strlen($var))
		{
			$var = preg_replace_callback('/(^|[a-z])([-])([a-z])/', function($matches) {
				return strlen("\\1") ? "$matches[1]" . ucfirst("$matches[3]") : "\\3";
			},
			$var);
		}

		return $var;
	}


    /**
     * Take a camel case string and add spaces between the words
     *
     * @param string $var
     * @return string
     */
	public function ccSpacer(?string $var)
	{
		if (is_string($var) && strlen($var))
		{
			$var = preg_replace_callback('/(^|[a-z])([A-Z])/', function($matches) {
				return strtolower(strlen("\\1") ? "$matches[1] $matches[2]" : "\\2");
			},
			$var);
		}

		return $var;
	}


    /**
     * Create a url with a timestamp.
     *
     * Use this for assets in the /web folder. Nothing else.
     *
     * @return string
     */
    public function bustyUrl($url): string
    {
        $file = Craft::getAlias('@webroot/' . trim($url, '/'));

        if ($file) {
            $mtime = @filemtime($file) ?: null;
        }

        return UrlHelper::siteUrl($url, ['_v' => $mtime ?? 0]);
    }

}
