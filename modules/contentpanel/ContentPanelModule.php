<?php
/**
 * @link      https://github.com/Karmabunny
 * @copyright Copyright (c) 2022 Karmabunny
 */

namespace modules\contentpanel;

use modules\contentpanel\fields\PanelSettingsField;
use modules\contentpanel\fields\PanelImagePreferencesField;

use Craft;
use craft\events\RegisterComponentTypesEvent;
use craft\events\RegisterTemplateRootsEvent;
use craft\events\TemplateEvent;
use craft\services\Fields;
use craft\web\View;

use yii\base\Event;
use yii\base\InvalidConfigException;

/**
 * Class ContentPanel
 *
 * <AUTHOR>
 * @package   ContentPanel
 * @since     1.0.0
 *
 */
class ContentPanelModule extends \yii\base\Module
{
    /**
     * @inheritdoc
     */
    public function init()
    {

        // Define a custom alias named after the namespace
        Craft::setAlias('@contentpanel', __DIR__);

        // Set the controllerNamespace based on whether this is a console or web request
        $this->controllerNamespace =
            Craft::$app->getRequest()->getIsConsoleRequest()
            ? 'modules\\contentpanel\\console\\controllers'
            : 'modules\\contentpanel\\controllers';

        // Templates
        // Register the templates path,
        // This will tell craft where to look for the template views for this module
        Event::on(
            View::class,
            View::EVENT_REGISTER_CP_TEMPLATE_ROOTS,
            function(RegisterTemplateRootsEvent $event) {
                $event->roots['contentpanel'] = __DIR__ . '/templates';
            }
        );

        // Custom field types.
        // Register additional custom fields
        // This will add the field to the admin field type dropdown selector when adding a new field
        Event::on(
            Fields::class,
            Fields::EVENT_REGISTER_FIELD_TYPES,
            function(RegisterComponentTypesEvent $event) {
                $event->types[] = PanelSettingsField::class;
                $event->types[] = PanelImagePreferencesField::class;
            }
        );

        // Register the asset and required media needed to be included with this module
        if (Craft::$app->getRequest()->getIsCpRequest()) {
            Event::on(
                View::class,
                View::EVENT_BEFORE_RENDER_TEMPLATE,
                function (TemplateEvent $event) {
                    try {
                        Craft::$app->getView()->registerAssetBundle(ContentPanelFieldAssets::class);
                    } catch (InvalidConfigException $e) {
                        Craft::error(
                            'Error registering AssetBundle - '.$e->getMessage(),
                            __METHOD__
                        );
                    }
                }
            );
        }

        Craft::info(
            'content-panel',
            '{name} module loaded',
            ['name' => 'Site']
        );

        parent::init();

    }

}
