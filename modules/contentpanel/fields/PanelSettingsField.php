<?php
/**
 * @link      https://karmabunny.com.au
 * @copyright Copyright (c) 2022 Karmabunny
 */

namespace modules\contentpanel\fields;

use Craft;
use craft\base\Field;
use craft\base\ElementInterface;

/**
 * Class PanelSettingsField.php
 *
 * The Content Panel's panel settings field
 * Set the aesthetics:
 *  - layout,
 *  - background colour,
 *  - content alignment,
 *  - content order on mobile and
 *  - whether to include a column gap.
 *
 * @package   ContentPanel - modules\contentpanel\fields
 * @since     1.0.0
 */

class PanelSettingsField extends Field
{

    const DEFAULTS = [
        'layout' => 'fullWidthCopy',
        'background' => 'white',
        'alignment' => 'top',
        'mobileOrder' => 'copyFirst',
        'gap' => true,
        'width' => '',
    ];

    /**
     * @var array
     * This is the variable that stores the default settings values
     */
    public $defaults = self::DEFAULTS;

    /**
     * @inheritdoc
     */
    public function init(): void
    {
        parent::init();
    }

    /**
     * @inheritdoc
     */
    public static function displayName(): string
    {
        return 'Panel Settings';
    }

    /**
     * @inheritdoc
     */
    public function getSettingsHtml():? string
    {
        // Set up the settings field data
        $gapField = $this->setGapFieldSettings();
        $backgroundField = $this->setBackgroundFieldSettings();
        $mobileOrderField = $this->setMobileOrderFieldSettings();
        $alignmentField = $this->setAlignmentFieldSettings();
        $panelWidthField = $this->setPanelWidthFieldSettings();

        // Render the settings template
        return Craft::$app->getView()->renderTemplate('contentpanel/_fields/panel_settings_settings.twig',[
            'name' => 'defaults',
            'handle' => $this->handle,
            'defaults' => $this->defaults,
            'gapField' => $gapField,
            'backgroundField' => $backgroundField,
            'mobileOrderField' => $mobileOrderField,
            'alignmentField' => $alignmentField,
            'panelWidthField' => $panelWidthField,
        ]);
    }

     /**
     * @inheritdoc
     * @param mixed $value
     * @param ElementInterface|null $element
     * @return string
     *
     * Create the admin entry edit field view
     */
    public function getInputHtml($value, ElementInterface $element = null): string
    {
        $view = Craft::$app->getView();

        // Set up the field settings
        $layoutField = $this->setLayoutFieldSettings();
        $gapField = $this->setGapFieldSettings();
        $backgroundField = $this->setBackgroundFieldSettings();
        $mobileOrderField = $this->setMobileOrderFieldSettings();
        $alignmentField = $this->setAlignmentFieldSettings();
        $panelWidthField = $this->setPanelWidthFieldSettings();

        // Fetch the view template and set up the passed data.
        return $view->renderTemplate('contentpanel/_fields/panel_settings_selector.twig', [
            'name' => $this->handle,
            'panelSettings' => $value,
            'layoutField' => $layoutField,
            'gapField' => $gapField,
            'backgroundField' => $backgroundField,
            'mobileOrderField' => $mobileOrderField,
            'alignmentField' => $alignmentField,
            'panelWidthField' => $panelWidthField,
        ]);
    }

    /**
     * @inheritdoc
     *
     * This file (modules/contentpanel/fields/PanelSettingsField.php) is to create the admin field itself,
     * This file does not hold the field's value.
     * However, CraftCMS passes the field value through the vendor/craftcms/cms/src/base/Field.php class
     * It uses the serializeValue and normalizeValue functions to ensure the field value is valid
     **/
    public function normalizeValue(mixed $value, ?ElementInterface $element = null): mixed
    {
        // The field's value returns as a JSON string.
        // Parse the field's value.
        if (is_string($value)) {
            $value = json_decode($value, true);
        }
        // Bad parsing.
        if (!$value) {
            $value = [];
        }

        /**
         * Return the full array of panel settings.
         * It is a combination, a triple array merge.
         * This is because the layout value isn't included in the field settings
         * This array includes the constant DEFAULTS
         * Then applies the panel settings defaults onto of it
         * Then applies the actual values set
         */
        return array_merge(self::DEFAULTS, $this->defaults, $value);
    }


     /**
     * Set the Layout colour field options
     *
     * * @return array
     */
    protected function setLayoutFieldSettings(): array
    {
        $layoutFieldSettings = [
            'label' => 'Layout',
            'options' => [
                [
                    'label' => 'Full Width - Copy',
                    'value' => 'fullWidthCopy'
                ],
                [
                    'label' => 'Full Width - Media',
                    'value' => 'fullWidthMedia'
                ],
                [
                    'label' => 'Half - Media Right',
                    'value' => 'halfMediaRight'
                ],
                [
                    'label' => 'Half - Media Left',
                    'value' => 'halfMediaLeft'
                ],
                [
                    'label' => 'Third - Media Right',
                    'value' => 'thirdMediaRight'
                ],
                [
                    'label' => 'Third - Media Left',
                    'value' => 'thirdMediaLeft'
                ],
                [
                    'label' => 'Third - Copy Right',
                    'value' => 'thirdCopyRight'
                ],
                [
                    'label' => 'Third - Copy Left',
                    'value' => 'thirdCopyLeft'
                ],
            ],
        ];

        return $layoutFieldSettings;
    }

     /**
     * Set the column gap lightswitch field options
     *
     * * @return array
     */
    protected function setGapFieldSettings(): array
    {
        $gapFieldSettings = [
            'label' => 'Column Gap',
            'offLabel' => 'No',
            'onLabel' => 'Yes',
            'default' => true,
        ];

        return $gapFieldSettings;
    }

    /**
     * Set the background colour field options
     *
     * * @return array
     */
    protected function setBackgroundFieldSettings(): array
    {
        $backgroundFieldSettings = [
            'label' => 'Background Colour',
            'options' => [
                [
                    'label' => 'Default',
                    'value' => 'default'
                ],
                [
                    'label' => 'Light Grey',
                    'value' => 'default-tint-01'
                ],
                [
                    'label' => 'Navy',
                    'value' => 'primary'
                ],
                [
                    'label' => 'Orange',
                    'value' => 'secondary'
                ],
                [
                    'label' => 'Pink',
                    'value' => 'alternative'
                ],
                [
                    'label' => 'Green',
                    'value' => 'accent-01'
                ],
                [
                    'label' => 'Yellow',
                    'value' => 'accent-02'
                ],
            ],
        ];

        return $backgroundFieldSettings;
    }

    /**
     * Set the mobile order field options
     *
     * * @return array
     */
    protected function setMobileOrderFieldSettings(): array
    {
        $mobileOrderFieldSettings = [
            'label' => 'Mobile Order',
            'options' => [
                [
                    'label' => 'Copy First',
                    'value' => 'copyFirst'
                ],
                [
                    'label' => 'Media First',
                    'value' => 'mediaFirst'
                ],
            ],
        ];

        return $mobileOrderFieldSettings;
    }

    /**
     * Set the vertical alignment field options
     *
     * * @return array
     */
    protected function setAlignmentFieldSettings(): array
    {
        $AlignmentOrderFieldSettings = [
            'label' => 'Vertical Alignment',
            'options' => [
                [
                    'label' => 'Top',
                    'value' => 'top',
                ],
                [
                    'label' => 'Center',
                    'value' => 'center'
                ],
                [
                    'label' => 'Bottom',
                    'value' => 'bottom'
                ],
            ],
        ];

        return $AlignmentOrderFieldSettings;
    }

     /**
     * Set the panel width field options
     *
     * * @return array
     */
    protected function setPanelWidthFieldSettings(): array
    {
        $PanelWidthFieldSettings = [
            'label' => 'Panel Width',
            'options' => [
                [
                    'label' => 'Line Length',
                    'value' => '',
                ],
                [
                    'label' => 'Container Width',
                    'value' => 'container-width'
                ],
                [
                    'label' => 'Full Bleed',
                    'value' => 'full-bleed'
                ],
            ],
        ];

        return $PanelWidthFieldSettings;
    }
}
