<?php
/**
 * @link      https://karmabunny.com.au
 * @copyright Copyright (c) 2022 Karmabunny
 */

namespace modules\contentpanel\fields;

use Craft;
use craft\base\Field;
use craft\base\ElementInterface;

/**
 * Class PanelImagePreferencesField.php
 *
 * @package   ContentPanel - modules\contentpanel\fields
 * @since     1.0.0
 */

class PanelImagePreferencesField extends Field
{

    const IMAGE_DEFAULTS = [
        'ratio' => '4by3',
        'displaySlider' => false,
        'columns' => '1',
        'showCaptions' => false,
        'enablePopup' => false,
    ];

    /**
     * @var array
     * This is the variable that stores the default settings values
     */
    public $imageDefaults = self::IMAGE_DEFAULTS;

    /**
     * @inheritdoc
     */
    public function init(): void
    {
        parent::init();
    }

    /**
     * @inheritdoc
     */
    public static function displayName(): string
    {
        return 'Image Preferences';
    }


    /**
     * @inheritdoc
     */
    public function getSettingsHtml():? string
    {
        // Set up the settings field data
        $imageRatio = $this->setImageRatioSettings();
        $imageDisplaySlider = $this->setImageDisplaySliderSettings();
        $imageColumns = $this->setImageColumnsSettings();
        $imageCaptions = $this->setImageCaptionsSettings();
        $enablePopup = $this->setEnablePopupSettings();

        // Render the settings template
        return Craft::$app->getView()->renderTemplate('contentpanel/_fields/image_preferences_settings.twig',[
            'name' => 'imageDefaults',
            'imageDefaults' => $this->imageDefaults,
            'ratioField' => $imageRatio,
            'displaySliderField' => $imageDisplaySlider,
            'columnsField' => $imageColumns,
            'showCaptionsField' => $imageCaptions,
            'enablePopupField' => $enablePopup
        ]);
    }

    /**
     * @inheritdoc
     * @param mixed $value
     * @param ElementInterface|null $element
     * @return string
     *
     * Create the admin entry edit field view
     */
    public function getInputHtml($value, ElementInterface $element = null): string
    {
        $view = Craft::$app->getView();

        // Set up the field settings
        $imageRatio = $this->setImageRatioSettings();
        $imageDisplaySlider = $this->setImageDisplaySliderSettings();
        $imageColumns = $this->setImageColumnsSettings();
        $imageCaptions = $this->setImageCaptionsSettings();
        $enablePopup = $this->setEnablePopupSettings();

        // Fetch the view template and set up the passed data.
        return $view->renderTemplate('contentpanel/_fields/image_preferences_selector.twig', [
            'name' => $this->handle,
            'imagePreferences' => $value,
            'ratioField' => $imageRatio,
            'displaySliderField' => $imageDisplaySlider,
            'columnsField' => $imageColumns,
            'showCaptionsField' => $imageCaptions,
            'enablePopupField' => $enablePopup
        ]);
    }

    /**
     * @inheritdoc
     **/
    public function normalizeValue(mixed $value, ?ElementInterface $element = null): mixed
    {
        // The field's value returns as a JSON string.
        // Parse the field's value.
        if (is_string($value)) {
            $value = json_decode($value, true);
        }
        // Bad parsing.
        if (!$value) {
            $value = [];
        }

        /**
         * Return the full array of panel settings.
         * It is a combination, a triple array merge.
         */
        return array_merge(self::IMAGE_DEFAULTS, $this->imageDefaults, $value);
    }


    /*
     * Set the Image Ratio options
     *
     * @return array
    **/
    protected function setImageRatioSettings(): array
    {

        $ImageRatioSettings = [
            'label' => 'Image Ratio',
            'options' => [
                [
                    'label' => 'Square',
                    'value' => 'square'
                ],
                [
                    'label' => '4/3',
                    'value' => '4by3'
                ],
                [
                    'label' => '3/2',
                    'value' => '3by2'
                ],
                [
                    'label' => '16/9',
                    'value' => '16by9'
                ],
                [
                    'label' => '2/1',
                    'value' => '2by1'
                ],
                [
                    'label' => 'None',
                    'value' => ''
                ],
            ]
        ];

        return $ImageRatioSettings;

    }

    /*
     * Set the Image Display as Slider lightswitch
     *
     * @return array
    **/
    protected function setImageDisplaySliderSettings(): array
    {

        $ImageDisplaySliderSettings = [
            'label' => 'Display Slider',
            'offLabel' => 'Grid',
            'onLabel' => 'Slider',
            'default' => false,
        ];

        return $ImageDisplaySliderSettings;

    }

    /*
     * Set the Image Columns options
     *
     * @return array
    **/
    protected function setImageColumnsSettings(): array
    {

        $ImageColumnsSettings = [
            'label' => 'Image Columns',
            'defaultValue' => 1,
            'min' => 1,
            'max' => 6,
            'decimals' => 0
        ];

        return $ImageColumnsSettings;

    }

    /*
     * Set the Image Captions options
     *
     * @return array
    **/
    protected function setImageCaptionsSettings(): array
    {

        $ImageCaptionsSettings = [
            'label' => 'Image Captions',
            'offLabel' => 'Hide Captions',
            'onLabel' => 'Show Captions',
            'default' => false,
        ];

        return $ImageCaptionsSettings;

    }

    /*
     * Set the Image Captions options
     *
     * @return array
    **/
    protected function setEnablePopupSettings(): array
    {

        $EnablePopupSettings = [
            'label' => 'Enable Popup',
            'offLabel' => 'No',
            'onLabel' => 'Yes',
            'default' => false,
        ];

        return $EnablePopupSettings;

    }
}
