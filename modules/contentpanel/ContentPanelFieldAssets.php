<?php
/**
 * @link      https://github.com/Karmabunny
 * @copyright Copyright (c) 2022 Karmabunny
 *
 * An Asset Bun­dle is a col­lec­tion of resources such as CSS, JavaScript, images, etc. that need to be loaded and avail­able on the fron­tend.
 *
 * Plugins are supposed to be installed above the web root, which ensures that their files can’t be accessed directly via HTTP requests.
 * But sometimes you need to be able to access front end resources such as images, JS and CSS.
 */

namespace modules\contentpanel;

use craft\web\AssetBundle;
use craft\web\assets\cp\CpAsset;

/**
 * Class ContentPanelFieldAssets
 *
 * <AUTHOR>
 * @package   ContentPanel
 * @since     1.0.0
 *
 */

class ContentPanelFieldAssets extends AssetBundle
{
    /**
     * @inheritdoc
     */
    public function init()
    {
        $this->sourcePath = "@modules/contentpanel/assets";

        $this->depends = [
            CpAsset::class,
        ];

        // define the relative path to CSS/JS files that should be registered with the page
        // when this asset bundle is registered
        $this->css = [
            'css/LayoutField.css',
            'css/BackgroundColourField.css',
            'css/VerticalAlignmentField.css',
            'css/MobileOrderField.css',
            'css/PanelWidthField.css',
            'css/PanelSettingsField.css',
            'css/ImagePreferencesSettings.css',
        ];

        $this->js = [
            'js/AdvancedOptionsToggle.js',
            'js/ContentToggle.js',
        ];

        parent::init();
    }
}
