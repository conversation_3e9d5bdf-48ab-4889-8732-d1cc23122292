!(function(cb) {
    if (document.readyState === 'complete') setTimeout(cb);
    else document.addEventListener('DOMContentLoaded', function ready() {
         document.removeEventListener('DOMContentLoaded', ready);
         cb();
    });
 })(function () {

    /* ----------------------------------
    Observe when panel items are added and removed from the page content matrix
    ---------------------------------- */

    // Select the node that will be observed for mutations
    const blocksContainer = document.querySelector('.blocks');
    let allMatrixBlocks;
    let previousMatrixBlockLength;
    if(!!blocksContainer) {
        allMatrixBlocks = blocksContainer.querySelectorAll('.matrixblock');
        previousMatrixBlockLength = allMatrixBlocks.length;

        // Options for the observer (which mutations to observe)
        const blocksConfig = { attributes: false, childList: true, subtree: false };

        // Callback function to execute when mutations are observed
        const blocksCallback = (mutationList, observe) => {
            for (const mutation of mutationList) {
                if (mutation.type === 'childList') {
                    // Update the matrix block list now that the length has been observed as changed
                    allMatrixBlocks = blocksContainer.querySelectorAll('.matrixblock');
                    let lastMatrixBlock = allMatrixBlocks[allMatrixBlocks.length - 1];
                    if(!!lastMatrixBlock && lastMatrixBlock.getAttribute('data-type-name') == 'Panel Layout') {
                        // Only want to run the function when it is now panel layouts added.
                        if(allMatrixBlocks.length > previousMatrixBlockLength) {
                            let panel = lastMatrixBlock;
                            let panelSettings = lastMatrixBlock.querySelector('div[data-id="panel-settings"]');
                            // Close the advanced panel options
                            hideAdvancedOptions(panelSettings);
                            // Initialise the image field toggle
                            initialiseImageToggle(panel);
                        }
                    }
                    // Update the matrix block counter to account for the most recent addition or subtraction.
                    previousMatrixBlockLength = allMatrixBlocks.length;
                }
            }
        };

        // Create an observer instance linked to the blocksCallback function
        const blocksObserver = new MutationObserver(blocksCallback);

        // Start observing the target node for configured mutations
        blocksObserver.observe(blocksContainer, blocksConfig);
    }


    /* ----------------------------------
    Initialise the panel advanced settings toggle and hide them by default
    ---------------------------------- */
    let panelSettings = document.querySelectorAll('div[data-id="panel-settings"]');
    panelSettings.forEach(panel => {
        hideAdvancedOptions(panel);
    });


    /* ----------------------------------
    When the panel settings button is pressed, toggle the panel settings
    ---------------------------------- */
    // Listen to all click events on the document
    document.addEventListener('click', function(event) {
        if (event.target.getAttribute('data-id') === 'panel-content-advanced-options__toggle-button') {
            // doo the thing.
            toggleAdvancedOptions(event.target);
        }
    });

    function toggleAdvancedOptions(target) {
        let panel = target.closest('div[data-id="panel-settings"]');
        let optionsList = panel.querySelector('[data-id="panel-content-advanced-options"]');

        if(optionsList.getAttribute('aria-expanded') == 'false') {
            showAdvancedOptions(panel);
        } else {
            hideAdvancedOptions(panel);
        }
    }

    function hideAdvancedOptions(panel) {
        let optionsList = panel.querySelector('[data-id="panel-content-advanced-options"]');
        let button = panel.querySelector('[data-id="panel-content-advanced-options__toggle-button"]');

        if(!optionsList.style.height) {
            // fetch and set the expanded height
            optionsList.style.height = optionsList.offsetHeight + 'px';
        }

        optionsList.setAttribute('aria-expanded', false);
        button.querySelector('.panel-content-advanced-options__toggle-button__label').innerHTML = 'Show Advanced';
        button.setAttribute('aria-pressed', false);
        button.setAttribute('aria-expanded', false);
    }

    function showAdvancedOptions(panel) {
        let optionsList = panel.querySelector('[data-id="panel-content-advanced-options"]');
        let button = panel.querySelector('[data-id="panel-content-advanced-options__toggle-button"]');

        // Set the height to scrollHeight to animate open
        optionsList.style.height = optionsList.scrollHeight + 'px';

        optionsList.setAttribute('aria-expanded', true);
        button.querySelector('.panel-content-advanced-options__toggle-button__label').innerHTML = 'Hide Advanced';
        button.setAttribute('aria-pressed', true);
        button.setAttribute('aria-expanded', true);
    }

    // Add a resize event listener to update the height
    window.addEventListener('resize', function() {
        document.querySelectorAll('div[data-id="panel-settings"]').forEach(panel => {
            let optionsList = panel.querySelector('[data-id="panel-content-advanced-options"]');
            optionsList.style.height = 'auto';
            optionsList.style.height = optionsList.scrollHeight + 'px';
        });
    });

    /* ----------------------------------
    Initialise the image field toggle on page load
    ---------------------------------- */
    if(!!allMatrixBlocks && allMatrixBlocks.length > 0) {
        allMatrixBlocks.forEach(block => {
            initialiseImageToggle(block);
        })
    }

    function initialiseImageToggle(panel) {
        let imagePreferencesField = panel.querySelector('[data-attribute="imagePreferences"]');
        // Select the node that will be observed for mutations
        let imageListElements = panel.querySelector('[data-attribute="imageList"] .elements');

        if(!!imagePreferencesField && !!imageListElements) {
            if(!!imageListElements) {
                // Options for the observer (which mutations to observe)
                const imagesConfig = { attributes: false, childList: true, subtree: true };

                // Callback function to execute when mutations are observed
                const imagesCallback = (mutationList, observe) => {
                    for (const mutation of mutationList) {
                        if (mutation.type === 'childList') {
                            toggleImagePreferenceFields(imageListElements, imagePreferencesField);
                        }
                    }
                };

                // Create an observer instance linked to the imagesCallback function
                const observer = new MutationObserver(imagesCallback);

                // Start observing the target node for configured mutations
                observer.observe(imageListElements, imagesConfig);

                toggleImagePreferenceFields(imageListElements, imagePreferencesField);
            }
        }
    }

    function toggleImagePreferenceFields(imageListElements, imagePreferencesField) {
        let pluralImagePreferencesFields = imagePreferencesField.querySelectorAll('.image-preferences__field--plural');
        let singleImagePreferencesFields = imagePreferencesField.querySelectorAll('.image-preferences__field--single');
        // Grab the block to know if the field has a set layout
        let block = imageListElements.closest('.matrixblock');
        let fieldsetValue = block.querySelector('[data-attribute="panelSettings-layout"] input:checked').value;

        if (fieldsetValue === 'fullWidthCopy') {
            // Don't show the image preferences as the layout field has determined there should be no media
            imagePreferencesField.style.display = 'none';
        } else {
            if(imageListElements.children.length > 1) {
                imagePreferencesField.style.display = null;
                pluralImagePreferencesFields.forEach(field => {
                    field.style.display = null;
                });
                singleImagePreferencesFields.forEach(field => {
                    field.style.display = null;
                });
            } else if(imageListElements.children.length == 1) {
                imagePreferencesField.style.display = null;
                pluralImagePreferencesFields.forEach(field => {
                    field.style.display = 'none';
                });
                singleImagePreferencesFields.forEach(field => {
                    field.style.display = null;
                });
            } else if(imageListElements.children.length == 0) {
                console.log('toggleImagePreferenceFields: ' + imageListElements.children.length);
                imagePreferencesField.style.display = 'none';
            }
        }
    }
});
