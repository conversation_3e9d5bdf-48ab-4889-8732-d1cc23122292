/*
 * Note - re: why I've used [data-id=""]
 * Craft uses namespaces in the admin views
 *   https://craftcms.com/docs/4.x/dev/filters.html#namespace
 *   Namespaces input names and other HTML attributes, as well as CSS selectors.
 * Which in practical terms means the css selectors are prepended using js on page load with 'fields-' or '',
 * Which imo is a bit...
 * Attributes aren't subject to this, so I've used data-id instead
**/

[data-id="panel-settings"] .field,
[data-id="panel-settings"] fieldset,
[data-id="panel-settings"] .flex-fields .field,
[data-id="panel-settings"] .flex-fields fieldset {
    margin-top: 0;
}

/* --- toggle button ---- */
[data-id="panel-content-advanced-options__toggle-button"] {
    margin-top: auto;
}

.panel-content-advanced-options__toggle-button__label {
    margin-bottom: 0;
    display: inline-grid;
    grid-auto-flow: column;
    align-items: center;
    color: inherit;
    font-weight: 700;
    pointer-events: none;
}

.panel-content-advanced-options__toggle-button__label:after {
    margin-left: 6px;
}

.panel-content-advanced-options__toggle-button__label:after {
    top: -1px;
}

.panel-content-advanced-options__toggle-button__label:after {
    border: solid;
    border-width: 0 2px 2px 0;
    content: "";
    display: block;
    font-size: 0;
    height: 7px;
    opacity: .8;
    position: relative;
    -webkit-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    transition: transform 200ms ease;
    width: 7px;
}

[data-id="panel-content-advanced-options__toggle-button"][aria-pressed="true"] .panel-content-advanced-options__toggle-button__label:after {
    -webkit-transform: rotate(225deg);
    -o-transform: rotate(225deg);
    transform: rotate(225deg);
}

[data-id="panel-content-advanced-options__toggle-button"][aria-pressed="true"] {
    outline: 2px dashed var(--medium-text-color);
    outline-offset: 2px;
}

/* --- content toggle ---- */
[data-id="panel-content-advanced-options"] {
    padding-top: var(--row-gap) !important;
    overflow: hidden;
    transition:
        height 250ms ease;
    border-left: 2px dashed transparent;
    border-radius: 3px;
}

[data-id="panel-content-advanced-options"][aria-expanded="false"] {
    height: 0 !important;
    overflow: hidden;
}

[data-id="panel-content-advanced-options"][aria-expanded="true"] {
    border-color: var(--medium-text-color);
}

[data-id="panel-content-advanced-options"] .field,
[data-id="panel-content-advanced-options"] fieldset,
#content [data-id="panel-settings"]:not(.meta)>.flex-fields>.field,
.panel-content-field-settings .field,
.panel-content-field-settings fieldset,
#content .panel-content-field-settings:not(.meta)>.flex-fields>.field {
    flex: 0 1 auto;
    width: auto;
}

/* The adding the field settings */
.panel-content-field-settings .field,
.panel-content-field-settings fieldset {
    margin-top: 0;
}

.panel-content-field-settings p {
    margin-top: 0;
}

[id*="fields-pageContent-blocks"] > .heading {
    margin-top: 0;
}

[id*="fields-panelSettings-background-label"][aria-hidden="true"] {
    visibility: hidden;
    opacity: 0;
    transition:
        opacity 25ms ease;
}

[data-id="panel-content-advanced-options"][aria-expanded="true"] [id*="fields-panelSettings-background-label"][aria-hidden="true"] {
    visibility: visible;
    opacity: 1;
}
