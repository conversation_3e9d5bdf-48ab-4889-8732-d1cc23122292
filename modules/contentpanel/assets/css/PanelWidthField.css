/* ---- override the craftcms styles ---- */
.radio-group[id*="panelSettings-width"] input.radio {
    opacity: 1;
    position: static;
    height: auto;
    width: auto;
    cursor: pointer;
}

.radio-group[id*="panelSettings-width"] input.radio+label {
    position: absolute;
}

.radio-group[id*="panelSettings-width"] input.radio+label:after,
.radio-group[id*="panelSettings-width"] input.radio+label:before {
    background-clip: initial;
    border-radius: 0;
    background: none;
    position: absolute;
    bottom: 100%;
    top: auto;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-style: solid;
}

.radio-group[id*="panelSettings-width"] input.radio+label:before {
    border-width: 0 7px 7px 7px;
    border-color: transparent transparent rgba(96,125,159,.25) transparent;
    z-index: -1;
}

.radio-group[id*="panelSettings-width"] input.radio+label:after {
    bottom: calc(100% - 1px);
    border-width: 0 6px 6px 6px;
    border-color: transparent transparent var(--white) transparent;
}

body.ltr .radio-group[id*="panelSettings-width"] input.radio+label {
    padding-left: 0.4em;
}

/* ---- Custom styles  ---- */
.radio-group[id*="panelSettings-width"] {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    gap: 0;
}

.radio-group[id*="panelSettings-width"] > div {
    flex: 0 0 auto;
    position: relative;
    display: flex;
    flex-direction: column;
    margin-right: -1px;
}

.radio-group[id*="panelSettings-width"] > div:last-child {
    margin-right: 0;
}

.radio-group[id*="panelSettings-width"] > div:first-child input[type="radio"]:before {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}

.radio-group[id*="panelSettings-width"] > div:last-child input[type="radio"]:before {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

.radio-group[id*="panelSettings-width"] label {
    top: calc(100% + 7px);
    left: 50%;
    transform: translateX(-50%);
    line-height: normal;
    font-size: 12px;
    white-space: nowrap;
    background-color: var(--white);
    color: var(--text-color);
    border-radius: 3px;
    padding: 0.2em 0.4em;
    border: 1px solid rgba(96,125,159,.25);
    z-index: 1;
    opacity: 0;
    pointer-events: none;
}

.radio-group[id*="panelSettings-width"] input:hover + label {
    opacity: 1;
}

.radio-group[id*="panelSettings-width"] input[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
}

.radio-group[id*="panelSettings-width"] input[type="radio"]:before {
    content: "";
    display: block;
    width: 40px;
    height: 40px;
    border: 1px solid rgba(96,125,159,.25);
    background-position: center;
    background-size: 40px auto;
    background-repeat: no-repeat;
    filter: grayscale(1);
}

.radio-group[id*="panelSettings-width"] input[type="radio"]:checked:before {
    background-color: var(--white);
    outline: 2px solid var(--link-color);
    filter: grayscale(0);
}

.radio-group[id*="panelSettings-width"] input[type="radio"]:checked + label {
    color: black;
}

.radio-group[id*="panelSettings-width"] input:before {
    background-image: url(../icons/width_line-length.svg);
}

.radio-group[id*="panelSettings-width"] input[value="container-width"]:before {
    background-image: url(../icons/width_container.svg);
}

.radio-group[id*="panelSettings-width"] input[value="full-bleed"]:before {
    background-image: url(../icons/width_full.svg);
}
