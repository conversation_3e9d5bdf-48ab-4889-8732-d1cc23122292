/* ---- override the craftcms styles ---- */
.radio-group[id*="panelSettings-background"] input.radio {
    opacity: 1;
    position: static;
    height: auto;
    width: auto;
    cursor: pointer;
}

.radio-group[id*="panelSettings-background"] input.radio+label {
    position: static;
}

.radio-group[id*="panelSettings-background"] input.radio+label:before {
    all: initial;
}

.radio-group[id*="panelSettings-background"] input.radio+label:after {
    all: initial;
}

/* ---- Custom styles  ---- */
.radio-group[id*="panelSettings-background"] {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    gap: 0.4em;
}

.radio-group[id*="panelSettings-background"] > div {
    position: relative;
    flex: 0 0 auto;
    display: grid;
    grid-auto-flow: column;
}

.radio-group[id*="panelSettings-background"] input[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
}

.radio-group[id*="panelSettings-background"] input[type="radio"]:before {
    content: "Aa";
    font: 12px sans-serif;
    font-weight: bold;
    display: grid;
    place-items: center;
    width: 30px;
    height: 30px;
    border: 1px solid rgba(96,125,159,.25);
    border-right: none;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}

.radio-group[id*="panelSettings-background"] input[value="white"]:before,
.radio-group[id*="panelSettings-background"] input[value="default"]:before {
    border-right: 1px solid rgba(96,125,159,.25);
}

.radio-group[id*="panelSettings-background"] input[type="radio"] + label,
.radio-group[id*="panelSettings-background"] input[type="radio"] + label {
    position: static;
    min-height: unset;
    padding-inline-start: 0;
    border: 1px solid rgba(96,125,159,.25);
    border-left: none;
    display: flex;
    align-items: center;
    padding-inline: 0.4em;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    line-height: normal;
}

.radio-group[id*="panelSettings-background"] input[type="radio"]:checked + label,
.radio-group[id*="panelSettings-background"] input[type="radio"]:checked + label {
    background-color: var(--white);
}

.radio-group[id*="panelSettings-background"] input[type="radio"]:checked:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow:
        0 0 0 1px var(--white),
        0 0 0 3px var(--link-color);
    border-radius: 3px;
}

input[value="default"]:before {
    background-color: #fff;
    color: var(--text-color);
}

input[value="default-tint-01"]:before {
    background-color: #f0f0f0;
    color: var(--text-color);
}

input[value="primary"]:before {
    background-color: #2a333e;
    color: var(--white);
}

input[value="secondary"]:before {
    background-color: #f77450;
    color: var(--white);
}

input[value="alternative"]:before {
    background-color: #EE4266;
    color: var(--white);
}

input[value="accent-01"]:before {
    background-color: #35ab75;
    color: var(--white);
}

input[value="accent-02"]:before {
    background-color: #FFD23F;
    color: var(--black);
}


