{% import "_includes/forms" as forms %}

{% set unique = random() %}

<div data-id="panel-settings" data-unique-id="{{ unique }}">

    <div data-id="panel-settings__default-options" class="flex-fields">

        <div class="field width-75">
            {# Layout #}
            {{ forms.radioGroupField({
                label: layoutField.label,
                id: name ~ '-layout',
                'name': name ~ '[layout]',
                options: layoutField.options,
                value: panelSettings.layout,
            }) }}
        </div>

        <div class="field width-25 flex">
            <button
                data-id="panel-content-advanced-options__toggle-button"
                type="button"
                class="btn"
                title="Advanced Content Panel Options"
                role="button"
                aria-controls="panel-content-advanced-options"
                aria-label="Advanced Content Panel Options"
                aria-expanded="true"
                aria-pressed="true"
            >
                <span class="panel-content-advanced-options__toggle-button__label">
                    Show advanced
                </span>
            </button>
        </div>

    </div>

    <div data-id="panel-content-advanced-options" class="flex-fields" aria-expanded="true">
        {# Background Colour #}
        <div class="field">
            {{ forms.radioGroupField({
                label: backgroundField.label,
                id: name ~ '-background',
                'name': name ~ '[background]',
                options: backgroundField.options,
                value: panelSettings.background,
            }) }}
        </div>

        {# Mobile Order #}
        <div class="field">
            {{ forms.radioGroupField({
                label: mobileOrderField.label,
                id: name ~ '-mobile-order',
                'name': name ~ '[mobileOrder]',
                options: mobileOrderField.options,
                value: panelSettings.mobileOrder,
            }) }}
        </div>

        {# Vertical Alignment #}
        <div class="field">
            {{ forms.radioGroupField({
                label: alignmentField.label,
                id: name ~ '-alignment',
                'name': name ~ '[alignment]',
                options: alignmentField.options,
                value: panelSettings.alignment,
            }) }}
        </div>

        {# Panel Width #}
        <div class="field">
            {{ forms.radioGroupField({
                label: panelWidthField.label,
                id: name ~ '-width',
                'name': name ~ '[width]',
                options: panelWidthField.options,
                value: panelSettings.width,
            }) }}
        </div>

        {# Column Gap #}
        <div class="field">
            {{ forms.lightswitchField({
                label: gapField.label,
                id: name ~ '-gap',
                'name': name ~ '[gap]',
                offLabel: gapField.offLabel,
                onLabel: gapField.onLabel,
                default: gapField.default,
                on: panelSettings.gap,
            }) }}
        </div>
    </div>

</div>
