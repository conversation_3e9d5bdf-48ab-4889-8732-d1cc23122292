{% import "_includes/forms" as forms %}

<div class="panel-content-field-settings">

    <p>Specify the content blocks' default advanced settings.</p>

    {% import "_includes/forms" as forms %}

    <div class="flex-fields">
        {# Background Colour #}
        <div class="field">
            {{ forms.radioGroupField({
                label: backgroundField.label,
                id: handle ~ '-background',
                'name': name ~ '[background]',
                options: backgroundField.options,
                value: defaults.background,
            }) }}
        </div>

        {# Mobile Order #}
        <div class="field">
            {{ forms.radioGroupField({
                label: mobileOrderField.label,
                id: handle ~ '-mobile-order',
                'name': name ~ '[mobileOrder]',
                options: mobileOrderField.options,
                value: defaults.mobileOrder,
            }) }}
        </div>

        {# Vertical Alignment #}
        <div class="field">
            {{ forms.radioGroupField({
                label: alignmentField.label,
                id: handle ~ '-alignment',
                'name': name ~ '[alignment]',
                options: alignmentField.options,
                value: defaults.alignment,
            }) }}
        </div>

        {# Column Gap #}
        <div class="field">
            {{ forms.radioGroupField({
                label: panelWidthField.label,
                id: handle ~ '-width',
                'name': name ~ '[width]',
                options: panelWidthField.options,
                value: defaults.width,
            }) }}
        </div>

         {# Column Gap #}
         <div class="field">
            {{ forms.lightswitchField({
                label: gapField.label,
                id: handle ~ '-gap',
                'name': name ~ '[gap]',
                offLabel: gapField.offLabel,
                onLabel: gapField.onLabel,
                default: gapField.default,
                on: defaults.gap,
            }) }}
        </div>
    </div>
</div>
