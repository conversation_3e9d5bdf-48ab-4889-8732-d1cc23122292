{% import "_includes/forms" as forms %}

<div data-id="PanelImagePreferencesField" class="flex-fields">
    {# Image Ratio #}
    <div class="field">
        {{ forms.selectField({
            'label': ratioField.label,
            id: name ~ '-ratio',
            'name': name ~ '[ratio]',
            options: ratioField.options,
            value: imageDefaults.ratio,
        }) }}
    </div>

    {# Image display slider #}
    <div class="field image-preferences__field--plural">
        {{ forms.lightswitchField({
            label: displaySliderField.label,
            id: name ~ '-display-slider',
            'name': name ~ '[displaySlider]',
            offLabel: displaySliderField.offLabel,
            onLabel: displaySliderField.onLabel,
            default: displaySliderField.default,
            on: imageDefaults.displaySlider,
        }) }}
    </div>

    {# Image columns #}
    <div class="field image-preferences__field--plural">
        {{ forms.textField({
            type: 'number',
            label: columnsField.label,
            'name': name ~ '[columns]',
            value: imageDefaults.columns,
            min: columnsField.min,
            max: columnsField.max,
            decimals: columnsField.decimals,
        }) }}
    </div>

    {# Image captions #}
    <div class="field image-preferences__field--plural">
        {{ forms.lightswitchField({
            label: showCaptionsField.label,
            id: name ~ '-show-captions',
            'name': name ~ '[showCaptions]',
            offLabel: showCaptionsField.offLabel,
            onLabel: showCaptionsField.onLabel,
            default: showCaptionsField.default,
            on: imageDefaults.showCaptions,
        }) }}
    </div>

    {# Image enable popup #}
    <div class="field image-preferences__field--plural">
        {{ forms.lightswitchField({
            label: enablePopupField.label,
            id: name ~ '-enable-popup',
            'name': name ~ '[enablePopup]',
            offLabel: enablePopupField.offLabel,
            onLabel: enablePopupField.onLabel,
            default: enablePopupField.default,
            on: imageDefaults.enablePopup,
        }) }}
    </div>
</div>
