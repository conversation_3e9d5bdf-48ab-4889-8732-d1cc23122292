/* ------------------------------------------------------
**** Print styles
------------------------------------------------------ */

@media print {
  * {
      background: transparent !important;
      color: #000 !important; /* Black prints faster: h5bp.com/s */
      -webkit-box-shadow: none !important;
              box-shadow: none !important;
      text-shadow: none !important;
  }

  a,
  a:visited {
      text-decoration: underline;
  }

  a[href]:after {
      content: " (" attr(href) ")";
  }

  abbr[title]:after {
      content: " (" attr(title) ")";
  }

  /* Don't show links for images, or javascript/internal links */

  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
      content: "";
  }

  pre,
  blockquote {
      border: 1px solid #999;
      break-inside: avoid-page;
  }

  thead {
      display: table-header-group; /* h5bp.com/t */
  }

  tr,
  img {
      break-inside: avoid-page;
  }

  img {
      max-width: 100% !important;
  }

  @page {
      margin: 0.5cm;
  }

  p,
  h2,
  h3 {
      orphans: 3;
      widows: 3;
  }

  h2,
  h3 {
      break-after: avoid-page;
  }
}

/*
 * Just a fun note about safari print styles
 * if you want safari to honour the break styles rules
 * you may need to explicitly set the height of all printed elements to auto
 */
