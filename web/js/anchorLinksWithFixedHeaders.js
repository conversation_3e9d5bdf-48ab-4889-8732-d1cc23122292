/*
 * So you have a fixed header
 * and you want to jump to content on a page
 * but keep loosing the content underneath said header
 * */

window.addEventListener("load", function () {

    /*
    * Safari doesn't natively support smooth scrolling
    * Check if the body honours a smooth scroll
    * Use this to work out if it can scroll natively or needs a fallback
    * */
    const scrollSmoothEnabled = getComputedStyle(document.body).scrollBehavior === 'smooth';
    const mobileBreakpoint = 992;

    /* --------------------------------------
    Fallback manual js scrolling
    -------------------------------------- */
    // Part I: Where from and where to and for how long
    function smoothVerticalScrolling(e, time, where) {
        var eTop = e.getBoundingClientRect().top;
        var eAmt = eTop / 100;
        var curTime = 0;
        while (curTime <= time) {
            window.setTimeout(SVS_B, curTime, eAmt, where);
            curTime += time / 100;
        }
    }

    // Part II: Make it so
    function SVS_B(eAmt, where) {
        if(where == "center" || where == "")
            window.scrollBy(0, eAmt / 2);
        if (where == "top")
            window.scrollBy(0, eAmt);
    }

    /* --------------------------------------
    Account for the floating header when jumping to anchor links
    ---------------------------------------*/
    function scrollAccountingForHeader($targetElement) {
        let targetElementOffset = $targetElement.getBoundingClientRect().top;
        let endScrollGoal = document.documentElement.scrollTop + targetElementOffset - (window.innerWidth >= mobileBreakpoint ? 194 : 60);

        if (scrollSmoothEnabled) {
            document.documentElement.scrollTop = endScrollGoal;
        } else {
            smoothVerticalScrolling($targetElement, 500, "top");
        }

        if(document.documentElement.scrollTop != endScrollGoal) {
            setTimeout(function() {
                /* run it a bit later also for browser compatibility */
                if (scrollSmoothEnabled) {
                    document.documentElement.scrollTop = endScrollGoal;
                } else {
                    smoothVerticalScrolling($targetElement, 500, "top");
                }
            }, 50);
        }
    }

    /* --------------------------------------
    New page and jump to a anchor target
    ---------------------------------------*/
    if (window.location.hash) {
        let $targetElement = document.querySelector(window.location.hash);
        scrollAccountingForHeader($targetElement);
    }

    /* ---------------------------------------------
    Same page and jump to a anchor target
    --------------------------------------------- */
    /*
    * just a wee note:
    * the selector below relies on a particular link format
    * example: href="#element"
    * the href _cannot_ explicitly set the base url
    * */
    document.querySelectorAll('[href^="#"], [href*="' + window.location.href + '#"]').forEach(anchorLink => {
        anchorLink.addEventListener("click", hijackAnchorLink, false);
    });

    function hijackAnchorLink(e) {
        link = e.currentTarget;

        /*
        * if the link doesn't have a #hash,
        * it probably ain't the droid you're looking for
        * */
        if (!link.hash) {
            // But if it's still actually a link - don't go looking elsewhere.
            if (link instanceof HTMLAnchorElement) {
                return;
            }

            link = e.target.closest('a');
        }

        /*
        * prevent the link from just doing it's thang
        * because it's doing it _wrong_
        * */
        if (e instanceof Event) {
            e.preventDefault();
            e.stopPropagation();
        }

        // set the hash so any :target related styles are honoured
        window.location.hash = link.hash;

        let $targetElement = document.querySelector(link.hash);

        scrollAccountingForHeader($targetElement);
    }


});
