/* Post archive list */
$(document).ready(function() {
    $('.post-sidebar-archive-list__item button').on('click', function() {
        $(this).parent().toggleClass('post-sidebar-archive-list__item--expanded')
        // $(this).next('.sidebar-linklist__item__sublist').slideToggle()
    });
});


/* Post Hub */
$(document).ready(function() {
    var loadState = 'ready';

    /**
    * Method to load post's image
    *
    * @param {jquery-object} $selector
    * @param {bool} animate Animate on true
    * @return {void}
    */
    function loadPosts($selector, animate) {
        animate = animate || false;

        var numberOfPosts = $selector.length;
        var postsLoaded = 0;

        loadState = 'loading';

        $selector.each(function() {
            var $this = $(this);

            $this.removeClass("post-grid__item--hidden");

            if (animate) {
                $this.addClass("post-grid__item--animate");
            }

            postsLoaded++;
            if (postsLoaded == numberOfPosts) {
                // Reset load state
                loadState = 'ready';
            }
        });
    }

    // First, hide other blog posts
    $(".post-grid__item:gt(8)").addClass("post-grid__item--hidden");

    // Load first blog posts
    loadPosts($(".post-grid__item:lt(9)"));

    // Show more posts on scroll
    $(window).scroll(function() {
        if ($('.js--load-on-scroll').length < 1) return;
        if ($(window).scrollTop() + $(window).height() >= $(".post-grid").offset().top + $(".post-grid").height() - 300 && loadState == 'ready') {
            // Load next 3 posts if they exist
            if ($(".post-grid__item--hidden").length) {
                loadPosts($(".post-grid__item--hidden:lt(9)"), true);
            }
        }
    });


    /**
    * Method to filter posts by given category
    *
    * @param {string} category
    * @return {void}
    */
    function filterPostsByCats(category) {
        $('.post-grid').removeClass('post-grid--unfiltered').addClass('post-grid--filtered');
        $('.post-grid__item').removeClass('post-grid__item--filtered-visible post-grid__item--filtered-pre-visible').addClass('post-grid__item--filtered-hidden');

        $('.post-grid__item').each(function() {
            var categories = $(this).attr('data-categories');

            if (categories.search(category) >= 0) {
                $(this).removeClass('post-grid__item--filtered-hidden').addClass('post-grid__item--filtered-pre-visible');
                loadPosts($(this));
            }
        });

        window.location.hash = category;

        setTimeout(function() {
            $(".post-grid__item--filtered-pre-visible").removeClass("post-grid__item--filtered-pre-visible").addClass("post-grid__item--filtered-visible");
        }, 1);
    }


    /**
    * Method to filter posts by given tag
    *
    * @param {string} tag
    * @return {void}
    */
    function filterPostsByTags(tag) {
        $('.post-grid').removeClass('post-grid--unfiltered').addClass('post-grid--filtered');
        $('.post-grid__item').removeClass('post-grid__item--filtered-visible post-grid__item--filtered-pre-visible').addClass('post-grid__item--filtered-hidden');

        $('.post-grid__item').each(function() {
            var tags = $(this).attr('data-tags');

            if (tags.search(tag) >= 0) {
                $(this).removeClass('post-grid__item--filtered-hidden').addClass('post-grid__item--filtered-pre-visible');
                loadPosts($(this));
            }
        });

        setTimeout(function() {
            $(".post-grid__item--filtered-pre-visible").removeClass("post-grid__item--filtered-pre-visible").addClass("post-grid__item--filtered-visible");
        }, 1);
    }


    /**
     * Click handler for category filter buttons
     *
     * @return {void}
     */
    $(".js-post-grid-filter-cat").click(function() {

        var filterCategory = $(this).attr("data-category");

        $(".js-post-grid-filter-cat").addClass("button-inactive");
        $(this).removeClass("button-inactive");

        if (filterCategory !== '*') {
            filterPostsByCats(filterCategory);
        } else {
            $(".post-grid").removeClass("post-grid--filtered").addClass("post-grid--unfiltered");
            $(".post-grid__item").removeClass("post-grid__item--filtered-visible post-grid__item--filtered-pre-visible post-grid__item--filtered-hidden").addClass("post-grid__item--animate-grow");
            history.pushState("", document.title, window.location.pathname + window.location.search);
        }
    });


    /**
     * Click handler for tag filter buttons
     *
     * @return {void}
     */
    $(".js-post-grid-filter-tag").click(function() {

        var filterTag = $(this).attr("data-tag");

        $(".js-post-grid-filter-tag").addClass("button-inactive");
        $(this).removeClass("button-inactive");

        if (filterTag !== '*') {
            filterPostsByTags(filterTag);
        } else {
            $(".post-grid").removeClass("post-grid--filtered").addClass("post-grid--unfiltered");
            $(".post-grid__item").removeClass("post-grid__item--filtered-visible post-grid__item--filtered-pre-visible post-grid__item--filtered-hidden").addClass("post-grid__item--animate-grow");
        }
    });


    // URL anchor support for filters
    if (window.location.hash) {
        var filter = window.location.hash.substr(1);

        if ($('.js-post-grid-filterlist').length > 0) {
            $(".js-post-grid-filter-cat").addClass("button-inactive");
            $('.js-post-grid-filter-cat[data-category="' + filter +'"]').removeClass('button-inactive');

            filterPostsByCats(filter);
        }

        if ($('.js-post-grid-tag-filterlist').length > 0) {
            $(".js-post-grid-filter-tag").addClass("button-inactive");
            $('.js-post-grid-filter-tag[data-tag="' + filter +'"]').removeClass('button-inactive');

            filterPostsByTags(filter);
        }

    } else {
        if ($('.js-post-grid-filterlist').length > 0) {
            $(".js-post-grid-filter-cat").addClass("button-inactive");
            $('.js-post-grid-filter-cat[data-category="*"]').removeClass('button-inactive');
        }

        if ($('.js-post-grid-tag-filterlist').length > 0) {
            $(".js-post-grid-filter-tag").addClass("button-inactive");
            $('.js-post-grid-filter-tag[data-tag="*"]').removeClass('button-inactive');
        }
    }


    /**
    * Change the filter buttons to a select menu on mobile
    *
    */

    // Create a select and append to #menu
    var $selectCat = $("<select class='js-post-grid-filter-cat__select post-grid-filter-cat__select'><option>-- Filter by category --</option></select>");
    $(".js-post-grid-filterlist .field-input").append($selectCat);

    var $selectTag = $("<select class='js-post-grid-filter-tag__select'><option>-- Filter by tag --</option></select>");
    $(".js-post-grid-tag-filterlist .field-input").append($selectTag);

    // Cycle over category buttons
    $(".js-post-grid-filter-cat").each(function() {
        var $anchor = $(this);
        var $option = $("<option></option>");

        // Deal with selected options depending on current page
        if ($anchor.parent().hasClass("selected")) {
            $option.prop("selected", true);
        }

        // Option's value is the href
        $option.val($anchor.attr("data-category"));
        // Option's text is the text of link
        $option.text($anchor.text());
        // Append option to select
        $selectCat.append($option);
    });

    // Cycle over tag buttons
    $(".js-post-grid-filter-tag").each(function() {
        var $anchor = $(this);
        var $option = $("<option></option>");

        // Deal with selected options depending on current page
        if ($anchor.parent().hasClass("selected")) {
            $option.prop("selected", true);
        }

        // Option's value is the href
        $option.val($anchor.attr("data-tag"));
        // Option's text is the text of link
        $option.text($anchor.text());
        // Append option to select
        $selectTag.append($option);
    });

    // Bind change listener to the category select
    $selectCat.change(function() {
        if ($selectCat.val() != '*' && $selectCat.val() != '-- Filter by category --') {
            filterPostsByCats($selectCat.val());
        } else {
            $(".post-grid").removeClass("post-grid--filtered").addClass("post-grid--unfiltered");
            $(".post-grid__item").removeClass("post-grid__item--filtered-visible post-grid__item--filtered-pre-visible post-grid__item--filtered-hidden").addClass("post-grid__item--animate-grow");
        }
    });

    // Bind change listener to the tag select
    $selectTag.change(function() {
        if ($selectTag.val() != '*' && $selectTag.val() != '-- Filter by tag --') {
            filterPostsByTags($selectTag.val());
        } else {
            $(".post-grid").removeClass("post-grid--filtered").addClass("post-grid--unfiltered");
            $(".post-grid__item").removeClass("post-grid__item--filtered-visible post-grid__item--filtered-pre-visible post-grid__item--filtered-hidden").addClass("post-grid__item--animate-grow");
        }
    });
});
