'use strict';

/*
* Require the path module
*/
const path = require('path');
const craft = require('@karmabunny/craft-twig');

/*
 * Require the Fractal module
 */
const fractal = module.exports = require('@frctl/fractal').create();

/*
 * Give your project a title.
 */
fractal.set('project.title', 'Craft de Karmabunny');

craft.setAlias('@webroot', path.join(__dirname, 'web'));
craft.setAlias('@root', __dirname);

/*
 * Require the Twig adapter
 */
const twigAdapter = require('@frctl/twig')({
    functions: craft.functions,
    filters: craft.filters,
});

fractal.docs.engine(twigAdapter);
fractal.components.engine(twigAdapter);
fractal.components.set('ext', '.twig');

/*
 * Tell Fractal where to look for components.
 */
fractal.components.set('path', path.join(__dirname, 'fractal/_patterns'));

/*
 * Tell Fractal where to look for documentation pages.
 */
fractal.docs.set('path', path.join(__dirname, 'fractal/_docs'));

/*
 * Tell the Fractal web preview plugin where to look for static assets.
 */
fractal.web.set('static.path', path.join(__dirname, 'web'));


const mandelbrot = require('@frctl/mandelbrot'); // require the Mandelbrot theme module

// create a new instance with custom config options
const myCustomisedTheme = mandelbrot({
    skin: "navy",
    "nav": ["search", "docs", "components"], // show docs above components in the sidebar
    "styles": [
        "default", // This is the default Mandelbrot CSS, not a ref to the default skin folder
        "/fractal/_patterns/styleguide.css"
    ]
    // any other theme configuration values here
});

myCustomisedTheme.addStatic(path.resolve(__dirname, 'fractal'), '/fractal');

fractal.web.theme(myCustomisedTheme); // tell Fractal to use the configured theme by default

/*
* Define collation defaults
*/
fractal.components.set('default.collated', true);
fractal.components.set('default.collator', function (markup, item) {
  return `<!-- Start: @${item.handle} -->\n<dt>${item.label || item.name}</dt><dd>${markup}</dd>\n<!-- End: @${item.handle} -->\n`;
});


// Define custom statuses
fractal.components.set('statuses', {
  reference: {
      label: "Reference",
      description: "Not ready for including, but viewable for reference",
      color: '#84008C'
  },
  wip: {
    label: "W.I.P.",
    description: "Work in progress. Implement with caution.",
    color: "#FF9233"
  },
  ready: {
    label: "Ready",
    description: "Ready to implement.",
    color: "#29CC29"
  },
});
