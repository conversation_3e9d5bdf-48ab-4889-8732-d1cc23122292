{"$schema": "https://getcomposer.org/schema.json", "require": {"craftcms/ckeditor": "^4.4.0", "craftcms/cms": "^5.5.7", "karmabunny/craft-errorlog": "^2.0", "karmabunny/craft-fractal": "^4.0", "karmabunny/craft-kbdev": "^4.0", "karmabunny/craft-translate": "^3.0", "nystudio107/craft-retour": "^5.0.4", "nystudio107/craft-seomatic": "^5.1.7", "nystudio107/craft-vite": "^5.0", "verbb/cloner": "^3.0", "verbb/formie": "^3.0.15", "vlucas/phpdotenv": "^3.4.0", "wrav/oembed": "^3.1"}, "autoload": {"psr-4": {"modules\\": "modules/", "modules\\contentpanel\\": "modules/contentpanel/src"}}, "config": {"sort-packages": true, "optimize-autoloader": true, "preferred-install": {"karmabunny/*": "dist"}, "platform": {"php": "8.2"}, "allow-plugins": {"yiisoft/yii2-composer": true, "craftcms/plugin-installer": true, "vaimo/composer-patches": true}}, "scripts": {"bsts": "bash docs/pull_bsts.sh"}, "repositories": [{"type": "composer", "url": "https://crftkb:<EMAIL>"}, {"type": "composer", "url": "https://composer.craftcms.com", "canonical": false}], "minimum-stability": "beta", "prefer-stable": true}