# Ongoing improvements to base install

- Background colour options config
    Create a background colour option config const
    Populate all the background colour fields (panel matrix, vertical gap, etcs..) using that object
    this will save on double handling for every site set up.

- Come up with a base 'block-list' style for use in the search results and maybe footers

- Set up the 404 page as a single so clients can use addons eg. brochure

- Overhaul `web/js/anchorLinksWithFixedHeaders.js`and `function scrollToElement`. First, there's a lot of duplicate functionality between the dedicated file and the scrollToElement function, further more the `html` element now has a `scroll-padding-top` that's the header height.
__Other notes__: Safari was is the only browse that doesn't natively support smooth scrolling `scroll-behavior: smooth;`.


- Discussions about if a highlight is actually just a box or alternatively should be named "highlight box" as "highlight" more inately refers to the inline styling of individual words.

- Roll all the components that use "content-block" into a macro so that you can make updates to one and all (multi-components was such a hinderance on SOE). Further more, consider a universal content-block settings object to normalise which values there could be. 

- rename promo cards set to "card set"



