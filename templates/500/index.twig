{% extends "01_core/main" %}

{% import "01_core/_macros/macros-navigation" as navMacros %}

{% set bodyAttributes = { class: '500-page' } %}


{% block header %}{% endblock %}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
{% endblock %}

{% block main_content %}
    <main class="page-layout page-layout--center section bg-default">
        <h2>Internal Server Error</h2>
        <p>{{ (message ?? null) ?: "An error occurred while processing your request." }}</p>

        {% set errorlog = craft.app.plugins.getPlugin('errorlog') %}
        {% if errorlog %}
            <strong>{{ errorlog.reference }}</strong>
        {% endif %}
    </main>
{% endblock %}


{% block footer %}{% endblock %}

