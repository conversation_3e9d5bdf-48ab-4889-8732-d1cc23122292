{% extends '01_core/main' %}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
    {{ parent() }}
{% endblock %}

{% block main_content %}

    {% embed "01_core/_layouts/page-layout.twig" with { layout: 'page-layout--center' } %}

        {% block main_bar %}

            <div class="section-small">

                <h2>What are you looking for?</h2>

                <form action="{{ url('search') }}">

                    <div class="form-row capsule-form-fields">
                        <div class="form-column field--text">
                            <label for="q" class="-vis-hidden field-label text">Search {{ siteName }}</label>
                            <input type="search" class="field--icon form-input" name="q" placeholder="Search...">
                        </div>

                        <div class="form-column">
                            <input type="submit" value="Search" class="button button--block">
                        </div>
                    </div>

                </form>

            </div>

            {% set searchQuery = craft.app.request.getParam('q') %}

            {% if searchQuery == null or searchQuery == '' %}

                <div class="highlight">
                    <p>Please make a search so we can help you find what you're looking for.</p>
                </div>

            {% else %}

                {% set results = craft.entries.search(searchQuery).navigationVisibility('searchResults').orderBy('score').all() %}

                <header class="search-site__results-header">
                    <h2>Search Results</h2>
                    <p class="search-site__count">
                        <span class="search-site__count__quantity">{{ results|length }}</span>
                        <span class="search-site__count__label">result{% if results|length != 1 %}s{% endif %}</span>
                    </p>
                </header>


                <div class="highlight">
                    {% if results|length %}
                        <ul class="simple-list">
                            {% for item in results %}
                                <li class="simple-list__item">

                                    {% include "./search/_search-result.twig" %}

                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>Your search for “{{ searchQuery }}” didn't return any results.</p>
                    {% endif %}
                </div>

            {% endif %}

        {% endblock %}

    {% endembed %}

{% endblock %}
