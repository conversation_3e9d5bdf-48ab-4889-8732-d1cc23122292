<!-- Template: {{ _self }} -->

<a class="search-result" href="{{ item.url }}">
    <h2 class="search-result__title">{{ item.itemTitle ?? item.title }}</h2>

{% if item.subtitle ?? false %}
    <p class="search-result__role">{{ item.subtitle }}</p>
{% else %}
    <p class="search-result__breadcrumb">
    Home / {{ item.uri|replace({ '-':' ', '/':' / '})|title }}
    </p>
{% endif %}

    <p class="search-result__desc">
    {% if item.excerpt ?? false %}
        {{ item.excerpt }}
    {% elseif item.metaDescription ?? false %}
        {{ item.metaDescription }}
    {% elseif item.pageContent and item.pageContent|length > 0 %}
        {% set shortDescriptFallback = item.pageContent.one() %}
        {{ shortDescriptFallback.copy|striptags|truncate(180) }}
    {% elseif item.richtextSansMedia ?? false %}
        {{ item.richtextSansMedia|striptags|truncate(180) }}
    {% endif %}
    </p>

</a>
