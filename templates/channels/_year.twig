{% extends '01_core/main' %}

{% import "01_core/_macros/macros-navigation" as navMacros %}

{% set sidebarEnabled = true %}
{% set year = year ?? now|date('Y') %}
{% set sectionTitle = craft.app.request.segments|first %}

{% block meta_page_title %}
    {% if seomatic is not defined or (seomatic is defined and not seomatic.meta.seoTitle) %}
    <title>
        {% if craft.app.config.general.devMode %}🚧{% endif %}
        {{ year }} {{ navMacros.slug_to_title(craft.app.request.segments|first) }} | {{ siteName }}
        </title>
    {% endif %}
{% endblock %}

{% set bodyAttributes = {
    class: ''
} %}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
    {{ parent() }}
{% endblock %}

{% block page_title %}
    {% include '@page-header' with { pageTitle : sectionTitle|title ~ ' in ' ~ year } %}
{% endblock %}

{% block main_content %}

    {% embed "01_core/_layouts/page-layout.twig" %}

        {% block main_bar %}

            {% set entriesInYear = craft.entries.section(sectionTitle).postDate(['and', '>= '~ year ~'-01-01', '< '~ year ~'-12-31']).all() %}

            {% for month, entries in entriesInYear|group("postDate|date('F')") %}

                <div class="section-small">

                    <h2>{{ month }}</h2>

                    <div class="fixed-grid fixed-grid--cols-2">

                        {% for entry in entries %}
                            {% switch entry.type %}
                                {% case 'post' %}
                                    {% include '@post-grid-item' %}

                                {% default %}
                                    {% include '@card' with {
                                        cardEntry: entry,
                                        linkLabelFallback: "View " ~ entry.type,
                                    } %}
                            {% endswitch %}

                        {% endfor %}

                    </div>

                </div>

            {% endfor %}

        {% endblock %}

        {% block side_bar %}
            {% include 'channels/_partials/archive.twig' with { archive: { section: sectionTitle } } %}
        {% endblock %}

    {% endembed %}

{% endblock %}

