<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-navigation" as navMacros %}
{% set categoryType = applicableSection ?? false ? applicableSection|trim('s') ~ 'Category' : null %}
{% set categories = craft.entries.section('categories').type(categoryType).orderBy('title ASC').all() %}

{% macro categoriesList( categories, applicableSection) %}
    <ul class="category-browser__list linklist depth1">
        {% nav category in categories %}
            {% set entriesInCategory = craft.entries.section(applicableSection).relatedTo(category) %}
            <li class="category-browser__item {{ navMacros.is_active( category.slug ) ?? null }}">
                <a class="category-browser__link" href="{{ category.url }}">
                    <span class="category-browser__item__title">{{ category.title }}</span>
                    <span class="category-browser__item__item-count">({{ entriesInCategory.count() }})</span>
                </a>
                {% ifchildren %}
                    <ul class="depth2">
                        {% children %}
                    </ul>
                {% endifchildren %}
            </li>
        {% endnav %}
    </ul>
{% endmacro %}


{% if categories ?? null %}
    <div class="category-browser sidebar-widget">

        <h3 class="category-browser__title">{{ applicableSection ? applicableSection|trim('s')|title ~ ' ' : null }}Categories</h3>

        <nav class="category-browser__nav">
            {% if applicableSection ?? false %}
                {{ _self.categoriesList( categories, applicableSection) }}
            {% else %}
                {% for type, categories in categories|group("type") %}
                    <h4 class="category-browser__list__title">{{ type|kebab|replace({ '-':' '})|trim('y')|title ~ 'ies' }}</h4>
                   {{ _self.categoriesList( categories, applicableSection) }}
                {% endfor %}
            {% endif %}
        </nav>

    </div>
{% endif %}
