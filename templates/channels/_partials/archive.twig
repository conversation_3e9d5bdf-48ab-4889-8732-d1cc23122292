<!-- Template: {{ _self }} -->

<div class="archive">

    <h3 class="archive__title">{{ archive.section|title }} Archive</h3>

    {% set allEntries = craft.entries.section(archive.section).all() %}
    {% set uriArray = craft.app.request.segments %}

    {% for year, entriesInYear in allEntries|group("postDate|date('Y')") %}
        {%- if loop.first %}
        <button aria-pressed="true" aria-label="View all {{ archive.section|title }} made in {{ year }}" class="js--slide-toggle slide-toggle__trigger">{{ year }}</button>
        <ul class="archive__list archive__list-depth0" aria-expanded="true">
        {% else %}
        <button aria-pressed="false" aria-label="View all {{ archive.section|title }} made in {{ year }}" class="js--slide-toggle slide-toggle__trigger">{{ year }}</button>
        <ul class="archive__list archive__list-depth0" aria-expanded="false">
        {%- endif %}

            <li class="archive__list__item">
                    <a class="archive__list__item__link" href="{% for i in uriArray %}{{ not loop.last ? '/' ~ i }}{% endfor %}/{{ year }}">
                    <span class="-vis-hidden">View </span>All<span class="-vis-hidden"> {{ year }} {{ archive.section|title }}</span>
                </a>
            </li>
            <li class="archive__list__item">
                {% for month, entriesInMonth in entriesInYear|group("postDate|date('F')") %}
                    <button aria-pressed="false" aria-label="View all {{ archive.section|title }} made in {{ month }} {{ year }}" class="js--slide-toggle slide-toggle__trigger">{{ month }}</button>
                    <ul class="archive__list archive__list-depth1" aria-expanded="false">
                        {% for entry in entriesInMonth %}
                            <li class="archive__list__item">
                                <a class="archive__list__item__link" href="{{ entry.url }}">
                                    {{ entry.navigationTitle ?? entry.title }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                {% endfor %}
            </li>
        </ul>
    {% endfor %}
</div>


{% js %}
/* --------------------------------------
Slide Toggles
---------------------------------------*/
const slideToggles = document.querySelectorAll('.js--slide-toggle');

function toggleSlide(e) {

    if (e instanceof Event) {
        e.preventDefault();
        e.stopPropagation();
    }

    let trigger = e.target;

    if (trigger.getAttribute('aria-pressed') == 'false') {
        trigger.setAttribute('aria-pressed', 'true');
        trigger.nextElementSibling.setAttribute('aria-expanded', true);
    } else {
        trigger.setAttribute('aria-pressed', 'false');
        trigger.nextElementSibling.setAttribute('aria-expanded', false);
    }
}

if(slideToggles.length > 0) {

    slideToggles.forEach(trigger => {
        trigger.addEventListener("click", toggleSlide, false);
    });

}
{% endjs %}
