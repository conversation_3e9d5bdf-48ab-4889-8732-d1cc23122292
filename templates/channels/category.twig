{% extends '01_core/main' %}

{% set sidebarEnabled = true %}

{% set entry = entry ?? null %}
{% set applicableSection = entry.type|split('Category')|first ~ 's' %}
{% set uncategorisedSections = ['alerts', 'footer', 'home', 'pageAddons', 'primaryNavigation', 'singularLink', 'search', 'sitemap'] %}
{% set entries = craft.entries.section(applicableSection ?? ['not']|merge(uncategorisedSections)).relatedTo(entry).all() %}

{% block js %}
{{ parent() }}
{% if applicableSection == 'posts' %}
<script src="{{ busty('/js/posts_settings.js') }}" async defer></script>
<script src="{{ busty('/js/posts.js') }}" async defer></script>
{% endif %}
{% endblock %}

{% block css %}
    {{ parent() }}
{% endblock %}

{% set bodyAttributes = {
    class: 'posts'
} %}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
    {{ parent() }}
{% endblock %}

{% block main_content %}

    {% embed "01_core/_layouts/page-layout.twig" %}

        {% block main_bar %}

            <div class="{{ applicableSection == 'posts' ? 'post-grid js--load-on-scroll' : 'tidy-list-grid' }}">

                {% for entry in entries %}
                    {% switch entry.type %}
                        {% case 'post' %}
                            {% include '@post-grid-item' %}

                        {% default %}
                        <div class="tidy-list-grid__item">
                            {% include '@card' with {
                                cardEntry: entry,
                            } %}
                        </div>
                    {% endswitch %}
                {% endfor %}

            </div>

        {% endblock %}

        {% block side_bar %}
            {% include 'channels/_partials/category-browser.twig' %}
        {% endblock %}

    {% endembed %}

{% endblock %}

