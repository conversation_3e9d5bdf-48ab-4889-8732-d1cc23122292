{% extends '01_core/main' %}

{% set sidebarEnabled = false %}

{% set applicableSection = slug ?? null %}
{% set categoryType = slug ?? false ? slug|trim('s') ~ 'Category' : null %}
{% set categories = craft.entries.section('categories').type(categoryType).all() %}

{% block js %}
{{ parent() }}
{% if applicableSection == 'posts' %}
<script src="{{ busty('/js/posts_settings.js') }}" async defer></script>
<script src="{{ busty('/js/posts.js') }}" async defer></script>
{% endif %}
{% endblock %}

{% block css %}
    {{ parent() }}
{% endblock %}

{% set bodyAttributes = {
    class: 'posts'
} %}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
    {{ parent() }}
{% endblock %}

{% block main_content %}

    {% embed "01_core/_layouts/page-layout.twig" %}

        {% block page_title %}
            {% set pageTitle = pageTitle ?? false and pageTitle|lower == 'categories' ? 'All ' ~ applicableSection|trim('s')|title ~ ' ' ~ pageTitle : pageTitle %}
            {% include '@page-header' %}
        {% endblock %}

        {% block main_bar %}

                {% for type, categoryGroup in categories|group("type") %}

                    {% if not applicableSection ?? false %}
                        <h4>{{ type|kebab|replace({ '-':' '})|trim('y')|title ~ 'ies' }}</h4>
                    {% endif %}

                    <div class="{{ applicableSection == 'posts' ? 'post-grid js--load-on-scroll' : 'tidy-list-grid' }}">

                        {% for entry in categoryGroup %}
                            {% include '@card' with {
                                cardEntry: entry,
                                cardTag: 'a',
                                cardLink: entry.url,
                            } %}

                        {% endfor %}

                    </div>

                {% endfor %}

        {% endblock %}

    {% endembed %}

{% endblock %}

