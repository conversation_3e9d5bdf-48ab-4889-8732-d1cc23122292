{# data objects #}
{%- set entry = object ?? null -%}

{%- set templateColours = {
        templateBackground: '#f1f1ed',
        headerBackground: '#2a333e',
        headerText: '#ffffff',
        bodyBackground: '#ffffff',
        bodyText: '#2b2b2b',
        subtleText: '#636363',
        border: '#d4d4d4',
        footerBackground: '#2a333e',
        footerText: '#ffffff',
        footerTextLink: '#d4d4d4',
        link: '#394E47',
        h1: '#0C0C0C',
        h2: '#2a333e',
        h3: '#35ab75',
        h4: '#636363'
    }
-%}

{%- set templateStyles = {
    fontFamily: "'Open Sans', Arial, Helvetica, sans-serif",
    fontSizeBody: "15px",
    fontSizeH1: "24px",
    fontSizeH2: "21px",
    fontSizeH3: "18px",
    fontSizeH4: "15px",
    lineHeight: "1.5",
}
-%}

{% import "01_core/_macros/macros-global.twig" as globalMacros %}

{% block head %}
    {# subject: New submission from {{ form.name }} form #}
    {# fromEmail: {{ craft.app.projectConfig.get('email.fromEmail') }} #}
    {# fromName: {{ craft.app.projectConfig.get('email.fromName')|raw }} #}
    {# replyToEmail: {{ craft.app.projectConfig.get('email.replyToEmail') }} #}
    {# replyToName: #}
    {# cc: #}
    {# bcc: #}
    {# includeAttachments: true #}
    {# presetAssets: #}
    {# description: Default template #}
{% endblock %}

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="{{ craft.app.language }}" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>{{ siteName }}</title>
	<!-- Facebook sharing information tags -->
	<meta property="og:title" content="{{ siteName }}">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!--[if !mso]><!-- -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600" rel="stylesheet">
    <!--<![endif]-->
    <!--[if mso]>
    <style type=”text/css”>
    .body-text {
    font-family: Arial, sans-serif;
    }
    </style>
    <![endif]-->

    <style type="text/css">
        body {
            width:100%;
            background-color: {{ templateColours.templateBackground }};
            margin:0;
            padding:0;
            -webkit-text-size-adjust:100%;
            -webkit-font-smoothing:antialiased;
            font-family: {{ templateStyles.fontFamily }};
            font-size: {{ templateStyles.fontSizeBody }};
            line-height: {{ templateStyles.lineHeight }};
            color: {{ templateColours.subtleText }};
        }
        h1,h2,h3,h4,h5,.h1,.h2,.h3,.h4,.h5 { margin-top:0; margin-right:0; margin-left:0; margin-bottom:10px; }
        h1,.h1 {
            color: {{ templateColours.h1 }} !important;
            font-family: 'Franklin Gothic Medium', 'Futura', Trebuchet MS,Arial,sans-serif;
            font-size: {{ templateStyles.fontSizeH1 }};
            font-weight:normal;
            mso-line-height-rule:exactly;
            line-height:34px;
        }
        h2,.h2 {
            color: {{ templateColours.h2 }} !important;
            font-family: 'Franklin Gothic Medium', 'Futura', Trebuchet MS,Arial,sans-serif;
            font-size: {{ templateStyles.fontSizeH2 }};
            font-weight:normal;
            mso-line-height-rule:exactly;
            line-height:30px;
        }
        h3,.h3 {
            color: {{ templateColours.h3 }} !important;
            font-family: 'Franklin Gothic Medium', 'Futura', Trebuchet MS,Arial,sans-serif;
            font-size: {{ templateStyles.fontSizeH3 }};
            font-weight:normal;
            mso-line-height-rule:exactly;
            line-height: 20px;
            margin-bottom:5px;
        }
        h4,.h4 {
            color: {{ templateColours.h4 }} !important;
            font-family: 'Franklin Gothic Medium', 'Futura', Trebuchet MS,Arial,sans-serif;
            font-size: {{ templateStyles.fontSizeH4 }};
            font-weight:bold;
        }
        a, a:link,a:visited {
            color: {{ templateColours.link }};
        }
        a img {
            border: 0;
        }
        /* Style iOS auto links to not look like links with a span */
        .autolink-fix a {
            color: {{ templateColours.subtleText }} !important;
            text-decoration: none !important;
        }
        .footer-link, .footer-link:link, .footer-link:visited {
            text-decoration: none;
        }
        table{
            border-collapse:collapse;
        }

        @media only screen and (max-device-width: 619px), screen and (max-width: 619px){
            .wrappper {
                padding: 10px!important;
            }
            .center-mob{
                text-align:center!important;
            }
            .right-mob{
                text-align:right!important;
            }
            .left-mob{
                text-align:left!important;
            }
            .deviceWidth,
            .deviceWidthImg img,
            .paddedDeviceWidth,
            .paddedDeviceWidthImg img {
                padding:0;
                max-width:100%!important;
            }
            .deviceWidth,
            .deviceWidthImg img{
                width:480px!important;
                max-height:none!important;
            }
            .deviceWidth.smImg,
            .deviceWidthImg.smImg img{
                width:230px!important;
                max-height:none!important;
            }
            .paddedDeviceWidth,
            .paddedDeviceWidthImg img {
                width:440px!important;
            }
            .paddedDeviceWidth.smImg,
            .paddedDeviceWidthImg.smImg img {
                width:210px!important;
            }
            .paddedDeviceWidth--gallery {
                width:260px!important;
                text-align: center!important;
            }
        }

        @media only screen and (max-device-width: 560px), screen and (max-width: 560px){
            .right-mob-sm{
                text-align:right!important;
            }
            .left-mob-sm{
                text-align:left!important;
            }
            .deviceWidth,
            .deviceWidthImg img{
                width:380px!important;
            }
            .paddedDeviceWidth,
            .paddedDeviceWidthImg img {
                width:340px!important;
            }
            .deviceWidth.smImg,
            .deviceWidthImg.smImg img{
                width:180px!important;
                max-height:none!important;
            }
            .paddedDeviceWidth.smImg,
            .paddedDeviceWidthImg.smImg img {
                width:160px!important;
            }
        }

        @media only screen and (max-device-width: 420px), screen and (max-width: 420px){
            .right-mob-xs{
                text-align:right!important;
            }
            .left-mob-xs{
                text-align:left!important;
            }
            .deviceWidth,
            .deviceWidthImg img,
            .paddedDeviceWidth,
            .paddedDeviceWidthImg img,
            .deviceWidth.smImg,
            .deviceWidthImg.smImg img,
            .paddedDeviceWidth.smImg,
            .paddedDeviceWidthImg.smImg img {
                width:100%!important;
            }
        }

    </style>
</head>
<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" style="font-family: {{ templateStyles.fontFamily }}">

	<!-- Wrapper -->
	<table width="100%" border="0" cellpadding="0" cellspacing="0" align="center" bgcolor="{{ templateColours.templateBackground }}">
		<tr>
			<td class="wrappper" valign="top" style="padding: 20px;">

                <!-- header -->
                <table width="600" class="deviceWidth" border="0" cellpadding="0" cellspacing="0" align="center">
                    <tbody>
                        <tr>
                            <td valign="top" align="center" bgcolor="{{ templateColours.headerBackground }}" style="padding: 20px 20px 20px 20px; vertical-align: center;">

                                <img src="{{ craft.app.sites.currentSite.baseUrl ?? '' }}assets/logo-email.png" width="170" height="124" style="width: 170px;height: 124px;" alt="{{ siteName }} Logo">

                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- /header -->

                 <!-- Article -->
                 <table width="600" class="deviceWidth" border="0" cellpadding="0" cellspacing="0" align="center" bgcolor="{{ templateColours.templateBackground }}">
                    <tr>
                        <td style="font-size: {{ templateStyles.fontSizeBody }}; color: {{ templateColours.bodyText }}; font-weight: normal; text-align: left; font-family: {{ templateStyles.fontFamily }}; mso-line-height-rule:exactly; line-height: 20px; vertical-align: top;">

                            {% if craft.app.sites.currentSite.baseUrl matches '{(bunnysites)}' %}
                                <table cellpadding="20" cellspacing="0">
                                    <tr>
                                        <td style="border-top:none;border-bottom:1px solid {{ templateColours.border }};background-color:{{ templateColours.bodyBackground }};width:600px;">
                                            <p style="text-transform: uppercase;letter-spacing: 2px;margin: 0;font-size:{{ templateStyles.fontSizeH3 }};font-weight:bold;text-align: center;">This is a test email</p>
                                        </td>
                                    </tr>
                                </table>
                            {% endif %}

                            {% if body ?? false or contentHtml ?? false  %}
                                <table cellpadding="20" cellspacing="0">
                                    <tr>
                                        <td style="border-top:none;border-bottom:1px solid {{ templateColours.border }};background-color:{{ templateColours.bodyBackground }};width:600px; padding-left: 20px; padding-right: 20px; padding-top: 20px; padding-bottom:20px;font-family: {{ templateStyles.fontFamily }};">
                                            {% block body %}{% endblock %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-top:none; width:600px;padding-bottom: 15px;"></td>
                                    </tr>
                                </table>
                            {% endif %}
                        </td>
                    </tr>
                </table>
                <!-- End Article -->

                <!-- Footer -->
                <table width="600" class="deviceWidth" border="0" cellpadding="0" cellspacing="0" align="center">
                    <tbody>
                        <tr>
                            <td valign="top" style="font-size: 14px; color: {{ templateColours.footerText }}; font-weight: normal; text-align: left; font-family: {{ templateStyles.fontFamily }};; mso-line-height-rule:exactly; line-height: 21px; vertical-align: top; padding-left: 20px; padding-right: 20px; padding-top: 10px; padding-bottom: 10px;" class="center-mob" bgcolor="{{ templateColours.footerBackground }}">
                                <p style="mso-table-lspace:0;mso-table-rspace:0;">  <!--  funky code fix for layout in outlook  -->
                                    <strong style="font-weight: bold;color: {{ templateColours.footerText }};">{{ siteIdentity.companyName ?? siteName }}</strong>
                                    <br>
                                    <a class="footer-link" href="{{ craft.app.sites.currentSite.baseUrl }}" style="color: {{ templateColours.footerTextLink }} !important">
                                        {{ craft.app.sites.currentSite.baseUrl|replace('/https?:/', '')|trim('/') }}
                                    </a>
                                    <br>
                                    {% if siteIdentity.address ?? false %}
                                        <br>{{ siteIdentity.address }}<br>
                                    {% endif %}
                                    {% if siteIdentity.emailAddress ?? false %}
                                        <a class="footer-link" href="mailto:{{ siteIdentity.emailAddress }}" style="color: {{ templateColours.footerTextLink }} !important">
                                            {{ siteIdentity.emailAddress }}<br>
                                        </a>
                                    {% endif %}
                                    {% if siteIdentity.phoneNumber ?? false %}
                                        <a class="footer-link" href="{{ siteIdentity.phoneNumber }}" style="color: {{ templateColours.footerTextLink }} !important">
                                            {{ globalMacros.phoneFormat(siteIdentity.phoneNumber) }}
                                        </a><br>
                                    {% endif %}
                                </p>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- End Footer -->

            </td>
        </tr>
    </table>
    <!-- End Wrapper -->

</body>
</html>
