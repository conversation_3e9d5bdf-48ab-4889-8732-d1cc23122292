{% if entry.redirect ?? false and entry.redirect.count() > 0 %}
    {% set redirectBlock = entry.redirect.one() %}
    {% set redirectTarget = null %}

    {% switch redirectBlock.type %}
        {% case 'pageRedirect' %}
            {% set redirectTarget = redirectBlock.targetEntry.one().uri %}
        {% case 'slugRedirect' %}
            {% set redirectTarget = redirectBlock.targetSlug %}
        {% case 'externalRedirect' %}
            {% set redirectTarget = redirectBlock.targetUrl %}
    {% endswitch %}

    {% redirect redirectTarget %}
{% endif %}
