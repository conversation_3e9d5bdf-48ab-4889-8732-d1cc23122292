<!-- Template: {{ _self }} -->

{% if entry ?? false %}

    {# set facebook image #}
    {% if (seomatic is not defined or (seomatic is defined and not seomatic.meta.ogImage)) %}
        {% if entry.banner is defined and entry.banner and entry.banner.one() %}
            {% set facebookImage = entry.banner.one().getUrl('facebookPost') %}
        {% elseif siteIdentity.defaultSocialMediaImage.one() ?? false %}
            {% set facebookImage = siteIdentity.defaultSocialMediaImage.one().getUrl('facebookPost') %}
        {% endif %}
    {% endif %}
    {# set twitter image #}
    {% if (seomatic is not defined or (seomatic is defined and not seomatic.meta.twitterImage)) %}
        {% if entry.banner is defined and entry.banner and entry.banner.one() %}
            {% set twitterImage = entry.banner.one().getUrl('facebookPost') %}
        {% elseif siteIdentity.defaultSocialMediaImage.one() ?? false %}
            {% set twitterImage = siteIdentity.defaultSocialMediaImage.one().getUrl('facebookPost') %}
        {% endif %}
    {% endif %}

    {%- if (seomatic is not defined or (seomatic is defined and not seomatic.meta.seoDescription)) and entry.metaDescription ?? false -%}
    <meta name="description" content="{{ entry.metaDescription }}" >
    {%- endif -%}
    {%- if (seomatic is not defined or (seomatic is defined and not seomatic.meta.seoKeywords)) and entry.metaKeywords ?? false -%}
    <meta name="keywords" content="{{ entry.metaKeywords }}" >
    {%- endif -%}

    {# article specific #}
    <meta property="og:type" content="article" >

    {# pintrest specific #}
    {% if entry.postDate is defined and entry.postDate %}
    <meta property="article:published_time" content="{{ entry.postDate|date('Y-m-d\\TH:i:sO') }}" >
    {% endif %}
    {% if entry.author is defined and entry.postDate %}
    <meta property="article:author" content="{{ entry.author and entry.author != 'admin' ? entry.author : siteName }}" >
    {% endif %}

    {# facebook specific #}
    <meta property="og:url" content="{{ entry.url }}">
    {%- if (seomatic is not defined or (seomatic is defined and not seomatic.meta.seoDescription)) and entry.metaDescription ?? false -%}
    <meta property="og:description" content="{{ entry.metaDescription }}">
    {%- endif -%}
    <meta property="og:site_name" content="{{ siteName }}" >
    {%- if (seomatic is not defined or (seomatic is defined and not seomatic.meta.seoTitle)) -%}
    <meta property="og:title" content="{{ entry.metaTitle ?? entry.title }}">
    {% endif %}
    {% if facebookImage ?? false %}
    <meta property="og:image" content="{{ facebookImage }}">
    {% endif %}

    {# twitter specific #}
    {%~ if entry.banner and entry.banner.one() ~%}
    <meta name="twitter:card" content="summary_large_image">
    {%- else -%}
    <meta name="twitter:card" content="summary">
    {%~ endif ~%}
    {%- if siteIdentity.twitter -%}
    <meta name="twitter:site" content="@{{ siteIdentity.twitter|replace('https://twitter.com/', '') }}">
    {%~ endif ~%}
    {%- if siteIdentity.twitterHandle -%}
    <meta name="twitter:creator" content="{{ siteIdentity.twitterHandle }}">
    {%~ endif ~%}
    {%- if (seomatic is not defined or (seomatic is defined and not seomatic.meta.seoDescription)) and entry.metaDescription ?? false -%}
    <meta name="twitter:description" content="{{ entry.metaDescription }}">
    {%- endif -%}
    {%- if (seomatic is not defined or (seomatic is defined and not seomatic.meta.seoTitle)) -%}
    <meta name="twitter:title" content="{{ entry.metaTitle ?? entry.title }}">
    {% endif %}
    {% if twitterImage ?? false %}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:image" content="{{ twitterImage }}">
    {% endif %}


{% endif %}
