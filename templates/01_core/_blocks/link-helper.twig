<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-global.twig" as macros %}

{% set linkHelperEntries = linkHelperEntries ?? entry.linkHelper.all() ?? null %}

{% for block in linkHelperEntries %}

    {% set blockLink = block.linkField ?? null %}
    {% set blockIcon = block.iconAsset.one() ?? null %}
    {% set blockLinkStyle = block.linkStyle.value ?? null %}

    {% if blockLink ?? false %}

        {% if itemParentElement is defined %}
            <{{ itemParentElement }}>
        {% endif %}

        {% include '01_core/_blocks/individual-link' with {
            link: blockLink,
            linkWrapBlockElem: linkWrapBlockElem ?? null,
            icon: staticIcon is defined ? staticIcon : blockIcon,
            linkIndex: loop.index0,
            'individualLink': {
                modifierClass: blockLinkStyle|length ? blockLinkStyle : linkHelper.modifierClass ?? null,
            }
        } %}

        {% if itemParentElement is defined %}
            </{{ itemParentElement }}>
        {% endif %}

    {% endif %}

{% endfor %}

