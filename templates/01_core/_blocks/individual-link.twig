<!-- Template: {{ _self }} -->

{% set linkClass = individualLink.modifierClass ?? null %}
{% if linkClass == 'button' %}
    {% set linkIndex = linkIndex ?? 0 %}
    {% if linkIndex == 0 %}
        {% set linkClass = 'button button--subtle' %}
    {% else %}
        {% set linkClass = 'text-link--footer' %}
    {% endif %}
{% endif %}

<a
    href="{{ link.value }}"
    class="{{ linkClass }}{{ link.value matches '/^#.*/' ? ' js--scroll-to' }}"
    target="{{ link.target }}"
    rel="{{ link.target == '_blank' ? 'noopener noreferrer' : null }}"
    {{ link.value matches '/^#.*/' ? ' data-target="' ~ link ~ '"' : null }}
>

    {%- if link.label and (linkWrapBlockElem is not defined or linkWrapBlockElem == false) -%}
        {{- link.label -}}
        {%- if icon ?? false %} {{ svg(icon)|attr({class: 'cta__icon'}) }}{% endif -%}
    {%- endif -%}

{% if linkWrapBlockElem is not defined or linkWrapBlockElem == false %}
        </a>
{% endif %}
