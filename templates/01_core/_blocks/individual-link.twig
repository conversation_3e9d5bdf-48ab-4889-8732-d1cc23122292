<!-- Template: {{ _self }} -->

{% set linkClass = individualLink.modifierClass ?? null %}
{% if linkClass == 'button' %}
    {% if link.label|lower == 'learn more' %}
        {% set linkClass = 'button button--subtle' %}
    {% elseif link.label|lower == 'sign up' %}
        {% set linkClass = 'text-link' %}
    {% else %}
        {% set linkClass = 'button' %}
    {% endif %}
{% endif %}

<a
    href="{{ link.value }}"
    class="{{ linkClass }}{{ link.value matches '/^#.*/' ? ' js--scroll-to' }}"
    target="{{ link.target }}"
    rel="{{ link.target == '_blank' ? 'noopener noreferrer' : null }}"
    {{ link.value matches '/^#.*/' ? ' data-target="' ~ link ~ '"' : null }}
>

    {%- if link.label and (linkWrapBlockElem is not defined or linkWrapBlockElem == false) -%}
        {{- link.label -}}
        {%- if icon ?? false %} {{ svg(icon)|attr({class: 'cta__icon'}) }}{% endif -%}
    {%- endif -%}

{% if linkWrapBlockElem is not defined or linkWrapBlockElem == false %}
        </a>
{% endif %}
