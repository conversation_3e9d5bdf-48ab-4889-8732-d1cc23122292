<!-- Template: {{ _self }} -->

<header id="js-header">

    <div id="js-header-inner" class="header">

        <div class="header__container container">

            <a class="header__logo" href="/" onclick="ga('send', 'event', 'Skin', 'LogoClick');" title="{{ siteName }}">
                {{ svg('@webroot/assets/logo.svg')|attr({
                    class: 'logo__img'
                }) }}
            </a>

            <div class="header__nav">

                {% set bookButton = craft.entries.section('primaryNavigation').type('bookButton').one() ?? null %}
                {#  Hampshire was built to allow user to set a booking url and link directly to a third-party OR have a search bar to link to a third-party.
                    If the site had a search bar, by default it would be hidden (but on the page)
                    However, on the home page it would be visible on page load.
                    To handle all these different behaviours we have the below logic. #}
                {% set searchBarVisibleOnPageLoad = false %}
                {% if bookButton ?? false %}
                    {% tag bookButton.linkField ?? false or searchBarVisibleOnPageLoad ? 'a' : 'button' with {
                        class: [
                            'book-button',
                            'button',
                            bookButton.linkField ?? false or searchBarVisibleOnPageLoad ?: 'js--search-toggle',
                        ],
                        href: bookButton.linkField.value ?? (searchBarVisibleOnPageLoad ? '#search-accommodation' : null),
                        target: bookButton.linkField.target ?? null,
                        'aria-pressed': (bookButton.linkField ?? false or searchBarVisibleOnPageLoad) ? null : 'false',
                        'aria-controls': (bookButton.linkField ?? false or searchBarVisibleOnPageLoad) ? null : 'search-accommodation',
                        'data-gtm-id': "header-book-btn"
                    } %}
                        {{ svg('@webroot/assets/icon-system/icon_search.svg')|attr({ class: 'icon-search' }) }}
                        {% if not (bookButton.linkField ?? false or searchBarVisibleOnPageLoad) %}
                            {{ svg('@webroot/assets/icon-system/icon_form_cross.svg')|attr({ class: 'icon-close' }) }}
                        {% endif %}
                        <span class="book-button__label js--book-button-label">Book</span>
                    {% endtag %}
                {% endif %}

                {% include "01_core/_layouts/primary-navigation.twig" %}
            </div>
        </div>

    </div>

</header>

{% js at beginBody %}
window.addEventListener("load", function () {
    const mobileBreakpoint = 992;
    const headerParentAnchor = document.getElementById('js-header');
    const headerInnerAnchor = document.getElementById('js-header-inner');
    let headerTopOffset = headerInnerAnchor.getBoundingClientRect().top + window.scrollY;
    window.__headerTopOffset = headerTopOffset;
    const activeRibbons = {{ craft.entries.section('alerts').type('ribbonAlert').ids()|json_encode|raw }};
    const navigation = document.querySelector('#nav');
    const searchParentAnchor = document.getElementById('search-accommodation');
    let windowHeight = window.innerHeight || window.screen.height;
    let currentScrollValue;

    /* --------------------------------------
    Sticky header on scroll
    -------------------------------------- */
    function stickyHeader() {
        if(currentScrollValue >= (window.__headerTopOffset)) {
            document.documentElement.classList.add('header--sticky');
        } else if(currentScrollValue < (window.__headerTopOffset)) {
            document.documentElement.classList.remove('header--sticky');
        }
    }

    window.__stickyHeader = stickyHeader;

    /* --------------------------------------
    Move the mobile menu sit at the bottom of the header
    -------------------------------------- */
    function moveMobileMenu() {
        // When there are alerts the mobile menu need to be moved with the scroll to ensure it sits beneath the header until the header becomes fixed
        if(!!navigation && window.innerWidth < mobileBreakpoint && activeRibbons.length > 0) {
            if(currentScrollValue < (window.__headerTopOffset)) {
                console.log('animate the menu top');
                document.documentElement.classList.add('menu--scroll-responsive');
                navigation.style.top = headerParentAnchor.getBoundingClientRect().bottom + 'px';
            } else {
                document.documentElement.classList.remove('menu--scroll-responsive');
                navigation.style.top = '54px';

                requestAnimationFrame(() => {
                    navigation.style = null;
                });
            }
        } else if(!!navigation && window.innerWidth > mobileBreakpoint && activeRibbons.length > 0) {
            navigation.style = null;
        }
    }

    window.__moveMobileMenu = moveMobileMenu;

    /* --------------------------------------
    Shrink header height on scroll
    -------------------------------------- */
    function shrinkHeader() {
        let shrinkScrollPoint = document.querySelector("body").classList.contains('home') ? windowHeight : headerParentAnchor.getBoundingClientRect().height;
        if(window.innerWidth >= mobileBreakpoint && currentScrollValue >= shrinkScrollPoint) {
            document.documentElement.classList.add('header--shrink');
        } else if(window.innerWidth >= mobileBreakpoint && currentScrollValue < shrinkScrollPoint) {
            document.documentElement.classList.remove('header--shrink');
        }
    }

    // On scroll event
    window.addEventListener('scroll', function() {
        currentScrollValue = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
        shrinkHeader();
        window.__stickyHeader();
        window.__moveMobileMenu();
    }, false);

    // On page load
    currentScrollValue = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    shrinkHeader();
    window.__moveMobileMenu();

    // If there are active ribbons let the ribbon alert code handle setting the point when the header is sticky.
    // the header sticky toggle point needs to account for the alert ribbons
    if (activeRibbons.length == 0) {
        window.__stickyHeader();
    }
});
{% endjs %}
