<!-- Template: {{ _self }} -->

{% set modalEntry = craft.entries.section('alerts').type('modalWindow').one() %}
{% set currentUri = craft.app.request.getFullUri() %}
{% set displayModal = false %}

{% if modalEntry and modalEntry.showOnAllPages %}
<!-- Display modal on all pages -->

    {% set displayModal = true %}
    {% set excludeUrlsContain = [] %}
    {% set excludeUrlsExact = [] %}

{% elseif modalEntry %}
    <!-- Choose which pages to display modal -->
    {% set urlsContain = clone(modalEntry.modalDisplayUrls).type('urlContains').all() %}
    {% set urlsExact = clone(modalEntry.modalDisplayUrls).type('urlExact').all() %}

    {# exclusions set in the JS #}
    {% set excludeUrlsContain = clone(modalEntry.modalDisplayUrls).type('urlDoesNotContain').all() %}
    {% set excludeUrlsExact = clone(modalEntry.modalDisplayUrls).type('urlDoesNotExactlyMatch').all() %}

    {% if modalEntry.showOnHomepage is defined and modalEntry.showOnHomepage %}
        {% if entry ?? false and entry.isHomepage %}
            {% set displayModal = true %}
        {% endif %}
    {% endif %}

    {% if urlsExact ?? false %}
        {% for item in urlsExact %}
            {% if currentUri == item.urlExact|trim('/') %}
                {% set displayModal = true %}
            {% endif %}
        {% endfor %}
    {% endif %}

    {% if urlsContain ?? false %}
        {% for item in urlsContain %}
            {% if item.urlContains in currentUri %}
                {% set displayModal = true %}
            {% endif %}
        {% endfor %}
    {% endif %}

{% endif %}



{% if displayModal %}
<!-- Modal is active -->

<div class="modal-window js-modal-window" id="modal-window">
    <div class="modal-window__mainbox">
        <div class="modal-window__mainbox__content">

            <div class="md-window md-window--{{ modalEntry.modalLayout ?? 'centered' }}">


                <h1>{{ modalEntry.title }}</h1>

                {% if modalEntry.windowContent %}
                {% for block in modalEntry.windowContent.all() %}
                {% switch block.type %}

                    {% case 'modalCopy' %}
                    {{ block.richtextSansMedia }}

                    {% case 'form' %}
                    {% if block.form ?? false %}
                        {% include '02_components/form' %}
                    {% endif %}

                {% endswitch %}
                {% endfor %}
                {% endif %}

                {% if modalEntry.contentCloseButton ?? false %}
                    <button class="js-modal-window-close button">Continue to website</button>
                {% endif %}


           </div>

        </div>
        <button class="modal-window__mainbox__closebtn js-modal-window-close">
            <span class="-vis-hidden">close window</span>
            {{ svg('@webroot/assets/icon-system/icon_form_cross.svg') }}
        </button>
    </div>
</div>

{% js %}

{# convert seconds to milliseconds for delay #}
{% set modalLoadDelay = modalEntry.loadDelay is defined ? modalEntry.loadDelay * 1000 : 3000 %}

// config settings
const waitDays = {{(modalEntry.waitDays ?? 1)|json_encode}};
const loadDelay = {{ modalLoadDelay|json_encode }}; // time in miliseconds
const excludedPathsContain = {{ excludeUrlsContain|map(block => block.urlContains)|json_encode|raw }}; // Slug contains
const excludedPathsExact = {{ excludeUrlsExact|map(block => block.urlExact)|json_encode|raw }}; // Slug exactly, must begin with forward slash
const showOnHomepage = {{ (modalEntry.showOnHomepage ?? true)|json_encode }};
const exitIntent = {{ (modalEntry.displayOnExit ?? false)|json_encode }}; // show immediately or display on exit intent detect
const debugMode = true;
// You can use `localStorage.clear()` in your console to clear the local storage.

// vars used by functions
const currentDate = new Date();
const expiryDate = currentDate.getTime() + (waitDays * 24 * 60 * 60 * 1000);
const currentPage = location.pathname;

// set page exclusions
let showOnCurrentPage = true;
for (const path of excludedPathsContain) {
    if (currentPage.includes(path)) {
        showOnCurrentPage = false;
    }
}
for (const path of excludedPathsExact) {
    if (currentPage == path ) {
        showOnCurrentPage = false;
    }
}

if (debugMode == true) {
    console.log('showOnCurrentPage = ' + showOnCurrentPage);
    console.log('showOnHomepage = ' + showOnHomepage);
    console.log('exitIntent = ' + exitIntent);
    console.log('waitDays = ' + waitDays);
    console.log('currentDate = ' + currentDate.getTime() + (waitDays * 24 * 60 * 60 * 1000));
    console.log('localStorage.modalDisplayed = ' + localStorage.modalDisplayed);

    if (localStorage.modalDisplayed < currentDate) {
        console.log('Past Expiry date');
    } else {
        console.log('Not yet past expiry date');
    }

    if (localStorage.getItem('modalDisplayed')) {
        console.log('modalDisplayed storage is set');
    }
}

// close the modal when click the close button
const exit = e => {
    let shouldCloseModal = false;

    // user clicks on the close icon
    // user clicks on mask
    if (e.currentTarget && e.currentTarget.classList && (
        e.target.classList.contains('js-modal-window') ||
        e.target.classList.contains('js-modal-window-close')
    )) {
        shouldCloseModal = true;
    }
    // user hits escape
    else if (e.key === 'Escape') {
        shouldCloseModal = true;
    }

    if (shouldCloseModal) {
        document.querySelector('.js-modal-window').classList.remove('modal-window--visible');
    }
};

document.querySelector('.js-modal-window').addEventListener('click', exit);

//
function displayModal() {
    // Add the class that shows the modal
    document.querySelector('.js-modal-window').classList.add('modal-window--visible');
    // Set Modal Storage
    localStorage.setItem('modalDisplayed', expiryDate);
}


// Mouse Exit Intent function - watch for mouse leaving the window
const mouseEvent = e => {
    const showExitIntent =
        !e.toElement &&
        !e.relatedTarget &&
        // exclude sideways mouse movement - probably not closing window
        e.clientY < 10;

    if (showExitIntent) {
        document.removeEventListener('mouseout', mouseEvent);
        displayModal();
    }
};


// Mobile exit intent function - scroll half then up at speed
function myScrollSpeedFunction() {
    const delta = scrollSpeed();
    if(delta < -20 && !localStorage.getItem('modalDisplayed')){
        document.removeEventListener('scroll', mobileModalEventsListen);
        displayModal();
    }
}

var scrollSpeed = (() => {
    let lastPosition, newPosition, timer, delta, delay = 50;

    function clear() {
        lastPosition = null;
        delta = 0;
    }

    clear();
    return () => {
        newPosition = window.scrollY;
        if (lastPosition != null){
            delta = newPosition - lastPosition;
        }

        lastPosition = newPosition;
        clearTimeout(timer);

        timer = setTimeout(clear, delay);

        return delta;
    };
})()


// Wait until 50% of the page has been viewed and have been on page for a time.
function mobileModalEventsListen() {
    const currentScrollValue = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    const halfBodyHeight = document.body.offsetHeight*.5 + window.innerHeight*.5;

    if (currentScrollValue > halfBodyHeight) {
        if (debugMode == true) {
            console.log('over halfBodyHeight')
        };

        myScrollSpeedFunction();
    }
}

function touchListen() {
    // when a touch event is detected, start the listening for mobile events
    document.addEventListener('scroll', mobileModalEventsListen);
}

function modalEventsListen() {
    // after time delay and watch for events
    setTimeout(() => {
        if (exitIntent == true) {
            document.addEventListener('touchstart', touchListen); // listen for a touch event
            document.addEventListener('mouseout', mouseEvent); // listen for mouse event
        } else {
            displayModal(); // launch the modal immediately
        }
        document.addEventListener('keydown', exit); // listen for exit for both desk and mob
    }, loadDelay); // time before the popup can appear
}


// If meets criterial, show the modal
if (showOnCurrentPage == true && !localStorage.getItem('modalDisplayed')) {
    modalEventsListen();

    // if modal fires then set storage
} else if (showOnCurrentPage == true && currentDate > (localStorage.getItem('modalDisplayed')) ) {
    // Remove expired storage
    localStorage.removeItem('modalDisplayed');
    modalEventsListen();
}


{% endjs %}


{% endif %}
