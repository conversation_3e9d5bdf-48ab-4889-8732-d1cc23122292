<!-- Template: {{ _self }} -->

{% if craft.entries.section('alerts').type('ribbonAlert').count() > 0 %}
<div class="ribbon-alerts">
    {% for entry in craft.entries.section('alerts').type('ribbonAlert').all() %}
        <div
            class="ribbon-alert ribbon-alert--hidden{{ entry.theme.value ?? false ? ' ribbon-alert--' ~ entry.theme }}" id="{{ entry.id }}"
            data-storage="{{ entry.alertShow ? 'timeframe' : 'session'}}"
            {{ entry.alertShow ? 'data-expiration=' ~ entry.waitDays }}
        >
             <div class="ribbon-alert__container container">
                <div class="ribbon-alert__text">
                    {% set icon = entry.systemIcon.one() ?? null %}
                    {% if icon %}
                        <div class="icon">
                            {{ svg(icon) }}
                        </div>
                    {% endif %}
                    {{ entry.alertMessage }}
                </div>
                <button class="ribbon-alert__closebtn icon">
                    {{ svg('@webroot/assets/icon-system/icon_form_cross.svg')|attr({ class: '' }) }}
                </button>
            </div>
        </div>
    {% endfor %}
</div>

{% js at beginBody %}
window.addEventListener('load', function() {

    /* SLIDE UP - Replaces jquery's slide up  */
    let slideUp = (target, duration=400) => {
        target.style.transitionProperty = 'height, margin, padding';
        target.style.transitionDuration = duration + 'ms';
        target.style.boxSizing = 'border-box';
        target.style.height = target.offsetHeight + 'px';
        target.offsetHeight;
        target.style.overflow = 'hidden';
        target.style.height = 0;
        target.style.paddingTop = 0;
        target.style.paddingBottom = 0;
        target.style.marginTop = 0;
        target.style.marginBottom = 0;
        window.setTimeout( () => {

            target.style.display = 'none';
            target.style.removeProperty('height');
            target.style.removeProperty('padding-top');
            target.style.removeProperty('padding-bottom');
            target.style.removeProperty('margin-top');
            target.style.removeProperty('margin-bottom');
            target.style.removeProperty('overflow');
            target.style.removeProperty('transition-duration');
            target.style.removeProperty('transition-property');
            //alert("!");

            // set ribbon alerts css height variable
            const ribbonAlertsContainer = document.querySelector('.ribbon-alerts');
            const height = ribbonAlertsContainer.getBoundingClientRect().height;
            setRibbonAlertsCssVariableHeight(height);

        }, duration);
    }

    // Create a function for setting a variable value
    function setRibbonAlertsCssVariableHeight(height) {
        const r = document.querySelector(':root');

        // Set the value of variable
        r.style.setProperty('--ribbon-alerts-height', height + 'px');
    }

    /* SLIDE DOWN - Replaces jquery's slide down */
    let slideDown = (target, duration=400) => {
        target.style.removeProperty('display');
        let display = window.getComputedStyle(target).display;
        if (display === 'none') display = 'block';
        target.style.display = display;
        let height = target.offsetHeight;
        target.style.overflow = 'hidden';
        target.style.height = 0;
        target.style.paddingTop = 0;
        target.style.paddingBottom = 0;
        target.style.marginTop = 0;
        target.style.marginBottom = 0;
        target.offsetHeight;
        target.style.boxSizing = 'border-box';
        target.style.transitionProperty = "height, margin, padding";
        target.style.transitionDuration = duration + 'ms';
        target.style.height = height + 'px';
        target.style.removeProperty('padding-top');
        target.style.removeProperty('padding-bottom');
        target.style.removeProperty('margin-top');
        target.style.removeProperty('margin-bottom');
        window.setTimeout( () => {

            target.style.removeProperty('height');
            target.style.removeProperty('overflow');
            target.style.removeProperty('transition-duration');
            target.style.removeProperty('transition-property');

           // set ribbon alerts css height variable
            const ribbonAlertsContainer = document.querySelector('.ribbon-alerts');
            const height = ribbonAlertsContainer.getBoundingClientRect().height;
            setRibbonAlertsCssVariableHeight(height);

        }, duration);
    }

    /* -------------------------------
    Show, hide and store the ribbons
    ---------------------------------- */

    // Select all the ribbons
    const ribbons = document.querySelectorAll('.ribbon-alert');
    const animationDuration = 150;
    const currentDate = new Date();

    // Initialise
    ribbons.forEach(ribbon => {
        // Attach a click handler to all the ribbon alert dismiss buttons
        ribbon.querySelector('.ribbon-alert__closebtn').addEventListener("click", closeRibbonAlert, false);

        // Show the ribbons (they're hidden by default)
        if (checkRibbonIsStored(ribbon) === false) {
            slideDown(ribbon, animationDuration);
            ribbon.classList.remove('ribbon-alert--hidden');
        }

        // update the top offset for the header
        document.documentElement.classList.remove('header--sticky');
        setTimeout(function() {
            window.__headerTopOffset = document.getElementById('js-header').getBoundingClientRect().top + window.scrollY;
            window.__stickyHeader();
        }, animationDuration);

    });

    function checkRibbonIsStored(ribbon) {
        // check if a ribbon is per session or has a timeframe expiration
        let storageType = ribbon.getAttribute('data-storage');
        let ribbonId = ribbon.getAttribute('id');

        if(storageType == 'session') {
            return sessionStorage.getItem('ribbon' + ribbonId) ? true : false;
        } else if(storageType == 'timeframe') {
            if(localStorage.getItem('ribbon' + ribbonId)) {
                return currentDate < (localStorage.getItem('ribbon' + ribbonId)) ? true : false;
            }
            return false;
        }
    }

    function closeRibbonAlert(e) {
        if (e instanceof Event) {
            e.preventDefault();
            e.stopPropagation();
        }

        const ribbon = e.target.closest('.ribbon-alert');

        slideUp(ribbon, animationDuration);
        ribbon.classList.add('ribbon-alert--hidden');

        storeTheRibbon(ribbon);

        // update the top offset for the header now the ribbon's been hidden
        setTimeout(function() {
            window.__headerTopOffset = document.getElementById('js-header').getBoundingClientRect().top + window.scrollY;
            window.__stickyHeader();
        }, animationDuration);
    }

    // Store the ribbon
    function storeTheRibbon(ribbon) {
        // check if a ribbon is per session or has a timeframe expiration
        let storageType = ribbon.getAttribute('data-storage');
        let ribbonId = ribbon.getAttribute('id');
        if(storageType == 'session') {
            sessionStorage.setItem('ribbon' + ribbonId, 'dismissed');
        } else if(storageType == 'timeframe') {
            let expirationDays = parseInt(ribbon.getAttribute('data-expiration'));
            let expiryDate = parseInt(currentDate.getTime() + (expirationDays * 1000 * 60 * 60 * 24));
            localStorage.setItem('ribbon' + ribbonId, expiryDate);
        }
    }
});

{% endjs %}
{% endif %}

