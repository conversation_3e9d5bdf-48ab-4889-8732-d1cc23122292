<!-- Template: {{ _self }} -->

{% import "01_core/_macros/macros-navigation" as navMacros %}


{% macro crumb( label, link, itemClass ) %}
    <li class="breadcrumb__crumb {{ itemClass ?? null }}"><a href="{{ link }}">{{ label }}</a></li>
{% endmacro %}



{% macro entryUriLink( crumb ) %}
    <a class="breadcrumb__crumb__link" href="{{ crumb.url }}">{{ crumb.navigationTitle ?? crumb.title }}</a>
{% endmacro %}



{% macro slugLink( loop, uriArray ) %}
    <a class="breadcrumb__crumb__link" href="{%- for i in 0..(loop.index0) -%}{%- if not loop.last -%}/{{ uriArray[i]}}{%- endif -%}{%- endfor -%}/{{ uriArray[loop.index0]}}">{{ navMacros.slug_to_title(uriArray[ loop.index0 ]) }}</a>
{% endmacro slugLink %}



{% macro entryBreadcrumb(entry) %}
    {% for crumb in entry.getAncestors().all() %}
        <li class="breadcrumb__crumb">{{ _self.entryUriLink( crumb ) }}</li>
    {% endfor %}
    <li class="breadcrumb__crumb"><span class="breadcrumb__crumb__label" >{{ entry.navigationTitle ?? entry.title }}</span></li>
{% endmacro entryBreadcrumb %}



{% macro urlBreadcrumb(uriArray, lastItemHtml) %}
    {% set collections = ['posts', 'teamMembers'] %}

    {% for uriItem in uriArray %}
        {%- if loop.last -%}
            {{ lastItemHtml }}
        {%- elseif uriItem in collections -%}
            {% set parentPage = siteIdentity[uriItem ~ 'ParentPage'].one() ?? null %}

            <li class="breadcrumb__crumb">
                {% if parentPage %}
                    {{ _self.entryUriLink( parentPage ) }}
                {% else %}
                    {{ _self.slugLink( loop, uriArray ) }}
                {% endif %}
            </li>

        {%- elseif not loop.last -%}
            <li class="breadcrumb__crumb">{{ _self.slugLink( loop, uriArray ) }}</li>
        {%- endif -%}
    {% endfor %}
{% endmacro urlBreadcrumb %}



{% set uriArray = craft.app.request.segments %}
<ul class="breadcrumb {{ entry ?? false ? entry.section.type : 'static' }} {{ modifierClass ?? null }}">

    {{ _self.crumb( "Home", "/" ) }}

     {% if entry.section.handle ?? false and entry.section.handle == 'categories' %}
        {{ _self.urlBreadcrumb( uriArray, null ) }}

    {% elseif entry.section.type ?? false and entry.section.type == 'structure' %}
        {{ _self.entryBreadcrumb( entry ) }}

    {% elseif entry.section.type ?? false and entry.section.type == 'channel' %}
        {% set lastItemHtml %}<li class="breadcrumb__crumb"><span>{{ entry.navigationTitle ?? entry.title }}</span></li>{% endset %}
        {{ _self.urlBreadcrumb( uriArray, lastItemHtml ) }}

    {% elseif entry.section.type ?? false and entry.section.type == 'single' %}
        {{ _self.entryBreadcrumb( entry ) }}

    {% else %}
        {% set lastItemHtml %}<li class="breadcrumb__crumb"><span>{{ navMacros.slug_to_title(uriArray|last ) }}</span></li>{% endset %}
        {{ _self.urlBreadcrumb( uriArray, lastItemHtml ) }}

    {% endif %}

</ul>
