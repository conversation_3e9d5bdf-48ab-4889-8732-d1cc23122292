<!-- Template: {{ _self }} -->

{% import "01_core/_macros/macros-navigation" as navMacros %}

{% set deviceVisibility = block.deviceVisibility ?? null %}
{% set navEntry = block.targetEntry.one() ?? null %}
{% set navItemUrl = navItemUrl ?? block.targetUrl.value ?? block.targetSlug ?? block.targetEntry.one().url ?? null %}


{% if navEntry %}

    {# Structure for Entry data source (and all its extra data) #}
    {% set childrenPages = craft.entries.section('pages').level('<=' ~ maximumDepth).descendantOf(navEntry).navigationVisibility('primaryMenu').all() %}
    {% set navEntryAsArray = [navEntry] %}
    {% set allPages = navEntryAsArray|merge(childrenPages) %}

    {% nav page in allPages %}
    {% set showSubnav = page.hasDescendants() and page.level < maximumDepth ?? false %}

    <li class="
        {{ itemClass }}
        {{ itemClass ~ '--' ~ block.owner.type|cc2kc }}
        {{ itemClass ~ '--depth-' ~ page.level }}
        {{ deviceVisibility ? itemClass ~ '--' ~ deviceVisibility }}
        {{ showSubnav ? itemClass ~ '--has-children' }}
        {{ showSubnav and navMacros.is_ancestor( page.slug ) ? itemClass ~ '--' ~ navMacros.is_ancestor( page.slug ) }}
        {{ navMacros.is_active(page.slug) ? itemClass ~ '--' ~ navMacros.is_active(page.slug) }}"
    >
        <a
            class="{{ itemClass ~ '__link' }}"
            href="{{ page.url }}" {{ block.newWindow ? 'target="_blank"' : '' }}
        >
            {{ page.navigationTitle ?? page.title }}
        </a>

        {# Subnav #}
        {% if showSubnav %}
        <span class="subnav-toggle js-togg-submenu">Toggle Submenu</span>
        {% endif %}
        {% ifchildren %}
            <ul class="subnav subnav--depth-{{ page.level }}">
                {% children %}
            </ul>
        {% endifchildren %}
    </li>
    {% endnav %}

{% else %}

    {# Structure for simpler link/slug/url field data #}
    {% tag 'li' with {
        class: [
            itemClass,
            itemClass ~ '--' ~ block.owner.type|cc2kc,
            itemClass ~ '--depth-1',
            navMacros.is_active(navEntry.slug ?? navEntry) ? itemClass ~ '--' ~ navMacros.is_active(navEntry.slug ?? navEntry),
            deviceVisibility ? itemClass ~ '--' ~ deviceVisibility : null,
        ]}
    %}
        {% tag 'a' with {
            class: itemClass ~ '__link',
            href: navItemUrl
        } %}
            {{ block.linkLabel ?? null }}
        {% endtag %}
    {% endtag %}

{% endif %}
