<!-- Template: {{ _self }} -->

{% set navConfiguration = craft.entries.section('primaryNavigation').type('navigationConfiguration').one() ?? null %}
{% set menuType = navConfiguration.menuType.value ?? 'fullScreen' %}

{% set navColumns = craft.entries.section('primaryNavigation').type('menuColumn').all() ?? null %}
{% set maximumDepth = 1 %}

{% if navColumns ?? false %}

<div class="menu-btn js-menu-open-close menu-btn--{{ menuType|kebab }}" data-gtm-id="menu-btn">
    {{ svg('@webroot/assets/icon-system/icon_menu.svg') }}
    <span class="menu-btn__label">menu</span>
</div>

<nav id="nav" class="primary-nav-container primary-nav-container--{{ menuType|kebab }}" aria-label="Main menu">
    <ul class="primary-nav primary-nav--{{ menuType|kebab }} primary-nav--{{ navConfiguration.desktopSubNavigationToggleTrigger ? 'click' : 'hover' }}">

        <li class="menu-item menu-item--home menu-item--depth-1">
            <a class="menu-item__link" href="/">
                Home
            </a>
        </li>

        {% for column in navColumns %}
            {% tag menuType == 'fullScreen' ? 'div' : null with {
                class: [
                    'primary-nav__column',
                ],
            } %}
                {% if menuType == 'fullScreen' and column.itemTitle ?? null %}
                    <h2 class="primary-nav__column-title">{{ column.itemTitle }}</h2>
                {% endif %}

                {% set menuItems = column.menuItems.all() ?? null %}
                {% for menuItem in menuItems %}
                    {% include '01_core/_layouts/menu-item.twig' with {
                        depth: 0,
                        itemClass: 'menu-item',
                    } %}
                {% endfor %}

            {% endtag %}
        {% endfor %}
    </ul>
</nav>

{% js at head %}
(function(cb) {
    if (document.readyState == 'complete') cb();
    else document.addEventListener('DOMContentLoaded', cb);
})(function() {
    document.removeEventListener('DOMContentLoaded', this);


    const menu = document.querySelector('.primary-nav-container');
    const menuToggleButton = document.querySelector('.js-menu-open-close');
    const menuToggleButtonLabel = menuToggleButton.querySelector('.menu-btn__label');
    const depth2Submenus = document.querySelectorAll('.subnav--depth-2');
    const closeMenuButton = document.querySelectorAll('.js--close-menu');

    closeMenuButton.forEach(closeBtn => {
        closeBtn.addEventListener("click", toggleMenu, false);
    })

    // Check for click events on the menu toggle button
    menuToggleButton.addEventListener("click", toggleMenu, false);

    function toggleMenu() {
        if(document.documentElement.classList.contains('js-menu--open')) {
            document.documentElement.classList.remove('js-menu--open');
            menuToggleButton.setAttribute('aria-pressed', false);
            menuToggleButtonLabel.innerHTML = 'menu';
        } else {
            document.documentElement.classList.add('js-menu--open');
            menuToggleButton.setAttribute('aria-pressed', true);
            menuToggleButtonLabel.innerHTML = 'close';

            // Close the search bar if open
            if(document.getElementById('search-accommodation').getAttribute('aria-expanded') == "true") {
                document.querySelector('.js--search-toggle').click();
            }
        }
    }

    // Toggle sub-menu
    const submenuToggles = document.querySelectorAll('.js-togg-submenu');

    submenuToggles.forEach(submenuToggle => {
        submenuToggle.addEventListener("click", toggleSubmenu, false);
    })

    function toggleSubmenu(e) {
        let $parentMenuItem = e.target.closest('.menu-item--has-children');

        if($parentMenuItem.classList.contains('menu-item--has-children-open')) {
            $parentMenuItem.classList.remove('menu-item--has-children-open');
        } else {
            $parentMenuItem.classList.add('menu-item--has-children-open');
        }
    }

    // Ensure any depth two items don't hit or overflow the sides of the screen
    function positionDepth2Submenus() {
        depth2Submenus.forEach(submenu => {
            if(submenu.getBoundingClientRect().right >= window.innerWidth - 12) {
                submenu.style.left = 'auto';
                submenu.style.right = '100%';
            } else {
                {# alternatively, you can use:
                 # else if(submenu.getBoundingClientRect().left - submenu.getBoundingClientRect().width < 0)
                 # The above means the depth 2 submenu will more often sit left of the previous menu
                 # because it requires the depth 2 submenu's left hand side to intersect the left side of the window
                 # before it will sit the menu on the right side of the previous menu
                 # which will occur very rarely, maybe never, based on our usual layouts
                 #}
                submenu.style.left = '100%';
                submenu.style.right = 'auto';
            }
        })
    }

    if(!!depth2Submenus && depth2Submenus.length > 0) {
        positionDepth2Submenus();
        window.addEventListener("resize", positionDepth2Submenus);
    }

});
{% endjs %}

{% endif %}
