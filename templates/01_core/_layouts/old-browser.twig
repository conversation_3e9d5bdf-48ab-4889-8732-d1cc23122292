<!-- googleoff: all -->
<div class="old-browser__banner">
    <div class="old-browser__center">
        <p>This website uses modern construction techniques, which may not render correctly in your old browser. <br>We recommend updating your browser for the best online experience.</p> <p>Visit <a href="http://browsehappy.com/">browsehappy.com</a> to help you select an upgrade.</p>
    </div>
</div>
<!-- googleon: all -->

<script>
(function() {
    try {
        Promise.resolve();     // IE:-- S:8    iOS:8
        new URLSearchParams(); // IE:-- S:10.1 iOS:10.3
        new Proxy({}, {});     // IE:-- S:10   iOS:10
    }
    catch (error) {
        console.log("Detected unsupported browser:", error.message);
        document.documentElement.className += " old-browser ";
    }
})();
</script>
