<!-- Template: {{ _self }} -->
{# Boolean: Whether the sidebar is enabled is dependent upon if there's a static set preference or if the entry has the sidebar enabled #}
{% set sidebarEnabled = sidebarEnabled is defined
    ? sidebarEnabled
    : entry ?? false and entry.enableSidebar
    ? entry.enableSidebar
    : false %}

{# String: The layout class is dependent upon if there's a hard set preferred layout or if there's sidebar #}
{% set layout = layout is defined
    ? layout
    : sidebarEnabled
    ? 'page-layout--skew'
    : 'page-layout--center' %}

<main class="page-layout {{ layout }} section {{ bannerImage is not defined and ( entry ?? false and entry.banner|length == 0 ) ? 'section-small' }} bg-default">

    {% block page_title %}
    {% if entry ?? false and entry.banner|length == 0 %}
    {% include '@page-header' %}
    {% endif %}
    {% endblock %}

    {% if sidebarEnabled %}
    <div class="mainbar">
    {% endif %}
    {% block main_bar %}
    {% include '01_core/_layouts/page-content' %}
    {% endblock %}
    {% if sidebarEnabled %}
    </div>
    {% endif %}

    {% if sidebarEnabled %}
    <div class="sidebar">
        {% block side_bar %}
            {% if entry ?? false %}

                {% for addOnEntry in entry.sidebarAddons.all() %}
                    {% include '02_components/' ~ addOnEntry.type ignore missing with {
                        'addOn': {
                            modifierClass: 'sidebar-widget'
                        }
                    } %}
                {% endfor %}

                {% if entry.sidebarAddons|length == 0 %}
                    {% include '02_components/relatedLinks' ignore missing with {
                        'addOn': {
                            modifierClass: 'sidebar-widget'
                        }
                    } %}
                {% endif %}

            {% endif %}
        {% endblock %}
    </div>
    {% endif %}

    <div class="bottombar">
        {% block bottom_bar %}
            {% set bottomBarAddons = entry.bottomBarAddons.all() ?? null %}

            {% if entry ?? false and bottomBarAddons %}
                {% for addOnEntry in bottomBarAddons %}
                    {% include '02_components/' ~ addOnEntry.type ignore missing with {
                        'addOn': {
                            modifierClass: 'bottombar-widget'
                        }
                    } %}
                {% endfor %}
            {% endif %}
        {% endblock %}
    </div>

</main>
