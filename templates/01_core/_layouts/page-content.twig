<!-- Template: {{ _self }} -->
{% set pageContentBlocks = entry.pageContent.all() ?? null %}
{% if pageContentBlocks %}
    {% for block in pageContentBlocks %}
        {# troubleshoot
        {% if craft.app.config.general.devMode or currentUser %}
            <pre>{{ block.type }}</pre>
        {% endif %} #}

        {% switch block.type %}

            {% case 'pageAddOn' %}
            {% set addOnEntry = block.addOnItem.one() %}
            {% include '02_components/' ~ addOnEntry.type ignore missing with {
                'addOn': {
                    modifierClass: not sidebarEnabled ? 'mainbar'
                }
            } %}

            {% default %}
            {% include '02_components/' ~ block.type ignore missing with {
                'addOn': {
                    modifierClass: not sidebarEnabled ? 'mainbar'
                }
            } %}

        {% endswitch %}
    {% endfor %}
{% endif %}



