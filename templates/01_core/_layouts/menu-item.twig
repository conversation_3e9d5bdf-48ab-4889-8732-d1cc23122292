<!-- Template: {{ _self }} -->

{% import "01_core/_macros/macros-navigation" as navMacros %}

{% set link = menuItem.linkField %}
{% set customChildrenNav = menuItem.customSecondaryLinks.all() ?? null %}
{% set showSubnav = (menuItem.showAllChildrenPages and menuItem.linkField.type == 'entry') or (customChildrenNav ?? null) %}
{% set navEntry = menuItem.linkField.type is defined and menuItem.linkField.type == 'entry' ? link.element : menuItem %}
{% set linkUrl = navEntry.url ?? link.value ?? null %}

{#
#  navigationTitleOverride
#  the link.label if left empty will autopopulate with the entry title
#  but if there's navigationTitle set, the navigation title needs override the link.lable autopopulated value
#}
{% set navigationTitleOverride = navEntry is defined and link.label ?? false and link.label == navEntry.title and navEntry.navigationTitle ?? false and link.label != navEntry.navigationTitle ? navEntry.navigationTitle : link.label ?? null %}
{% set menuItemLabel = navigationTitleOverride ?? link.label ?? navEntry.navigationTitle ?? navEntry.title %}

{% if linkUrl ?? false %}
  <li class="
    {{ itemClass }}
    {{ itemClass ~ '--' ~ column.itemTitle|cc2kc }}
    {{ itemClass ~ '--depth-' ~ (depth + 1) }}
    {{ showSubnav ? itemClass ~ '--has-children' }}
    {{ showSubnav and navMacros.is_ancestor( navEntry.slug ?? link.value ) ? itemClass ~ '--' ~ navMacros.is_ancestor( navEntry.slug ) }}
    {{ navMacros.is_active(navEntry.slug ?? link.value) ? itemClass ~ '--' ~ navMacros.is_active(navEntry.slug) }}"
>
    <a
        class="{{ itemClass ~ '__link' }}{{ link.value is defined and link.value == '#contact' ? ' js--close-menu' }}"
        href="{{ linkUrl }}"
        target="{{ link.target ?? null }}"
        data-gtm-id="{{ 'primary-navigation-' ~ menuItemLabel|kebab|lower ~ '-btn' }}"
    >
        {{ menuItemLabel }}
    </a>

    {% if menuItem.type == 'menuItem' and menuItem.subtitle %}
        <p class="{{ itemClass }}__subtitle">{{ menuItem.subtitle }}</p>
    {% endif %}

    {# Subnav #}
    {% if showSubnav %}
    <span class="subnav-toggle js-togg-submenu">Toggle Submenu</span>
    {% endif %}

    {% set secondaryNav = showSubnav ? (menuItem.showAllChildrenPages ? navEntry.getChildren().navigationVisibility('primaryMenu').all() : menuItem.customSecondaryLinks.all()) : null %}

    {% if showSubnav and secondaryNav %}
        <ul class="subnav subnav--{{ navEntry.slug }} subnav--depth-{{ (depth + 1) }}">
            {% for submenuItem in secondaryNav %}
                {% include '01_core/_layouts/menu-item.twig' with {
                    depth: depth + 1,
                    menuItem: submenuItem
                } %}
            {% endfor %}
        </ul>
    {% endif %}

</li>
{% endif %}
