{# --------------------------------------
    Embedded Video Helpers
--------------------------------------- #}

{# This will return either a youtube or Vimeo link or nothing #}
{% macro getHostedVideoLink(link) %}
    {%- if link matches '{(youtube.com/embed)}' -%}
        {{ link }}
    {%- elseif link matches '{(youtu.be)}' or link matches '{(youtube.com/live)}' -%}
        https://www.youtube.com/embed/{{ link|replace('/^(.*)\\/(.{11})\\??[^?]*$/', '$2') }}
    {%- elseif link matches '{(youtube)}' -%}
        https://www.youtube.com/embed/{{ link|replace('/^(.*)(.{11})$/', '$2') }}
    {%- endif -%}

    {%- if link matches '{(vimeo)}' -%}
        https://player.vimeo.com/video/{{ link|replace('/^(.*?)(\\d+)$/', '$2') }}
    {%- endif -%}
{% endmacro %}

{# This will return either a youtube ID or nothing #}
{% macro getYouTubeId(link) %}
    {%- if link matches '{(youtube)|(youtu.be)}' -%}
        {{ link|replace('/^(.*)(.{11})$/', '$2') }}
    {%- endif -%}
{% endmacro %}




{# --------------------------------------
    Phone Number
 --------------------------------------- #}

{% macro phoneFormat(number) %}
    {%- if number matches '{(tel)}' -%}
        {% set number = number|replace('/\D', '') %}
        {% set number = number|replace('tel:', '') %}
        {% set number = number|replace('-', ' ') %}
        {% if not number matches '{1[0-9]00}' %}
            {% set number = number|replace('/^\w*([0-3,5-9]{2})/', '($1) ') %}
        {% endif %}
        {{ number }}
    {%- else -%}
        {{ number }}<small>Invalid</small>
    {%- endif -%}
{% endmacro %}



{# --------------------------------------
    Link Helper Link
 --------------------------------------- #}

 {%- macro linkHelperLink(block) -%}
    {% switch block.type %}
        {% case 'pageEntry' %}
        {%- set targetEntry = block.targetEntry.one() -%}
        {{ targetEntry ? targetEntry.url : 'empty' }}

        {% case 'externalUrl' %}
        {{ block.targetUrl }}

        {% case 'internalSlug' %}
        {{ block.targetSlug }}

        {% case 'fileLink' %}
        {{ block.targetFile.one().getUrl() }}
    {% endswitch %}
 {%- endmacro -%}



{# --------------------------------------
    Start to now date comparision eg. copyright years
--------------------------------------- #}

{% macro yearTillCurrent(number) %}
    {%- if number == now|date('Y') -%}
        {{ number }}
    {%- else -%}
        {{ number }} - {{ now|date('Y') }}
    {%- endif -%}
{% endmacro %}



{# --------------------------------------
    Section header
--------------------------------------- #}

{% macro sectionHeader(header, modifierClass = null, headingLevel = "h2") %}
{% set cacheKey = "sectionHeader:#{header.id}:#{header.dateUpdated|date('c')}" %}
{% cache globally using key cacheKey for 3 hours %}
<div class="section-header {{ modifierClass ?? null }}">
    {%- if header.itemTitle -%}
        {% tag headingLevel with { class: 'section-header__title' } %}{{ header.itemTitle }}{% endtag %}
    {%- endif -%}
    {%- if header.subtitle -%}
        <p class="section-header__subtitle">{{ header.subtitle }}</p>
    {%- endif -%}
    {%- if header.richtextBasicLinks -%}
        <div class="section-header__tagline">{{ header.richtextBasicLinks }}</div>
    {%- endif -%}
    {% set headerLinks = header.linkHelper.all() ?? null %}
    {%- if headerLinks -%}
        <p class="section-header__cta">
            {% include './01_core/_blocks/link-helper.twig' with { linkHelperEntries: headerLinks, 'modifierClass' : 'button' } %}
        </p>
    {%- endif -%}
</div>
{% endcache %}
{% endmacro %}



{# --------------------------------------
    Section Footer
--------------------------------------- #}

{% macro sectionFooter(footer) %}
{% set cacheKey = "sectionFooter:#{footer.id}:#{footer.dateUpdated|date('c')}" %}
{% cache globally using key cacheKey for 3 hours %}
<div class="section-footer">
    {%- if footer.notes ?? false -%}
        <p class="section-footer__notes">{{ footer.notes ?? null }}</p>
    {%- endif -%}
    {% set footerLinks = footer.linkHelper.all() ?? null %}
    {%- if footerLinks ?? false -%}
        <p class="section-footer__cta">
            {% include './01_core/_blocks/link-helper.twig' with { linkHelperEntries: footerLinks, 'modifierClass' : 'button' } %}
        </p>
    {%- endif -%}
</div>
{% endcache %}
{% endmacro %}



{# --------------------------------------
    On-this-page anchor link support
--------------------------------------- #}

 {% macro selfAnchorSupport(copy) %}
    {{ copy|replace("/href=\"#([^\"]+)\"/", "href=\"#{craft.app.request.url}#$1\"")|raw }}
 {% endmacro %}



{# --------------------------------------
    Sentence Grammar helper
    eg. W, X, Y & Z
--------------------------------------- #}

{% macro sentenceGrammar(items, itemVar) %}
    {% set items = items|filter(item => item is not empty) %}

    {% set sentence = null %}

    {% for item in items %}

        {%- if loop.first -%}
            {% set sentence = item[itemVar] %}
        {%- elseif loop.last -%}
            {% set sentence = sentence ~ ' & ' ~ item[itemVar] %}
        {%- else -%}
            {% set sentence = sentence ~ ', ' ~ item[itemVar] %}
        {%- endif -%}
    {% endfor %}

    {{ sentence|raw }}

{% endmacro %}
