{# --------------------------------------
Basic textbox
--------------------------------------- #}

{% macro fieldText(params) %}

{% set params = ({
    type: 'text',
    name: '',
    label: params.name ?? '',
    hiddenLabel: false,
    required: false,
    class: '',
    error: false,
    errorItems: [],
    value: '',
    placeholder: '',
    fieldAttributes: {},
    inputAttributes: {},
})|merge(params) %}

{% set codeName = params.name|lower|replace({' ': '_'}) %}

<div class="field--text {{ params.class }}{{ params.hiddenLabel ? ' field--hidden-label' : '' }}{{ params.error ? ' field--error' : '' }}" {{ attr(params.fieldAttributes) }}>
    <div class="field-label">
        <label for="{{ codeName }}">
            {{ params.label ? params.label : params.name }}
            {{ params.required ? '<span class="field-label__required">required</span>' : '' }}
        </label>
    </div>
    <div class="field-input">
        <input id="{{ codeName }}" class="textbox" type="{{ params.type }}" name="{{ codeName }}" value="{{ params.value }}" placeholder="{{ params.placeholder }}" {{ params.required ? 'required' : '' }} >
    </div>
    {% if params.error %}
        <ul class="field-error__list">
            {% for label in params.errorItems %}
            <li class="field-error__list__item">{{ label }}</li>
            {% endfor %}
        </ul>
    {% endif %}
</div>
{% endmacro %}





{# --------------------------------------
Basic Select
Useage eg.
{{ macros.fieldSelect({
    name: 'Business Type',
    options: [
        { value: 1, label: "Rando single thing if needed" },
        { optgroup: 'Cabins and Villas' },
        { value: 2, label: "3 Bedroom Deluxe Retreat" },
        { value: 3, label: "3 Bedroom Retreat" },
        { optgroup: 'Sites & Ensuites' },
        { value: 4, label: "Premier Ensuite Sites" },
    ]
}) }}
--------------------------------------- #}

{% macro fieldSelect(params) %}
    {% set params = ({
        options: [],
        name: '',
        label: params.name ?? '',
        hiddenLabel: false,
        required: false,
        class: '',
        value: '',
        error: false,
        errorItems: [],
        placeholder: 'Select an option',
        fieldAttributes: {},
        inputAttributes: {},
    })|merge(params) %}

    {% set codeName = params.name|lower|replace({' ': '_'}) %}

    {% set optgroup = false %}

    <div class="field--select {{ params.class }}{{ params.error ? ' field--error' : '' }}" {{ attr(params.fieldAttributes) }}>
        <div class="field-label {{ params.hiddenLabel ? ' -vis-hidden' : '' }}">
            <label for="{{ codeName }}">
                {{ params.label ? params.label : params.name }}
                {{ params.required ? '<span class="field-label__required">required</span>' : '' }}
            </label>
        </div>
        <div class="field-input">
            <select id="{{ codeName }}" class="dropdown" name="{{ codeName }}" {{ attr(params.inputAttributes) }} {{ params.required ? 'required' : '' }}>
                <option value="" class="dropdown-top">{{ params.placeholder }}</option>
                {% for key, label in params.options %}
                    {% if label.label is defined %}
                        {% set key = label.value %}
                        {% set label = label.label %}
                    {% endif %}
                    {% if label.optgroup is defined %}
                        {% if optgroup %}
                            </optgroup>
                        {% else %}
                            {% set optgroup = true %}
                        {% endif %}
                        <optgroup label="{{ label.optgroup }}">
                    {% else %}
                        <option value="{{ key }}" {% if params.value == key %}selected{% endif %}>{{ label }}</option>
                    {% endif %}
                {% endfor %}
                {% if optgroup %}
                    </optgroup>
                {% endif %}
            </select>
        </div>
        {% if params.error %}
            <ul class="field-error__list">
                {% for label in params.errorItems %}
                <li class="field-error__list__item">{{ label }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
{% endmacro %}



{# --------------------------------------
Basic Textarea
--------------------------------------- #}

{% macro fieldMultiline(params) %}
    {% set params = ({
        name: '',
        label: params.name ?? '',
        hiddenLabel: false,
        required: false,
        class: '',
        value: '',
        rows: '5',
        error: false,
        errorItems: [],
        placeholder: 'Select an option',
        fieldAttributes: {},
        inputAttributes: {},
    })|merge(params) %}

    {% set codeName = params.name|lower|replace({' ': '_'}) %}

    <div class="field-element field-element--text {{ params.class }}{{ params.hiddenLabel ? ' field-element--hidden-label' : '' }}{{ params.error ? ' field-element--error' : '' }}" {{ attr(params.fieldAttributes) }}>
        <div class="field-label">
            <label for="{{ codeName }}">
                {{ params.label ? params.label : params.name }}
                {{ params.required ? '<span class="field-label__required">required</span>' : '' }}
            </label>
        </div>
        <div class="field-input">
            <textarea rows="{{ params.rows }}" id="{{ codeName }}" class="textbox" name="{{ codeName }}" {{ params.required ? 'required' : '' }}>{{ params.value }}</textarea>
        </div>
        {% if params.error %}
            <ul class="field-error__list">
                {% for label in params.errorItems %}
                <li class="field-error__list__item">{{ label }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>

{% endmacro %}


{# --------------------------------------
Basic Date Range picker
probably needs js support because of bad safari support
--------------------------------------- #}

{% macro fieldDateRange(params) %}
    {% set params = ({
        nameStart: 'date-start',
        nameEnd: 'date-end',
        legend: 'Between dates',
        labelStart: params.nameStart ?? '',
        labelEnd: params.nameEnd ?? '',
        hiddenLabel: true,
        required: false,
        class: '',
        error: false,
        errorItems: [],
        valueStart: '',
        valueEnd: '',
        placeholder: '',
        placeholderStart: params.placeholder ?? '',
        placeholderEnd: params.placeholder ?? '',
        fieldAttributes: {},
        inputAttributes: {},
    })|merge(params) %}

    {% set codeNameStart = params.nameStart|lower|replace({' ': '_'}) %}
    {% set codeNameEnd = params.nameEnd|lower|replace({' ': '_'}) %}

    <fieldset class="field-element field-element--date-range {{ params.class }}{{ params.hiddenLabel ? ' field-element--hidden-setlabels' : '' }}{{ params.error ? ' field-element--error' : '' }}" {{ attr(params.fieldAttributes) }}>
        <div class="fieldset-content">
            <legend class="fieldset__legend">
                {{ params.legend }}
                {{ params.required ? '<span class="field-label__required">required</span>' : '' }}
            </legend>

            <div class="row">
                <div class="col-xs-6">
                    <div class="field-label">
                        <label for="{{ codeNameStart }}">{{ params.labelStart ? params.labelStart : params.nameStart }}</label>
                    </div>
                    <div class="field-input">
                        <input id="{{ codeNameStart }}" class="textbox" type="date" name="{{ codeNameStart }}" value="{{ params.valueStart }}" placeholder="dd/mm/yyyy" {{ params.required ? 'required' : '' }}>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="field-label">
                        <label for="{{ codeNameEnd }}">{{ params.labelEnd ? params.labelEnd : params.nameEnd }}</label>
                    </div>
                    <div class="field-input">
                        <input id="{{ codeNameEnd }}" class="textbox" type="date" name="{{ codeNameEnd }}" value="{{ params.valueEnd }}" placeholder="dd/mm/yyyy" {{ params.required ? 'required' : '' }}>
                    </div>
                </div>
            </div>

        {% if params.error %}
            <ul class="field-error__list">
                {% for label in params.errorItems %}
                <li class="field-error__list__item">{{ label }}</li>
                {% endfor %}
            </ul>
        {% endif %}

        </div>
    </fieldset>

{% endmacro %}




{# --------------------------------------
Multiple checkboxes
--------------------------------------- #}

{% macro fieldCheckboxes(params) %}

{% set params = ({
    name: '',
    label: params.name ?? '',
    hiddenLabel: false,
    required: false,
    prefix: '',
    class: '',
    error: false,
    errorItems: [],
    value: [],
    fieldAttributes: {},
    inputAttributes: {},
})|merge(params) %}

{% set codeName = params.name|lower|replace({' ': '_'}) %}

<div class="field-element field-element--checkboxset {{ params.class }}{{ params.hiddenLabel ? ' field-element--hidden-label' : '' }}{{ params.error ? ' field-element--error' : '' }}" {{ attr(params.fieldAttributes) }}>
    <fieldset class="fieldset--checkboxset">
        <legend class="fieldset__legend">
            {{ params.label ? params.label : params.name }}
            {{ params.required ? '<span class="field-label__required">required</span>' : '' }}
        </legend>
        <div class="field-element__input-set">
            {% for key, label in params.options %}
            <div class="fieldset-input">
                <input type="checkbox" id="{{ params.prefix }}{{ codeName }}-{{ key }}" value="{{ key }}" {% if key in params.value %}checked{% endif %} {{ params.required ? 'required' : '' }}>
                <label for="{{ params.prefix }}{{ codeName }}-{{ key }}">{{ label }}</label>
            </div>
            {% endfor %}
        </div>
    </fieldset>
    {% if params.error %}
        <ul class="field-error__list">
            {% for label in params.errorItems %}
            <li class="field-error__list__item">{{ label }}</li>
            {% endfor %}
        </ul>
    {% endif %}
</div>

{% endmacro %}



{# --------------------------------------
Basic Upload field
--------------------------------------- #}

{% macro fieldFileUpload(params) %}

{% set params = ({
    name: '',
    label: params.name ?? '',
    hiddenLabel: false,
    required: false,
    class: '',
    error: false,
    errorItems: [],
    value: '',
    placeholder: '',
    fieldAttributes: {},
    inputAttributes: {},
})|merge(params) %}

{% set codeName = params.name|lower|replace({' ': '_'}) %}

<div class="field-element field-element--upload {{ params.class }}{{ params.hiddenLabel ? ' field-element--hidden-label' : '' }}{{ params.error ? ' field-element--error' : '' }}" {{ attr(params.fieldAttributes) }}>
    <div class="field-label">
        <label for="{{ codeName }}">
            {{ params.label ? params.label : params.name }}
            {{ params.required ? '<span class="field-label__required">required</span>' : '' }}
            <div class="field-label__click-area">
                <span class="field-label__click-area__status">
                    No file selected
                </span>

            </div>
        </label>
    </div>
    <div class="field-input">
        <input id="{{ codeName }}" type="file" name="{{ codeName }}" value="{{ params.value }}" {{ params.required ? 'required' : '' }}>
    </div>
    <span class="field-clear-btn">clear</span>
    {% if params.error %}
        <ul class="field-error__list">
            {% for label in params.errorItems %}
            <li class="field-error__list__item">{{ label }}</li>
            {% endfor %}
        </ul>
    {% endif %}
</div>

{% js %}
// form upload
// reproduce the filename on the pretty upload overlay
$('body').on('change', '.field-element--upload input[type="file"]', function(e) {
  var fileName = e.target.files[0].name;
  console.log(fileName);
  $(this).parents(".field-element--upload").find('.field-label__click-area__status').replaceWith( '<span class="field-label__click-area__status">' + fileName + '</span>' );
  $(this).parents(".field-element--upload").addClass('field-element--upload-has-val');

});
// clear the upload field
$('body').on('click', '.field-element--upload .field-clear-btn', function(e) {
  console.log('remove');
  $(this).parents(".field-element--upload").removeClass('field-element--upload-has-val');
  $(this).parents(".field-element--upload").find('input[type="file"]').val('');
  $(this).parents(".field-element--upload").find('.field-label__click-area__status').replaceWith( '<span class="field-label__click-area__status"> No file selected</span>' );
});
{% endjs %}

{% endmacro %}

