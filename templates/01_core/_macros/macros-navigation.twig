
{# --------------------------------------
    Navigation
--------------------------------------- #}

{% macro is_ancestor(segment) %}
    {%- if segment in craft.app.request.segments and craft.app.request.segments|last != segment -%}
    current-ancestor
    {%- endif -%}
{% endmacro %}

{% macro is_active(segment) %}
    {%- if craft.app.request.getFullUri() == segment or craft.app.request.segments|last == segment -%}
    current-item
    {%- endif -%}
{% endmacro %}

{% macro slug_to_title(segment) %}
{% apply spaceless %}
    {{ segment|replace({ '-':' '})|title }}
{% endapply %}
{% endmacro %}

