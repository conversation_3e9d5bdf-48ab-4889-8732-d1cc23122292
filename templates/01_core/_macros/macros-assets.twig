{# --------------------------------------
Return filesize in Kb
--------------------------------------- #}

{%- macro fileSizeInKiloBytes(assetSize) -%}
    {%- set bytes = assetSize -%}
    {%- set kiloBytes = (bytes / 1024)|round(2) -%}
    {{- kiloBytes }} Kb
{%- endmacro -%}


{# --------------------------------------
Return filesize in Mb
--------------------------------------- #}

 {%- macro fileSizeInMegaBytes(assetSize) -%}
    {%- set bytes = assetSize -%}
    {%- set kiloBytes = (bytes / 1024)|round(2) -%}
    {%- set megaBytes = (kiloBytes / 1024)|round(2) -%}
    {{- megaBytes -}} Mb
 {%- endmacro -%}


{# --------------------------------------
Return an id for a fallback image
    This macro will look to see if the user has set a list of images to pick from under the Site Identity global
    otherwise it will randomly select
        an image file, that's either a jpg or a jpeg
        from the images volume
        that is wider than 1600px
--------------------------------------- #}

{%- macro randomFallbackImageId() -%}
{% set imageIds = siteIdentity.imageList.all()|length > 0 ? siteIdentity.imageList.ids() : craft.assets.kind('image').volume('contentImages').filename(['*.jpg', '*.jpeg']).width('>= 1600').orderBy('RAND()').limit(1).ids() %}
{% set randomImageIndex = random() % imageIds|length %}
{% set randomImageId = imageIds[randomImageIndex] %}
{{ randomImageId }}
{%- endmacro -%}

{# --------------------------------------
    Output SVG code with width & height
    (because Adobe Illu doesn't) or a optimised Webp
    if a raster file is the only option.
--------------------------------------- #}

{%- macro vectorSwitcher( file, width, height, modifierClass = null ) -%}

    {% if file.filename ends with '.svg' %}
        {# vector #}
        {{ svg(file)|attr({
            width: width ?? file.width,
            height: height ?? file.height,
            class: modifierClass
        }) }}
    {% else %}
        {# rasterised  #}
        {{ file.getImg({ mode: 'fit', width: width, height: height, format: 'webp' }, ['1.5x', '2x', '3x'])|attr({
            class: modifierClass,
            loading: 'lazy',
            role: file.alt ? null : 'presentation'
        }) }}
    {% endif %}

{%- endmacro -%}
