{% extends '01_core/main' %}

{%- set bodyAttributes = { class: entry ?? false ? entry.type } -%}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
    {{ parent() }}
{% endblock %}

{% block banner %}{% endblock %}

{% block main_content %}

<main class="page-layout page-layout--skew section section-t0 bg-darkgrey">

     <div class="team-banner">

        <a class="button-back" href="javascript: history.go(-1)">
            Back
        </a>
    </div>

    <div class="mainbar bg-default">

        <div class="channel__content">

        {% include '@page-header' with { showBreadcrumb: false } %}

        {% block main_bar %}

        <div class="team-page__scroll-overflow">

        {% if entry.linkedin ?? false %}
            <p class="social-link">
                <a href="{{ entry.linkedin }}" target="_blank">
                <span class="icon">
                {{ svg('@webroot/assets/icon-system/icon_linkedin.svg') }}
                </span>
                Connect on LinkedIn
                </a>
            </p>
        {% endif %}
        {{ entry.richtextSansMedia ?? false }}

        </div>


        {% endblock %}

        </div>
        {% set profileImage = entry.profilePhoto.one() ?? fallbackImage %}
        {% if profileImage %}
        {% endif %}

        <div class="team__photo">
            {% set bannerRetina = profileImage.getUrl({ mode: 'crop', width: 1305, height: 1595, quality: 68 }) %}
            {% set bannerLg = profileImage.getUrl({ mode: 'crop', width: 800, height: 970, quality: 68 }) %}
            {% set bannerSm = profileImage.getUrl({ mode: 'crop', width: 600, height: 730, quality: 68 }) %}

            <picture class="channel-banner__image">
                <img
                    alt="Photo of {{ entry.title }}"
                    loading="lazy"
                    src="{{ bannerSm }}"
                    srcset="
                        {{ bannerSm }} 600w,
                        {{ bannerLg }} 800w,
                        {{ bannerRetina }} 2x"
                    width="480"
                    height="800"
                />
            </picture>

        </div>

    </div>

</main>

{% endblock %}

