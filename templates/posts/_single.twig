{% extends 'channels/channel.twig' %}

{% block css %}
    {{ parent() }}
{% endblock %}

{% block main_bar %}

    {% import 'posts/_includes/macros-blog' as blogMacros %}

    <article>

        <header class="post-single__introduction">
            {{ blogMacros.postDetails(entry, 'F jS Y', ) }}
        </header>

        {% if entry ?? false and entry.pageContent %}
            {% include '01_core/_layouts/page-content' %}
        {% endif %}

        <footer class="post-single__footer">

            {{ blogMacros.postCategories(entry) }}

            {{ blogMacros.postTags(entry) }}

        </footer>

    </article>

    {% include 'posts/_includes/post-pagination' %}

{% endblock %}


{% block bottom_bar %}

    {% include '02_components/share' with { shareTitle: 'Share this story, choose your platform!' }%}

    {% set relatedPostsList = craft.entries.section('posts').id('not ' ~ entry.id).orderBy('RAND()').limit(2).all() %}
    {% include 'posts/_includes/posts-list' with { postsList: relatedPostsList, postsListTitle: 'Related Posts' } %}

    {% set latestPostsList = craft.entries.section('posts').id('not ' ~ entry.id).orderBy('postDate DESC').limit(2).all() %}
    {% include 'posts/_includes/posts-list' with { postsList: latestPostsList, postsListTitle: 'Latest Posts' } %}

{% endblock %}


