{# --------------------------------------
    Post details
 --------------------------------------- #}

 {% macro postDetails(post, dateFormat, author) %}
 <p class="post-details">Posted {{ _self.postAuthor(post, author) }} {{ _self.postDate(post, dateFormat) }}</p>
 {% endmacro postDetails %}



{# --------------------------------------
    Post author
--------------------------------------- #}
{%- macro postAuthor(post, author) -%}
{% apply spaceless %}
{%- if author != 'none' -%}
<span class="post-detail post-detail--author">
    <span class="post-detail__label">by </span>
    {%- if author == 'image' -%}
        <img src="" alt="">
    {%- elseif author == 'icon' -%}
        {{ svg('@webroot/assets/icon-system/icon_edit-pencil-tiny.svg')|attr({ class: 'post-detail__icon' }) }}
    {%- endif -%}
    {{ post.attribution ?? ( post.author != 'admin' ? post.author : siteName ) }}
</span>
{%- endif -%}
{% endapply %}
{%- endmacro postAuthor -%}




{# --------------------------------------
    Post date published
--------------------------------------- #}
{%- macro postDate(post, dateFormat) -%}
{% apply spaceless %}
   <span class="post-detail post-detail--time">
       <span class="post-detail__label">on </span>
       {# svg('@webroot/assets/icon-system/icon_calendar-tiny.svg')|attr({ class: 'post-detail__icon' }) #}
       <time date-time="{{ post.postDate|date('Y-M-d') }}">{{ post.postDate|date(dateFormat) }}</time>
   </span>
{% endapply %}
{%- endmacro postDate -%}



{# --------------------------------------
    Post categories
--------------------------------------- #}

{% macro postCategories(post) %}
    {% if post.postCategory.all() ?? false %}
    <p class="post-single-categories">
        {{ svg('@webroot/assets/icon-system/icon_category-tiny.svg')|attr({ class: 'post-single-categories__icon' }) }}
        <span class="post-single-categories__label">Post Categories</span>
        {% for postCategory in post.postCategory.all() %}
            {% if postCategory.uri %}
                <a  class="post-single-categories__item badge" href="{{ postCategory.uri }}">{{ postCategory.title }}</a>
            {% else %}
                <span class="post-single-categories__item badge">{{ postCategory.title }}</span>
            {% endif %}
        {% endfor %}
    </p>
    {% endif %}
{% endmacro postCategories %}



{# --------------------------------------
    Post tags
--------------------------------------- #}

{% macro postTags(post) %}
    {% if post.postTags.all() ?? false %}
    <p class="post-single-tags">
        {{ svg('@webroot/assets/icon-system/icon_tag-tiny.svg')|attr({ class: 'post-single-tags__icon' }) }}
        <span class="post-single-tags__label">Post Tags</span>
        {% for tag in post.postTags.all() %}
            <span class="post-single-tags__item badge">
                {{ tag }}
            </span>
        {% endfor %}
    </p>
    {% endif %}
{% endmacro postTags %}



{# --------------------------------------
    Post hub page link
    Find the first instance of the post gallery page add-on
    Get the page it has been included on.

    this breaks very easily so I've removed it from the posts pagination
    the caching is particulary problematic but
    leaving here for reference just in case.
--------------------------------------- #}

{% macro postHubLink() %}
{% cache globally using key "post-hub-link" for 1 day %}
    {% set postGalleryAddon = craft.entries.section('pageAddons').type('postsGallery').one() %}
    {% set postsHubPageBlock = craft.matrixBlocks().field('pageContent').type('pageAddOn').relatedTo(postGalleryAddon).one() %}
    {{ postsHubPageBlock.owner.url() }}
{% endcache %}
{% endmacro postHubLink %}
