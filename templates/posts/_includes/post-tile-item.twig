<!-- Template: {{ _self }} -->

{% set tileImage = post.banner.one() ?? siteIdentity.defaultSocialMediaImage.one() ?? false %}

<a class="post-tile-item" href="{{ post.url }}">


    <picture class="post-tile-item__picture">
    {% if tileImage ?? false and tileImage|length > 1 %}
        {{ tileImage.getImg({ width: 600, height: 400, format: 'webp', }, ['1.5x', '2x', '3x'])|attr({
                loading: 'lazy',
                role: tileImage.alt ? null : 'presentation'
            })
        }}
    {% endif %}
    </picture>

    <div class="post-tile-item__copy">

        <h2 class="post-tile-item__title">
            {{ post.title|truncate(120) }}
        </h2>

        {% import 'posts/_includes/macros-blog' as blogMacros %}
        {{ blogMacros.postDetails(post, 'M jS, Y') }}

    </div>

</a>
