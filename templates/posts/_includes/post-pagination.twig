<!-- Template: {{ _self }} -->
{% import 'posts/_includes/macros-blog' as blogMacros %}
{% set postsParentPage = siteIdentity.postsParentPage.one() ?? null %}

<div class="post-pagination">
    {% set criteria = {section: 'posts', orderBy: 'postDate'} %}
    {% set prev = entry.getPrev(criteria) %}
    {% set next = entry.getNext(criteria) %}

    {% set uriArray = craft.app.request.segments %}

    <a href="{{ prev ? prev.url : null }}" class="post-pagination__older" {{ not prev ? 'disabled' }}>
        <span>Older</span>
    </a>

    {% if postsParentPage ?? false %}
    <a href="{{ postsParentPage.url }}" class="post-pagination__back">All posts</a>
    {% endif %}

    <a href="{{ next ? next.url : null }}" class="post-pagination__newer" {{ not next ? 'disabled' }}>
        <span>Newer</span>
    </a>
</div>
