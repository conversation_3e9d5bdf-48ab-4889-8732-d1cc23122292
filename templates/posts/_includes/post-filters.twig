<!-- Template: {{ _self }} -->

{% if relevantCategories|length %}
    <div class="js-post-grid-filterlist">
        <div class="field-element field-element--dropdown">
            <div class="field-input">
                <!-- J<PERSON> inserts a select menu here on mobile -->
            </div>
        </div>

        <ul class="post-grid-filterlist">
            <li class="post-grid-filterlist__item">
                <button class="button button--small js-post-grid-filter-cat" data-category="*">All</button>
            </li>
            {% for category in relevantCategories %}
                <li class="post-grid-filterlist__item">
                    <button class="button button--small js-post-grid-filter-cat" data-category="{{ category.title|kebab|lower }}">{{ category.title }}</button>
                </li>
            {% endfor %}
        </ul>
    </div>
{% endif %}
