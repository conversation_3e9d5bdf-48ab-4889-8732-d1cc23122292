<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-assets.twig" as assetMacros %}


{% set bannerImage = entry.banner.one() ?? null %}
    <div class="channel-banner {{ bannerImage|length ? ' ' : 'channel-banner--no-image ' }}{{ modifierClass ?? null }}">

        {% include '@button-back' %}

        {% if bannerImage %}
        <picture class="channel-banner__image">
            {{ bannerImage.getImg({ width: 800, height: 480, format: 'webp'}, ['1.5x', '2x', '3x'])|attr({
                class: '',
                loading: 'lazy',
                role: bannerImage.alt ? null : 'presentation'
            }) }}
        </picture>
        {% else %}
        <div class="channel-banner__image channel-banner__image--fallback">
        </div>
    {% endif %}

    </div>
