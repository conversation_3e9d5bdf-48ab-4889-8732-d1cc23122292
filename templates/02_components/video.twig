<!-- Template: {{ _self }} -->
{% set videoUrl = video.videoUrl ?? null %}
{% set videoFile = video.videoFile.one() ?? null %}

{% if videoUrl ?? false %}
    <div class="widget-video">
        {% include "@video" with {
            videoUrl: videoUrl,
            'video': {
                modifierClass: 'featured-item__video'
            }
        } %}
    </div>
{% elseif videoFile ?? false %}
    {% set preferences = video.videoPreferences %}
    <video {{ preferences|join(' ') }} playsinline width="100%" height="100%">
        <source src="{{ videoFile.getUrl() }}" type="video/mp4">
        Your browser does not support video. Visit <a href="http://browsehappy.com/">browsehappy.com</a> to help you select an upgrade.
    </video>
{% endif %}
