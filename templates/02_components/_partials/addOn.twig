<!-- Template: {{ _self }} -->
{% set panelWidth = panelWidth is defined ? panelWidth : (block.panelWidth.value ?? 'container-width') %}
{% set backgroundColour = backgroundColour is defined ? backgroundColour : (block.backgroundColour ?? false ? 'bg-' ~ block.backgroundColour.value) %}
{% set isBleed = (entry is defined and entry.enableSidebar == true) ? false : isBleed is defined ? isBleed : backgroundColour not in ['', 'bg-default'] ? true : false %}
{% set addOnType = addOnType is defined ? addOnType|split('/')|last|split('@')|last|split('.')|first|cc2kc : null %}

<div class="
    {{ addOnType ?? null }}
    {{ modifierClass ?? null }}
    {{ panelWidth ?? null }}
    {{ backgroundColour ?? null }}
    {{ isBleed ? ' background--bleed section-small' }}
">
    {% set sectionHeader = addOnEntry.sectionHeader.one() ?? null %}
    {% if sectionHeader %}
        {% import "01_core/_macros/macros-global.twig" as globalMacros %}
        {% if modifierClass is defined and modifierClass == 'sidebar-widget' %}
            {{ globalMacros.sectionHeader(sectionHeader, 'section-header--sidebar', 'h3') }}
        {% else %}
            {{ globalMacros.sectionHeader(sectionHeader) }}
        {% endif %}
    {% endif %}

    {% block addOnContent %}{% endblock %}

    {% set sectionFooter = addOnEntry.sectionFooter.one() ?? null %}
    {% if sectionFooter ?? false %}
        {% import "01_core/_macros/macros-global.twig" as globalMacros %}
        {{ globalMacros.sectionFooter(sectionFooter) }}
    {% endif %}

</div>
