<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-global.twig" as globalMacros %}

{% set cardMatrix = addOnEntry.cardHelper %}
{% set listLayout = listLayout is defined ? listLayout : 'tidy-list-grid' %}

{% if cardMatrix %}

    {% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
    {% block addOnContent %}

        <div class="{{ listLayout }}">

            {% for item in cardMatrix %}
            <div class="{{ listLayout }}__item">
                {% include '@card' with {
                    cardEntry: item,
                    'card': {
                        modifierClass: 'card--promo-set',
                    }
                } %}
            </div>
            {% endfor %}

        </div>

    {% endblock %}
    {% endembed %}

{% endif %}
