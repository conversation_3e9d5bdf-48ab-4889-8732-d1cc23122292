<!-- Template: {{ _self }} -->
{% if addOnEntry.showContactDetails ?? false %}
    {% set showDetails = addOnEntry.showContactDetails %}
{% endif %}

{% set showDetails = showDetails ?? ['address', 'phoneNumber', 'emailAddress', 'enquiry'] %}

{% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
{% block addOnContent %}

    {% import "01_core/_macros/macros-global.twig" as macros %}

    <address class="contact-details__list">

        {% if 'address' in showDetails and siteIdentity.address ?? false %}
            <p class="contact-details__item {{ 'address'|cc2kc }}">
                <strong class="contact-details__item__title">Address</strong>
                <br>{% tag siteIdentity.googleMapLink ?? false ? 'a' : null with { href: siteIdentity.googleMapLink } %}{{ siteIdentity.address|nl2br|raw }}{% endtag %}
            </p>
        {% endif %}

        {% if 'postalAddress' in showDetails and siteIdentity.postalAddress ?? false %}
            <p class="contact-details__item {{ 'postalAddress'|cc2kc }}">
                <strong class="contact-details__item__title">Address</strong>
                <br>{{ siteIdentity.postalAddress|nl2br|raw }}
            </p>
        {% endif %}

        {% if 'phoneNumber' in showDetails and siteIdentity.phoneNumber ?? false %}
            <p class="contact-details__item {{ 'phoneNumber'|cc2kc }}">
                <strong class="contact-details__item__title">Phone</strong>
                <a href="{{ siteIdentity.phoneNumber }}">{{ macros.phoneFormat(siteIdentity.phoneNumber) }}</a>
            </p>
        {% endif %}

        {% if 'alternativePhone' in showDetails and siteIdentity.alternativePhone ?? false %}
            <p class="contact-details__item {{ 'alternativePhone'|cc2kc }}">
                <strong class="contact-details__item__title">Alternative Phone</strong>
                <a href="{{ siteIdentity.alternativePhone }}">{{ macros.phoneFormat(siteIdentity.alternativePhone) }}</a>
            </p>
        {% endif %}

        {% if 'emailAddress' in showDetails and siteIdentity.emailAddress ?? false %}
            <p class="contact-details__item {{ 'emailAddress'|cc2kc }}">
                <strong class="contact-details__item__title">Email</strong>
                <a href="mailto:{{ siteIdentity.emailAddress }}">Send us an email</a>
            </p>
        {% endif %}

        {% if 'enquiry' in showDetails %}
            <p class="contact-details__item {{ 'enquiry'|cc2kc }}">
                <strong class="contact-details__item__title">Online Enquiry</strong>
                <a href="/contact">Send us a message</a>
            </p>
        {% endif %}

        {% if 'operatingHours' in showDetails and siteIdentity.operatingHours.one() ?? false %}
            <div class="contact-details__item {{ 'operatingHours'|cc2kc }}">
                <strong class="contact-details__item__title">Trading Hours</strong>
                {% for block in siteIdentity.operatingHours.all() %}
                    {% switch block.type %}
                        {% case 'operatingHoursItem' %}
                        <p class="opening-hours__item">
                            <span class="opening-hours__title">{{ block.operatingHoursTimeframe }}</span>
                            <span class="opening-hours__hours">{{ block.operatingHoursOpening|time('short') }} to {{ block.operatingHoursClosing|time('short') }}</span>
                        </p>

                        {% default %}
                            <p class="opening-hours__note">{{ block.operatingHoursNote }}</p>
                    {% endswitch %}
                {% endfor %}
            </div>
        {% endif %}

    </address>

{% endblock %}
{% endembed %}
