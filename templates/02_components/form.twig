<!-- Template: {{ _self }} -->

{% if selectedForm is not defined %}
{% set selectedForm = block.formie ? block.formie.one() : null %}
{% endif %}

{{ craft.formie.renderForm(selectedForm, {
    themeConfig: {
        formWrapper: {
            resetClass: true,
            attributes: {
                class: ['form-wrapper', formComponent.modifierClass ?? null]
            },
        },
        form: {
            resetClass: true,
            attributes: {
                class: 'form'
            },
        },
        formContainer: {
            resetClass: true,
            attributes: {
                class: 'form-container'
            },
        },
        alertError: {
            resetClass: true,
            attributes: {
                class: 'form-alert-error'
            },
        },
        alertSuccess: {
            resetClass: true,
            attributes: {
                class: 'form-alert-success'
            },
        },
        errors: {
            resetClass: true,
            attributes: {
                class: 'form-errors'
            },
        },
        error: {
            resetClass: true,
            attributes: {
                class: 'form-error'
            },
        },
        formTitle: {
            resetClass: true,
            attributes: {
                class: 'form-title'
            },
        },

        page: {
            resetClass: true,
            attributes: {
                class: ['form-page', styleCapsule ?? false ? 'capsule-form-fields']
            },
        },
        pageContainer: {
            resetClass: true,
            attributes: {
                class: 'form-page-container'
            },
        },
        pageTitle: {
            resetClass: true,
            attributes: {
                class: 'form-page-title'
            },
        },
        row: {
            resetClass: true,
            attributes: {
                class: 'form-row'
            },
        },

        pageTabs: {
            resetClass: true,
            attributes: {
                class: 'form-page-tabs'
            },
        },
        pageTab: {
            resetClass: true,
            attributes: {
                class: 'form-page-tab'
            },
        },
        pageTabLink: {
            resetClass: true,
            attributes: {
                class: 'form-page-tab-link'
            },
        },

        progressWrapper: {
            resetClass: true,
            attributes: {
                class: 'form-progress-wrapper'
            },
        },
        progress: {
            resetClass: true,
            attributes: {
                class: 'form-progress'
            },
        },
        progressContainer: {
            resetClass: true,
            attributes: {
                class: 'form-progress-container'
            },
        },
        progressValue: {
            resetClass: true,
            attributes: {
                class: 'form-progress-value'
            },
        },
        Buttons: {
            resetClass: true,
            attributes: {
                class: 'form-buttons'
            },
        },
        buttonWrapper: {
            resetClass: true,
            attributes: {
                class: 'form-button-wrapper'
            },
        },
        submitButton: {
            resetClass: true,
            attributes: {
                class: 'form-submit-button'
            },
        },
        backButton: {
            resetClass: true,
            attributes: {
                class: 'form-back-button'
            },
        },
        saveButton: {
            resetClass: true,
            attributes: {
                class: 'form-save-button'
            },
        },

        field: {
            resetClass: true,
            attributes: {
                class: 'field'
            },
        },
        fieldContainer: {
            resetClass: true,
            attributes: {
                class: 'field-container'
            },
        },
        fieldLabel: {
            resetClass: true,
            attributes: {
                class: 'field-label'
            },
        },
        fieldRequired: {
            resetClass: true,
            attributes: {
                class: 'field-required'
            },
        },
        fieldOptional: {
            resetClass: true,
            attributes: {
                class: 'field-optional'
            },
        },
        fieldInstructions: {
            resetClass: true,
            attributes: {
                class: 'field-instructions'
            },
        },
        fieldInputWrapper: {
            resetClass: true,
            attributes: {
                class: 'field-input-wrapper'
            },
        },
        fieldInput: {
            resetClass: true,
            attributes: {
                class: 'field-input'
            },
        },
        fieldErrors: {
            resetClass: true,
            attributes: {
                class: 'field-errors'
            },
        },
        fieldError: {
            resetClass: true,
            attributes: {
                class: 'field-error'
            },
        },

        subFieldRows: {
            resetClass: true,
            attributes: {
                class: 'sub-field-rows'
            },
        },
        subFieldRow: {
            resetClass: true,
            attributes: {
                class: 'sub-field-row'
            },
        },

        fieldOption: {
            resetClass: true,
            attributes: {
                class: 'field-option'
            },
        },
        fieldOptionLabel: {
            resetClass: true,
            attributes: {
                class: 'field-option-label'
            },
        },

        fieldOptions: {
            resetClass: true,
            attributes: {
                class: 'field-options'
            },
        },
        fieldOption: {
            resetClass: true,
            attributes: {
                class: 'field-option'
            },
        },
        fieldOptionLabel: {
            resetClass: true,
            attributes: {
                class: 'field-option-label'
            },
        },

        subFieldRows: {
            resetClass: true,
            attributes: {
                class: 'sub-field-rows'
            },
        },
        subFieldRow: {
            resetClass: true,
            attributes: {
                class: 'sub-field-row'
            },
        },

        fieldSummary: {
            resetClass: true,
            attributes: {
                class: 'field-summary'
            },
        },
        fieldSummaryContainer: {
            resetClass: true,
            attributes: {
                class: 'field-summary-container'
            },
        },
        fieldSummaryItem: {
            resetClass: true,
            attributes: {
                class: 'field-summary-item'
            },
        },

        nestedFieldRows: {
            resetClass: true,
            attributes: {
                class: 'nested-field-rows'
            },
        },
        nestedFieldRow: {
            resetClass: true,
            attributes: {
                class: 'nested-field-row'
            },
        },
        nestedFieldContainer: {
            resetClass: true,
            attributes: {
                class: 'nested-field-container'
            },
        },

        fieldHeading: {
            resetClass: true,
            attributes: {
                class: 'field-heading'
            },
        },

        fieldLimit: {
            resetClass: true,
            attributes: {
                class: 'field-limit'
            },
        },

        subFieldRows: {
            resetClass: true,
            attributes: {
                class: 'sub-field-rows'
            },
        },
        subFieldRow: {
            resetClass: true,
            attributes: {
                class: 'sub-field-row'
            },
        },

        fieldOptions: {
            resetClass: true,
            attributes: {
                class: 'field-options'
            },
        },
        fieldOption: {
            resetClass: true,
            attributes: {
                class: 'field-option'
            },
        },
        fieldOptionLabel: {
            resetClass: true,
            attributes: {
                class: 'field-option-label'
            },
        },

        fieldSection: {
            resetClass: true,
            attributes: {
                class: 'field-section'
            },
        },

        fieldLimit: {
            resetClass: true,
            attributes: {
                class: 'field-limit'
            },
        },

        fieldSummaryBlocks: {
            resetClass: true,
            attributes: {
                class: 'field-summary-blocks'
            },
        },
        fieldSummaryBlock: {
            resetClass: true,
            attributes: {
                class: 'field-summary-block'
            },
        },
        fieldSummaryHeading: {
            resetClass: true,
            attributes: {
                class: 'field-summary-heading'
            },
        },
    },
}) }}
