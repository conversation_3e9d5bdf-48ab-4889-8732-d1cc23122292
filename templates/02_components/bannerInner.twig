<!-- Template: {{ _self }} -->

{% import "01_core/_macros/macros-navigation" as navMacros %}


{% if bannerImage is not defined and entry ?? false and entry.banner.one() ?? false %}
    {% set bannerImage = entry.banner.one() %}
{% endif %}


{% if pageSubtitle is not defined and entry ?? false and entry.subtitle ?? false %}
    {% set pageSubtitle = entry.subtitle %}
{% endif %}


{% set showBreadcrumb = showBreadcrumb is defined ? showBreadcrumb : true %}


{% if bannerImage is defined and bannerImage|length %}
    <div class="inner-banner {{ modifierClass ?? null }}">

        {% set bannerXxlg = bannerImage.getUrl({ mode: 'crop', width: 2400, height: 720, quality: 85 }) %}
        {% set bannerXlg = bannerImage.getUrl({ mode: 'crop', width: 1920, height: 720, quality: 85 }) %}
        {% set bannerLg = bannerImage.getUrl({ mode: 'crop', width: 1600, height: 720, quality: 85 }) %}
        {% set bannerMd = bannerImage.getUrl({ mode: 'crop', width: 1200, height: 540, quality: 85 }) %}
        {% set bannerSm = bannerImage.getUrl({ mode: 'crop', width: 768, height: 768, quality: 85 }) %}
        {% set bannerXsm = bannerImage.getUrl({ mode: 'crop', width: 560, height: 560, quality: 85 }) %}

        <picture class="inner-banner__img">
            <source srcset="{{ bannerXxlg }}" media="(min-width: 1920px)">
            <source srcset="{{ bannerXlg }}" media="(min-width: 1600px)">
            <source srcset="{{ bannerLg }}" media="(min-width: 1200px)">
            <source srcset="{{ bannerMd }}" media="(min-width: 768px)">
            <source srcset="{{ bannerSm }}" media="(min-width: 560px)">
            <img src="{{ bannerXsm }}" alt="{{ bannerImage.alt ?? null }}" role="presentation">
        </picture>

        <div class="inner-banner__content">
            {% include '@page-header' %}
        </div>

    </div>
{% endif %}

