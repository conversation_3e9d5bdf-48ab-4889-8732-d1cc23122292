<!-- Template: {{ _self }} -->
{% js "/js/posts.js" %}
{% set postsParentPage = siteIdentity.postsParentPage.one() %}


{% if not postEntries ?? false %}
    {% set catFilter = specifiedCategory ?? addOn.postCategory ?? null %}
    {% set postsLimit = addOn.limit ?? null %}
    {% set postEntries = craft.entries.section('posts').postCategory(catFilter|length > 0 ? catFilter : null).navigationVisibility('pageGalleries').orderBy("postDate DESC").limit(postsLimit).all() %}
{% endif %}

{% set relevantCategories = catFilter|length > 0 ? catFilter : craft.entries.section('categories').type('postCategory').relatedTo(postEntries) %}

{% if postEntries %}
{% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
{% block addOnContent %}

    {% if addOnEntry.showHide ?? false %}
        {% include "posts/_includes/post-filters" %}
    {% endif %}

    <div class="post-grid js--load-on-scroll">
        {% for entry in postEntries %}
            {% include '@post-grid-item' %}
        {% endfor %}
    </div>

{% endblock %}
{% endembed %}
{% endif %}
