<!-- Template: {{ _self }} -->

<div class="section section--article bg-default">
    <div class="container">
        <article class="media-article">
            <img src="{{ entry.imageSingular.one().getUrl({ mode: 'crop', width: 800, height: 600 }) }}" alt="{{ entry.title }}" loading="lazy" />
            <div class="media-article__content">
                <p>{{ entry.subtitle }}</p>
                <h2>{{ entry.itemTitle }}</h2>
                {{ entry.richtext }}
                {% if entry.linkHelper|length %}
                <p>{% include './01_core/_blocks/link-helper.twig' with {
                    'linkHelper': {
                        modifierClass: 'button'
                    }
                } %}</p>
                {% endif %}
            </div>
        </article>
    </div>
</div>

