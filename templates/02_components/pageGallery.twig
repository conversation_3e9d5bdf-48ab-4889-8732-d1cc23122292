<!-- Template: {{ _self }} -->

{# Set the intended type of the page gallery #}
{% set contentSource = addOnEntry is defined ? addOnEntry.contentSource.value %}

{# Define the parent page, if it's not passed into the partial #}
{% set parentPage = not parentPage is defined ? null %}
{# Set the intended parent page #}
{% if not (parentPage ?? false) %}
    {% if addOnEntry.parentPage.one() ?? false and contentSource == 'customSetPageChildren' %}
        {# Parent page been intentionally set #}
        {% set parentPage = addOnEntry.parentPage.one() %}
    {% elseif entry ?? false %}
        {% set children = entry.getChildren() %}
        {# contentSource == 'currentPageChildren' #}
        {% set parentPage = entry %}
    {% endif %}
{% endif %}

{% set additionalPages = addOnEntry.pagesGalleryAdditionalItems.all() %}

{% if (craft.app.config.general.devMode or currentUser) and (contentSource == 'customSetPageChildren' and not addOnEntry.parentPage.one() ?? false) %}
<div class="site-messages">
    <p class="site-messages__item warning">
        <span class="notification--text">You've specified you'd like to set a custom parent page but haven't picked a specific page.
Please set a parent page in admin.</span>
    </p>
</div>
{% endif %}

{% if parentPage or additionalPages ?? false %}

    {% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
    {% block addOnContent %}

        {% macro pageItem(pageItem) %}
            <li class="children-gallery-list__item">
                {% set title = pageItem.navigationTitle ?? pageItem.title %}
                <a href="{{ pageItem.url }}" class="children-gallery-list__item__link">

                    {% set pageImage = pageItem.imageSingular and pageItem.imageSingular.one() ? pageItem.imageSingular.one() : ( pageItem.banner and pageItem.banner.one() ? pageItem.banner.one() : null ) %}

                    <picture class="children-gallery-list__item__picture">
                        {% if pageImage is defined and pageImage != null %}
                            {{ pageImage.getImg({ mode: 'crop', width: 1080, height: 810, format: 'webp' }, ['1.5x', '2x', '3x'])|attr({
                                class: "children-gallery-list__item__image",
                                loading: 'lazy',
                                role: pageImage.alt ? null : 'presentation'
                            }) }}
                        {% endif %}
                    </picture>

                    <p class="children-gallery-list__item__title">
                        {{ title }}
                    </p>
                </a>
            </li>
        {% endmacro %}

        <ul class="
            {{ block.pagesGalleryColumns ?? false ? 'fixed-grid fixed-grid--cols-' ~ block.pagesGalleryColumns : 'tidy-list-grid' }}
            ">

            {% if parentPage ?? false and contentSource != 'aLaCartePageSelection' %}
                {% for pageItem in parentPage.getChildren().navigationVisibility('pageGalleries').all() %}
                    {{ _self.pageItem(pageItem) }}
                {% endfor %}
            {% endif %}

            {% if additionalPages ?? false %}
                {% for pageItem in additionalPages %}
                    {{ _self.pageItem(pageItem) }}
                {% endfor %}
            {% endif %}

        </ul>

    {% endblock %}
    {% endembed %}

{% endif %}
