<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-assets.twig" as assetMacros %}

{% if mediaImages is not defined %}
{% set mediaImages = addOnEntry.imageList.all() %}
{% endif %}
{% set imagePreferences = addOnEntry.imageGalleryPreferences ?? null %}

{% set captions = captions is defined ? captions : imagePreferences.showCaptions %}
{% set enablePopup = enablePopup is defined ? enablePopup : imagePreferences.enablePopup ?? false %}
{% set imageRatio = imageRatio is defined ? imageRatio : imagePreferences.ratio %}
{% set columns = columns is defined ? columns : imagePreferences.columns %}
{% set slider = slider is defined ? slider : imagePreferences.displaySlider %}

{% if mediaImages ?? false %}
    {% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
    {% block addOnContent %}

        {% macro figure( image, displayCaptions, imageRatio, enablePopup, modifierClass ) %}
            {% tag enablePopup ? 'a' : null with {
                class: [
                    "popup-gallery",
                    modifierClass
                ],
                href: image.getUrl({ mode: 'fit', width: 1920, height: 1920, format: 'webp' }),
                target: '_blank',
                caption: displayCaptions == true ? [image.title, image.alt ?? false ? ' - ' ~ image.alt]|join(' ')
            } %}
            <figure class="image-gallery__figure {{ enablePopup ? null : modifierClass }}">

                {% set transformType = 'crop' %}
                {% set ratioWidth = null %}
                {% set ratioHeight = null %}

                {% switch imageRatio %}
                    {% case '4by3' %}
                        {% set ratioWidth = 768 %}
                        {% set ratioHeight = 576 %}
                    {% case '3by2' %}
                        {% set ratioWidth = 450 %}
                        {% set ratioHeight = 300 %}
                    {% case '16by9' %}
                        {% set ratioWidth = 450 %}
                        {% set ratioHeight = 253 %}
                    {% case '2by1' %}
                        {% set ratioWidth = 450 %}
                        {% set ratioHeight = 225 %}
                    {% case 'square' %}
                        {% set ratioWidth = 450 %}
                        {% set ratioHeight = 450 %}
                    {% default %}{# none #}
                        {% set transformType = 'fit' %}
                        {% set ratioWidth = 450 %}
                        {% set ratioHeight = 450 %}
                {% endswitch %}

                {{ image.getImg({ mode: transformType, width: ratioWidth, height: ratioHeight, format: 'webp', }, ['1.5x', '2x', '3x'])|attr({
                        class: "image-gallery__image",
                        loading: 'lazy',
                        role: image.alt ? null : 'presentation'
                    })
                }}

                {% if displayCaptions %}
                    <figcaption class="image-gallery__figure__caption">
                        {{ image.title }}
                    </figcaption>
                {% endif %}
            </figure>

            {% if enablePopup ?? null %}
                {{ svg('@webroot/assets/icon-system/icon_popup.svg')|attr({class: 'popup-gallery__icon' }) }}
            {% endif %}

            {% endtag %}
        {% endmacro %}


        {% if mediaImages|length > 1 %}
            <!-- List -->
            {% if slider %}
                <!-- Slider -->
                <splide-slider
                    class="image-gallery__slider columns-{{ columns }}"
                    data-splide-options='{{ {
                        type: columns == 1 ? "fade" : "loop",
                        gap: columns == 1 ? null : 16,
                        rewind: true,
                        perPage: columns,
                        autoplay: true,
                    }|filter|json_encode|e('html_attr') }}'
                >
                    {% include './02_components/_partials/custom-splide-arrows.twig' %}

                    <div class="image-gallery__slider__track splide__track">
                        <magnific-popup
                            class="image-gallery__slider__list splide__list"
                            data-selector=".popup-gallery:not(.splide__slide--clone)"
                            data-magnific-options='{
                                "type": "image",
                                "gallery": {
                                    "enabled": true
                                },
                                "image": {
                                    "tError": "<a href=\"%url%\">The image #%curr%</a> could not be loaded.",
                                    "titleSrc": "caption"
                                }
                            }'
                        >
                                {% for image in mediaImages %}
                                    {{ _self.figure( image, captions, imageRatio, enablePopup, 'splide__slide' ) }}
                                {% endfor %}
                        </magnific-popup>
                    </div>
                </splide-slider>
            {% else %}
                <!-- Grid -->
                <magnific-popup
                    class="image-gallery__grid columns-{{ columns }}"
                    data-selector=".popup-gallery"
                    data-magnific-options='{
                        "type": "image",
                        "gallery": {
                            "enabled": true
                        },
                        "image": {
                            "tError": "<a href=\"%url%\">The image #%curr%</a> could not be loaded.",
                            "titleSrc": "caption"
                        }
                    }'
                >
                    {% for image in mediaImages %}
                        {{ _self.figure( image, captions, imageRatio, enablePopup ) }}
                    {% endfor %}
                </magnific-popup>
            {% endif %}
        {% else %}
            <!-- Single Image -->
            <magnific-popup
                class="image-gallery__singular"
                data-selector=".popup-gallery"
                data-magnific-options='{
                    "type": "image",
                    "gallery": {
                        "enabled": true
                    },
                    "image": {
                        "tError": "<a href=\"%url%\">The image #%curr%</a> could not be loaded.",
                        "titleSrc": "caption"
                    }
                }'
            >
                {% for image in mediaImages %}

                    {{ _self.figure( image, captions, imageRatio, enablePopup, 'image-gallery__singular' ) }}
                {% endfor %}
            </magnific-popup>
        {% endif %}

    {% endblock %}
    {% endembed %}
{% endif %}


{% if mediaImages|length > 1 and slider %}
{% js "/dist/js/splide.min.js" at head %}
{% endif %}


{% if enablePopup %}
{# {% css "/css/magnific-popup.css" %} #}
{% js "/js/jquery.magnific-popup.min.js" at head %}
{% endif %}
