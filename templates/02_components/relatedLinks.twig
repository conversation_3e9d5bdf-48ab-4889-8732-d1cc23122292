<!-- Template: {{ _self }} -->

{% import "01_core/_macros/macros-navigation" as navMacros %}

{% if entry ?? false %}

    {% set section = entry.getSection() ?? null %}
    {% set ancestors = entry.getAncestors() %}
    {% set anchorPage = ancestors|length > 0 ? ancestors|first : entry %}
    {% set childrenPages = anchorPage.getDescendants()|filter(page => 'relatedLinks' in page['navigationVisibility']) %}

    {% if childrenPages|length > 0 %}

        {% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
        {% block addOnContent %}

            <h3 class="related-links__title">In this section</h3>

            <nav class="related-links__list">
                {% set listDepth = 1 %}
                <ul class="linklist depth{{ listDepth }}">
                    {% nav page in childrenPages %}
                        <li class="{{ navMacros.is_active( page.slug ) ?? null }}">
                            <a href="{{ page.url }}">{{ page.navigationTitle ?? page.title }}</a>
                            {% ifchildren %}
                                <ul class="depth{{ listDepth + 1 }}">
                                    {% children %}
                                </ul>
                            {% endifchildren %}
                        </li>
                    {% endnav %}
                </ul>
            </nav>

        {% endblock %}
        {% endembed %}

    {% endif %}

{% endif %}
