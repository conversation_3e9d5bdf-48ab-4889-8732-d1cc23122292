<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-global.twig" as globalMacros %}

<div class="
    {{ modifierClass ?? null }}
    content-block
    half-two-copy
    {{ block.backgroundColour ? 'bg-' ~ block.backgroundColour }}
    {{ block.backgroundColour and not sidebarEnabled ? ' background--bleed' }} {# only bleed the background of a panel, if the template doesn't have a sidebar #}
    {{ not sidebarEnabled ? 'container-width' }}" {# only let a user pick a width, if the template doesn't have a sidebar #}
>

{% if block.copy %}
    <div class="content-block__copy content-block__copy--1">
        {{ globalMacros.selfAnchorSupport( block.copy ) }}
    </div>
{% endif %}

{% if block.copy2 %}
    <div class="content-block__copy content-block__copy--2">
        {{ globalMacros.selfAnchorSupport( block.copy2 ) }}
    </div>
{% endif %}

</div>
