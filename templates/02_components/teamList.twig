<!-- boo Template: {{ _self }} -->

{# Set to all Team Members if none selected #}
{% if addOnEntry.teamMembers|length > 0 %}
    {% set teamList = addOnEntry.teamMembers.all() %}
{% else %}
    {% set teamList = craft.entries.section('teamMembers').orderBy('title ASC').all() %}
{% endif %}

{% if teamList|length > 0 %}

    {% embed "02_components/_partials/addOn.twig" with { addOnType: _self } %}
    {% block addOnContent %}

        <div class="team-members tidy-list-grid">

            {% for entry in teamList %}
            <div class="tidy-list-grid__item">
                {% include "@team-tile" with {
                    profileLink: addOnEntry.fullProfileLinks
                } %}
            </div>

            {% endfor %}
        </div>

    {% endblock %}
    {% endembed %}

{% else %}

    {% if craft.app.config.general.devMode and craft.app.config.general.allowAdminChanges %}
    <pre>No Active Team Members</pre>
    {% endif %}

{% endif %}
