<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-global.twig" as globalMacros %}
{% import "01_core/_macros/macros-assets.twig" as assetMacros %}

{% set panelSettings = block.panelSettings %}
{% set uniqueSliderId = random() %}
{% set layout = panelSettings.layout %}
{% set imagePreferences = block.imagePreferences %}

{% macro copy( block ) %}
    {% if block.copy %}
        <div class="content-block__copy">
            {{ globalMacros.selfAnchorSupport( block.copy ) }}
        </div>
    {% endif %}
{% endmacro %}

<div class="
    {{ modifierClass ?? null }}
    content-block
    content-block-valign--{{ panelSettings.alignment }}
    {{ layout|cc2kc }}
    {{ panelSettings.mobileOrder|cc2kc }}
    {{ panelSettings.gap ? 'content-block--gap' : 'content-block--no-gap' }}
    {{ panelSettings.background ? 'bg-' ~ panelSettings.background }}
    {{ panelSettings.background and not sidebarEnabled ? ' background--bleed' }} {# only bleed the background of a panel, if the template doesn't have a sidebar #}
    {{ not sidebarEnabled ? panelSettings.width }}" {# only let a user pick a width, if the template doesn't have a sidebar #}
>

    {% if layout != 'fullWidthMedia' and panelSettings.mobileOrder == 'copyFirst' %}
        {{ _self.copy( block ) }}
    {% endif %}

    {% set mediaImages = block.imageList.all() ?? null %}
    {% set form = block.form.one() ?? null %}
    {% set video = block.video.one() ?? null %}
    {% set embedWidget = block.embedWidget ?? null %}

    {% if layout != 'fullWidthCopy' and (mediaImages ?? false or form ?? false or video ?? false or embedWidget ?? false) %}
        <div class="content-block__media">

            {% if mediaImages ?? false %}
                {% include '02_components/imageList' with {
                    captions: imagePreferences.showCaptions ?? false,
                    imageRatio: imagePreferences.ratio ?? false,
                    slider: imagePreferences.displaySlider,
                    columns: imagePreferences.columns,
                    enablePopup: imagePreferences.enablePopup,
                } %}
            {% endif %}

            {% if form ?? false %}
                {% include '02_components/form' with { selectedForm: form } %}
            {% endif %}

            {% if video ?? false %}
                {% include '02_components/video' with { video: video } %}
            {% endif %}

            {% if embedWidget ?? false %}
                <div class="widget-embed">
                    {{ embedWidget|raw }}
                </div>
            {% endif %}

        </div>
    {% endif %}

    {% if layout != 'fullWidthMedia' and panelSettings.mobileOrder == 'mediaFirst' %}
        {{ _self.copy( block ) }}
    {% endif %}

</div>
