{% extends "01_core/main" %}

{% import "01_core/_macros/macros-navigation" as navMacros %}

{%- set bodyAttributes = { class: entry ?? false ? entry.getSection|lower|kebab : '404'} -%}
{%- set siteIdentity = craft.entries.section('siteIdentity').type('siteIdentity').one() -%}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
    {{ parent() }}
{% endblock %}


{% block main_content %}

    {% embed "01_core/_layouts/page-layout.twig" %}

        {% block main_bar %}

            {% if entry is not defined %}

                {% if siteIdentity.richtext %}
                    {{ siteIdentity.richtext }}
                {% else %}
                    <h2>Page not found</h2>

                    <p>Unfortunately, the page you are looking for could not be found.</p>

                    <p>If you <a href="/">visit the homepage</a>, hopefully you'll be able to browse to find what you need.</p>

                {% endif %}
            {% endif %}

            {% if entry ?? false %}
                {% include '01_core/_layouts/page-content' %}
            {% endif %}

        {% endblock %}

    {% endembed %}

{% endblock %}

