{% extends 'pages/page' %}

{%- set bodyAttributes = { class: 'sitemap'} -%}

{% block top_bar %}
    <!-- Template: {{ _self }} -->
    {{ parent() }}
{% endblock %}

{% block main_content %}

    {% embed "01_core/_layouts/page-layout.twig" with { layout: 'page-layout--center' } %}

    {% block main_bar %}

        {# --------------------------------------
            Template macros
        -------------------------------------- #}
        {% macro channelAll(pageLevel, section) %}
            <p class="sitemap__sublist-title">All {{ section|title }}</p>
            <ul class="sitemap__list sitemap__list--depth{{ pageLevel }}">
                {%- for channelItem in craft.entries.section(section).navigationVisibility('sitemap').orderBy('title ASC').all() -%}
                    <li class="sitemap__item sitemap__item--depth{{ pageLevel }}">
                        <a href="{{ channelItem.url }}">{{ channelItem.navigationTitle ?? channelItem.title }}</a>
                    </li>
                {%- endfor -%}
            </ul>
        {% endmacro %}

        {% macro channelByGrouping(pageLevel, section, groupingTitle, groupingQuery) %}
            <p class="sitemap__sublist-title">{{ section|title }} by {{ groupingTitle }}</p>
            <ul class="sitemap__list sitemap__list--depth{{ pageLevel }}">
                {%- set channelUrl = siteUrl ~ section -%}
                {%- for group, entriesInGroup in craft.entries.section(section).navigationVisibility('sitemap').all()|group(groupingQuery) -%}
                    <li class="sitemap__item sitemap__item--depth{{ pageLevel }}">
                        <a href="{{ channelUrl }}/{{ group }}">{{ group }}</a>
                    </li>
                {%- endfor -%}
            </ul>
        {% endmacro %}

        {% macro channelByCategory(pageLevel, section) %}
            {% set categoryType = section|trim('s') ~ 'Category' %}
            {% set entriesWithCategory = craft.entries.section(section).(categoryType)(':notempty:').all() %}
            {% set channelCategories = craft.entries.section('categories').type(categoryType).navigationVisibility('sitemap').all()|unique %}
            {% if channelCategories ?? null %}
                <p class="sitemap__sublist-title">{{ section|title }} by category</p>
                <ul class="sitemap__list sitemap__list--depth{{ pageLevel }}">
                    {%- for category in channelCategories -%}
                        <li class="sitemap__item sitemap__item--depth{{ pageLevel }}">
                            <a href="{{ category.uri }}">{{ category }}</a>
                        </li>
                    {%- endfor -%}
                </ul>
            {% endif %}
        {% endmacro %}
        {# ----------------------------------- #}

        {% set entries = craft.entries.section('pages').navigationVisibility('sitemap').all() %}

        {% set collections = ['posts', 'teamMembers'] %}
        {% set parentPages = {} %}

        {% for collectionType in collections %}
            {% set parentPages = parentPages|merge({(collectionType): siteIdentity[collectionType ~ 'ParentPage'].one().id ?? null}) %}
        {% endfor %}

        <ul class="sitemap__list">
            {% nav page in entries %}
            <li class="sitemap__item sitemap__item--depth{{ page.level }}">
                <a href="{{ page.url }}">{{ page.navigationTitle ?? page.title }}</a>

                {% if page.id in parentPages|values %}
                    {% set collectionHandle = null %}

                    {% for key, value in parentPages %}
                         {% if page.id == value %}
                            {% set collectionHandle = key %}
                        {% endif %}
                    {% endfor %}

                    {% if collectionHandle ?? null %}

                        {% switch collectionHandle %}

                            {%- case "posts" -%}
                                {# posts by year #}
                                {{ _self.channelByGrouping((page.level + 1), "posts", "year", "postDate|date('Y')") }}

                                {# posts by category #}
                                {{ _self.channelByCategory((page.level + 1), "posts") }}

                            {% default %}
                                {# all #}
                                {{ _self.channelAll((page.level + 1), collectionHandle) }}

                        {% endswitch %}

                {% endif %}

                {% endif %}

                {% ifchildren %}
                    <ul class="sitemap__list sitemap__list--depth{{ page.level }}">
                        {% children %}
                  </ul>
                {% endifchildren %}

              </li>
            {% endnav %}
        </ul><!-- /.sitemap__list -->

    {% endblock %}

    {% endembed %}

{% endblock %}

