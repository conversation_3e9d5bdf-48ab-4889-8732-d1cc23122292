/// <reference types="vite/client" />
import { ModuleNamespace } from 'vite/types/hot.js';

/**
 * Sets up HMR for the module that imports this utility.
 * @param {Function} onUpdate - Callback function that handles the module update.
 */
export function setupHMR(onUpdate?: (newModule: ModuleNamespace) => void): void {
    if (import.meta.hot) {
        import.meta.hot.accept((newModule) => {
            if (newModule && onUpdate) {
                onUpdate(newModule);
            }
        });
    }
}

/**
 * Emits a vite-script-loaded event when the dev server is running to replicate production behaviour.
 * @param {string} url - The full URL of the script that has been loaded.
 */
export function emitScriptLoaded(url: string): void {
    if (import.meta.hot) {
        const parsedUrl = new URL(url);
        const pathAndFilename = parsedUrl.pathname.substring(1); // removing leading slash

        const e = new CustomEvent('vite-script-loaded', { detail: { path: pathAndFilename } });
        document.dispatchEvent(e);
    }
}
