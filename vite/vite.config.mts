import { defineConfig } from 'vite';
import liveReload from 'vite-plugin-live-reload';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import dotenv from 'dotenv';

import os from 'os';
import * as path from 'path';

dotenv.config();

const isSSLEnabled = process.env.SITES_SSL_ENABLED === 'true';

// https://vitejs.dev/config/
export default defineConfig(({ command }) => ({
	base: command === 'serve' ? '' : '/dist/',
	css: {
		devSourcemap: true,
	},
	build: {
		emptyOutDir: true,
		manifest: true,
		outDir: './web/dist/',
		rollupOptions: {
			input: {
				app: './vite/app.ts',
			},
			output: {
				// Configure the output of asset files:
				// * SCSS/CSS goes to web/dist/css/
				// * JS/TS goes to web/dist/js/
				// * Everything else goes to web/dist/assets/
				assetFileNames: (assetInfo) => {
					if (assetInfo.name?.endsWith('.css')) {
						return 'css/[name].[hash][extname]';
					}

				  	return 'assets/[name].[hash][extname]';
				},
				// Configure the output of JS files
				chunkFileNames: 'js/[name].[hash].js',
				entryFileNames: 'js/[name].[hash].js',
			},
		},
	},
	plugins: [
		liveReload(['./templates/**/*.twig', './fractal/_patterns/**/*.twig']),
		viteStaticCopy({
			targets: [
				{
					src: './node_modules/@splidejs/splide/dist/js/splide.min.js',
					dest: './js'
				},
				{
					src: './node_modules/@splidejs/splide/dist/js/splide-renderer.min.js',
					dest: './js'
				},
			]
		  }),
	],
	resolve: {
		alias: {
			'@root': path.resolve('./'),
			'@webroot': path.resolve('./web'),
		},
		preserveSymlinks: true,
	},
	server: {
		https: isSSLEnabled ? {
			key: path.join(os.homedir(), '.sites/compose/apache/keys', 'bunnysites.key'),
			cert: path.join(os.homedir(), '.sites/compose/apache/keys', 'bunnysites.crt')
		} : undefined,
		fs: {
			strict: false,
		},
		origin: '//localhost:5137',
		host: '0.0.0.0',
		port: 5137,
		strictPort: false,
		cors: true,
		headers: {
		  'Access-Control-Allow-Origin': '*',
		},
	},
}));
