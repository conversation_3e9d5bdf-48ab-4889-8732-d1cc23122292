# Vite ⚡ + Craft

Vite handles two separate functions for us:
1. A dev server that supports hot module replacement for `.scss`/`.ts`/`.js` files, as well as auto-refresh on `.twig` template changes. The Vite dev server uses esbuild for pre-bundling and code transforms.
2. A bundler for production builds when the Craft site is in production mode _or_ the Vite dev server is not running. This function is largely equivalent to `gulp build`. Behind the scenes, Vite uses Rollup for bundling.

Anything transformed by esbuild when running the dev server is **temporary** and only exists for as long as the dev server is running. For persistent, production bundles, you need to run Vite's build process to output .js/.css files.

## Quick start
Make sure you have all necessary dependencies with `npm ci`.

During development, in almost all cases you will want to use:
- `npm run dev:watch`

This command starts the Vite dev server _and_ makes Vite listen for changes to automatically update our production bundles. This way, when the dev server is not running, the production bundles will be 1:1 with the state of our project while using the dev server.

Alternatively, you can call `npm run dev` for the dev server and manually build production bundles with `npm run build` after completing your dev work. Doing it this way will be less CPU-intensive.

## How it works

The process for getting Vite to build production bundles and using these in Twig templates is as follows:
- Specify source file(s) to build in `vite/vite.config.ts`, in the `rollupOptions → input` object
- When running `vite build`, these are output to `web/dist`, inside `/js`, `/css`, or `/assets` children directories depending on the type
- In templates, using nystudio107's [Craft Vite Plugin](https://github.com/nystudio107/craft-plugin-vite), we generally use `{{ craft.vite.script("example.ts", false) }}`

The `craft.vite.script` function does a few magical things for us:
- When the Vite dev server is running, it will use esbuild bundles and support hot module replacement for any entrypoints that have opted-in (more on this later).
- When we are in production mode _or_ the dev server is not running, it will automatically reference our production bundles via `<script>` and `<link>` tags.

This means that we no longer use `{% js %}` or `{% css %}` Twig tags for anything served/built by Vite.

### .ts as our entrypoint
You may have noticed that we used a TypeScript file as the argument in: `{{ craft.vite.script("example.ts", false) }}`. Rather than supplying `craft.vite.script` with a `.scss` file, for example, most of the time it will be beneficial to create a new `.ts/.js` file and import what we need all in 1 file

```js
// app.ts example
// This file is used across all templates (main.twig)
import '/fractal/_patterns/base.scss';
import '/fractal/_patterns/components.scss';
import '/src/js/skin.js';
```

In this case, we import two different Sass files and our skin JavaScript. In production, this will produce 1 bundled .css file and 1 minified .js file to `web/dist`.

**Note:** Vite supports TypeScript out of the box, so using a `.ts` file as our entrypoint or as part of an import statement is perfectly fine. Given this, we may want to consider switching any existing JavaScript logic, such as `skin.js` to use TypeScript instead.

#### Opting in to hot module replacement (HMR)
Another benefit of using a TypeScript entrypoint rather than a single `.scss` file, for example, is that it allows us to opt-in to hot module replacement. Without opting in, changes to a `.scss` file, for example, would result in a full page refresh when using the Vite dev server.

Add this to any `.ts/.js` entrypoint file to enable hot module replacement:
```js
import { setupHMR } from '@root/vite/hmr';

setupHMR();
```

For production builds, this HMR function is tree-shaken and is not be included in production bundles.

## Module scripts and vite-script-loaded
Vite creates `<script>` tags with `type="module"`, and as a result, processing of the script contents **is deferred**. If you want to add `{% js %}` in a template that relies on code from a Vite-supplied bundle, the Vite plugin provides a custom event that we can listen to for each entrypoint:
```js
<script type="module" src="/dist/js/app.D9XEn6DV.js" crossorigin onload="e=new CustomEvent('vite-script-loaded', {detail:{path: 'vite/app.ts'}});document.dispatchEvent(e);"></script>
```

Rather than using a listener for `DOMContentLoaded`, instead we can listen for `vite-script-loaded`. If we need to, we can also check for the specific script loaded by checking the path key:
```js
{% js %}
    document.addEventListener('vite-script-loaded', function(event) {
        if (event.detail.path === 'vite/app.ts') {
            // Logic here following loading of main app entrypoint
        }
    });
{% endjs %}
```

## Helpful links
- [Vite CraftCMS Plugin docs](https://nystudio107.com/docs/vite/)
- [Vite guide/docs](https://vitejs.dev/guide/)
- [Rollup guide/docs](https://rollupjs.org/introduction/)


