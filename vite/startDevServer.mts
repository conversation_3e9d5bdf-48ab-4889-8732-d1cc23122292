import { spawn } from 'child_process';
import { createServer } from 'vite';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

// ASCII colors
const red = '\x1b[31m';
const blue = '\x1b[34m';
const reset = '\x1b[0m';

let serverConfig: { site: string, port: number | null } = {
    "site": fetchSiteCode(),
    "port": null,
}

let watchMode = false;

async function startViteServer() {
    const server = await createServer({
        configFile: "./vite/vite.config.mts",
    });

    await server.listen();

    server.printUrls();
    server.bindCLIShortcuts({ print: true });

    const port = server.config.server.port;

    if (!port) {
        throw new Error('Port not found in server config (was not dynamically allocated by the dev server?)');
    }

    serverConfig.port = port;

    fs.writeFileSync('./vite/server.config.json', JSON.stringify(serverConfig, null, 2));

    const args = process.argv.slice(2);
    if (args[0] === '--watch') {
        const packageJsonData = fs.readFileSync("./package.json", 'utf-8');
        const buildScript = JSON.parse(packageJsonData).scripts.build;
        const commands = buildScript.split(' && ');

        for (const command of commands) {
            const splitCommand = command.split(' ');
            splitCommand.push('--watch');

            const child = spawn('npx', splitCommand, { stdio: 'inherit' });

            child.on('close', (code: number) => {
                console.log(`${red}Vite build process ${blue}(${command})${red} exited${reset} with code ${code}`);
            });
        }

        watchMode = true;
    }
}

// Ties a particular site code to an open dev server port
function fetchSiteCode() {
    const siteCode = process.env.SITES_DB_DATABASE;

    if (!siteCode) {
        throw new Error(`Tried to identify site code via SITES_DB_DATABASE environment variable (.env file), but it was not set. Have you run ${blue}sites sync${reset}?`);
    }

    return siteCode;
}

let exiting = false;

async function shutdownCleanup() {
    // Prevent the function from being called more than once
    if (exiting) {
        return;
    }

    exiting = true;

    serverConfig.port = null;

    console.log(`\n${red}Shutting down server and performing clean-up...${reset}`);
    fs.writeFileSync('./vite/server.config.json', JSON.stringify(serverConfig, null, 2));

    if (!watchMode) {
        // Slight delay to draw attention to this message before exiting
        await new Promise(resolve => setTimeout(resolve, 200));
        console.log(`Run ${blue}npm run build${reset} if you want your changes to be reflected when this dev server isn't running!`)
    }
    process.exit(0);
}

[`exit`, `SIGINT`, `SIGUSR1`, `SIGUSR2`, `uncaughtException`, `SIGTERM`].forEach((eventType) => {
    process.on(eventType, shutdownCleanup);
});

startViteServer().catch((err) => {
    console.error(err);
    process.exit(1);
});
