import { defineConfig } from 'vite';
import * as path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({
	css: {
		devSourcemap: true,
	},
	build: {
		emptyOutDir: false,
		outDir: './fractal/_patterns/',
		rollupOptions: {
			input: {
				styleguide: './fractal/_patterns/styleguide.scss',
			},
			output: {
				assetFileNames: '[name][extname]',
			},
		},
	},
	resolve: {
		alias: {
			'@root': path.resolve('./')
		},
		preserveSymlinks: true,
	},
}));
