{
    "compilerOptions": {
        "baseUrl": ".",
        "module": "ESNext",
        "target": "ESNext",
        "moduleResolution": "bundler",
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "paths": {
            "@root/*": ["./*"]
        },
        "preserveSymlinks": true
    },
    "include": [
        "vite/**/*",
        "fractal/**/*",
        "templates/**/*",
    ],
    "exclude": [
        "node_modules",
    ],
}
